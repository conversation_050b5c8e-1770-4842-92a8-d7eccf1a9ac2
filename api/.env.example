DJANGO_SETTINGS_MODULE=project.settings.dev
SECRET_KEY={secret}
BASE_URL=http://localhost:8000
SITE_URL=http://localhost:3000

DB_USER={dbuser}
DB_NAME={dbname}
DB_PASS={dbpass}
DB_HOST={dbhost}

DATABASE_URL={dburl}

IS_FLY=False

CACHE_URL={cache-url}

COMMAND_TOKEN={command-token}
DEBUG_TOOLBAR={bool:display-debug-toolbar}
GOOGLE_APPLICATION_CREDENTIALS={path:google-credentials-json-file}
RATE_LIMIT_API=True

REDSYS_MERCHANT_NAME={str}
REDSYS_MERCHANT_CODE={str}
REDSYS_MERCHANT_TERMINAL={str}
REDSYS_SECRET_KEY={str}
REDSYS_PRODUCTION=False

SENDCLOUD_API_KEY={api-key}
SENDCLOUD_API_SECRET={api-secret}

FIXER_ACCESS_KEY={fixer-access-key}

SENDGRID_API_KEY={str}
EMAIL_HOST_USER={email-user}
