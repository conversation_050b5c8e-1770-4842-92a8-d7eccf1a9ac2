import csv
from io import String<PERSON>
from django import forms
from django.conf import settings
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.core.files.base import ContentFile
from django.http.response import HttpResponse
from django.shortcuts import get_object_or_404, redirect
from django.urls import path, re_path
from django.utils import timezone
from django.utils.html import linebreaks
from post_office import mail
from post_office.admin import CommaSeparatedEmailWidget
from post_office.models import Email, Log
from salesman.orders.models import Order
from salesman.orders.serializers import OrderSerializer
from wagtail.admin.edit_handlers import FieldPanel, MultiFieldPanel, ObjectList
from wagtail.contrib.modeladmin.options import (
    ModelAdmin,
    ModelAdminGroup,
    modeladmin_register,
)
from wagtail.contrib.modeladmin.views import CreateView, EditView

from catalog.reports import generate_analytics_report, generate_catalog_report
from extra.edit_handlers import CodeMirrorHTMLPanel, ReadOnlyDatePanel, ReadOnlyPanel
from shop.views.order_admin import render_orders_csv

from .models import EmailTemplate, NotificationTrigger, ReportSchedule
from .utils import get_email_template_context
from django.core.files.base import ContentFile
from io import BytesIO


class LogAdmin(ModelAdmin):
    model = Log
    menu_label = "Logs"
    menu_icon = "doc-empty"
    list_display = ["email", "date", "exception_type", "status"]


class EmailForm(forms.ModelForm):
    class Meta:
        model = Email
        exclude: list = []

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.from_email = settings.DEFAULT_FROM_EMAIL
        instance.save()
        self.save_m2m()
        return instance


class EmailAdmin(ModelAdmin):
    model = Email
    menu_label = "Emails"
    menu_icon = "mail"
    list_display = ["id", "admin_to", "admin_subject", "status", "created"]
    list_filter = ["status"]
    search_fields = ["to"]

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("to", widget=CommaSeparatedEmailWidget()),
                FieldPanel("cc", widget=CommaSeparatedEmailWidget()),
                FieldPanel("bcc", widget=CommaSeparatedEmailWidget()),
            ],
            heading="Recipients",
        ),
        MultiFieldPanel(
            [
                FieldPanel("subject"),
                CodeMirrorHTMLPanel("message"),
                CodeMirrorHTMLPanel("html_message"),
            ],
            heading="Content",
        ),
        MultiFieldPanel(
            [
                FieldPanel("status"),
                FieldPanel("priority"),
            ],
            heading="Status",
        ),
        MultiFieldPanel(
            [
                FieldPanel("scheduled_time"),
                FieldPanel("expires_at"),
                ReadOnlyDatePanel("created"),
            ],
            heading="Settings",
        ),
    ]

    edit_handler = ObjectList(panels, base_form_class=EmailForm)

    def admin_title(self, obj):
        return super().admin_title(obj)

    admin_title.short_description = "ID"  # type: ignore

    def admin_to(self, obj):
        return ", ".join(obj.to) or "-"

    admin_to.short_description = "To"  # type: ignore

    def admin_subject(self, obj):
        if obj.subject:
            return obj.subject
        if obj.template:
            return obj.template.subject
        return "-"

    admin_subject.short_description = "Subject"  # type: ignore


class SameURLEditView(EditView):
    def get_success_url(self):
        return self.edit_url


class EmailTemplateAdmin(ModelAdmin):
    model = EmailTemplate
    menu_label = "Templates"
    menu_icon = "doc-full-inverse"
    list_display = ["name", "subject", "created_at"]
    list_filter = ["locale"]
    search_fields = ["name"]
    edit_view_class = SameURLEditView
    edit_template_name = "notification/email_template_edit.html"
    title_indent_depth = True
    title_indent_root_depth = 1

    def preview_view(self, request, instance_pk):
        """
        Render email content preview.
        """
        instance = get_object_or_404(self.model, pk=instance_pk)
        Status = Order.get_statuses()
        queryset = Order.objects.exclude(status=Status.NEW).prefetch_related(
            *OrderSerializer.Meta.prefetched_fields
        )
        try:
            order = queryset.get(ref=request.GET["order"])
        except KeyError:
            order = queryset.first()
        except Order.DoesNotExist:
            order = None

        user = order.user if order else None
        context = get_email_template_context(request, user, order)
        is_html = bool(request.GET.get("html", 0))
        try:
            if bool(request.GET.get("subject", 0)):
                data = instance.render_subject(context)
            else:
                data = instance.render(context, html=is_html)
        except Exception as exc:
            data = repr(exc)
        if not is_html:
            data = linebreaks(data)
        return HttpResponse(data)

    def get_admin_urls_for_registration(self):
        urls = super().get_admin_urls_for_registration()
        urls += (
            re_path(
                self.url_helper.get_action_url_pattern("preview"),
                self.preview_view,
                name=self.url_helper.get_action_url_name("preview"),
            ),
        )
        return urls


class NotificationTriggerAdmin(ModelAdmin):
    model = NotificationTrigger
    menu_label = "Triggers"
    menu_icon = "repeat"
    list_display = ["get_trigger_display", "admin_email_template", "admin_recipient"]
    list_filter = ["trigger", "email_template"]
    search_fields = ["name", "email_template__name"]
    edit_view_class = SameURLEditView
    ordering = ["trigger"]

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("trigger"),
                FieldPanel("email_template"),
                ReadOnlyPanel(
                    "admin_context_vars",
                    heading="Variables",
                    help_text="Template variables available in subject and contents.",
                ),
            ],
            heading="Config",
        ),
        MultiFieldPanel(
            [
                FieldPanel("recipient"),
                FieldPanel("recipient_email"),
            ],
            heading="Recipient",
        ),
        MultiFieldPanel(
            [
                ReadOnlyDatePanel("created_at"),
                ReadOnlyDatePanel("updated_at"),
            ],
            heading="Timestamps",
        ),
    ]

    def admin_title(self, obj):
        return super().admin_title(obj)

    admin_title.short_description = "Trigger"  # type: ignore

    def admin_email_template(self, obj):
        return obj.email_template.name

    admin_email_template.short_description = "Template"  # type: ignore

    def admin_recipient(self, obj):
        return obj.recipient_email or obj.recipient

    admin_recipient.short_description = "Recipient"  # type: ignore


class NotificationsGroup(ModelAdminGroup):
    menu_label = "Notifications"
    menu_order = 800
    menu_icon = "mail"
    items = (EmailAdmin, EmailTemplateAdmin, NotificationTriggerAdmin, LogAdmin)


modeladmin_register(NotificationsGroup)


class ReportScheduleAdmin(ModelAdmin):
    model = ReportSchedule
    menu_label = "Report Schedules"
    menu_icon = "date"
    list_display = (
        "report_type",
        "day_of_month",
        "recipients",
        "is_active",
        "last_sent",
    )
    list_filter = ("report_type", "is_active")
    search_fields = ("recipients",)

    # Specify the template names
    index_template_name = "notification/report_schedule_index.html"
    edit_template_name = "notification/report_schedule_edit.html"

    def get_admin_urls_for_registration(self):
        urls = super().get_admin_urls_for_registration()

        # Custom send now URL with a consistent naming pattern
        urls += (
            path(
                f'{self.url_helper.get_action_url_pattern("index")}send-now/<int:instance_pk>/',
                self.send_now_view,
                name="reportschedule_modeladmin_send_now",
            ),
        )
        return urls

    def send_now_view(self, request, instance_pk):
        import logging

        logger = logging.getLogger(__name__)

        try:
            schedule = self.model.objects.get(pk=instance_pk)
            end_date = timezone.now()
            start_date = end_date - timezone.timedelta(days=schedule.duration_days)

            request.method = "POST"
            request.POST = {
                "datetime_from": start_date.strftime("%Y-%m-%d %H:%M"),
                "datetime_to": end_date.strftime("%Y-%m-%d %H:%M"),
            }

            report_content = None
            if schedule.report_type == ReportSchedule.ReportType.CATALOG:
                response = generate_catalog_report(request)
                report_content = response.content
                report_name = "Catalog"
            elif schedule.report_type == ReportSchedule.ReportType.ANALYTICS:
                response = generate_analytics_report(request)
                report_content = response.content
                report_name = "Analytics"
            elif schedule.report_type == ReportSchedule.ReportType.ORDERS:
                from django.db import connection

                # Get only paid orders
                qs = Order.objects.exclude(
                    status=Order.get_statuses().NEW
                ).prefetch_related("items", "payments")

                # Filter for paid orders
                paid_order_ids = [order.id for order in qs if order.is_paid]
                qs = qs.filter(id__in=paid_order_ids)

                output = StringIO()
                first_chunk = True
                chunk_size = 100

                for i in range(0, qs.count(), chunk_size):
                    chunk = list(qs[i : i + chunk_size])
                    if first_chunk:
                        output.write(render_orders_csv(request, chunk))
                        first_chunk = False
                    else:
                        output.write(
                            render_orders_csv(request, chunk, write_headers=False)
                        )
                    connection.queries_log.clear()

                report_content = output.getvalue().encode("utf-8")
                output.close()
                report_name = "Paid Orders"

            if not report_content:
                raise ValueError("No report content generated")

            # Create a proper file-like object for the attachment
            content_file = ContentFile(report_content)

            # Prepare email
            recipients = [email.strip() for email in schedule.recipients.split(",")]
            email_subject = (
                f'{report_name} Report - {timezone.now().strftime("%Y-%m-%d")} (Manual)'
            )
            report_filename = f"{report_name.lower()}_report_{timezone.now().strftime('%Y-%m-%d')}.csv"

            mail.send(
                recipients=recipients,
                subject=email_subject,
                message=f"Please find attached the {report_name} report.",
                html_message=f"<p>Please find attached the {report_name} report.</p>",
                attachments={report_filename: content_file},
            )

            schedule.mark_sent()
            messages.success(
                request, f"Report sent successfully to {schedule.recipients}"
            )

        except Exception as e:
            logger.exception("Error sending report")
            messages.error(request, f"Failed to send report: {str(e)}")

        return redirect(self.url_helper.get_action_url("edit", instance_pk))

    # Panels for the admin interface
    panels = [
        MultiFieldPanel(
            [
                FieldPanel("report_type"),
                FieldPanel("day_of_month"),
                FieldPanel("duration_days"),
                FieldPanel("recipients"),
                FieldPanel("is_active"),
            ],
            heading="Schedule Settings",
        ),
        MultiFieldPanel(
            [
                ReadOnlyDatePanel("last_sent"),
                ReadOnlyDatePanel("created_at"),
                ReadOnlyDatePanel("updated_at"),
            ],
            heading="Report Timestamps",
        ),
    ]


# Register the admin class
modeladmin_register(ReportScheduleAdmin)
