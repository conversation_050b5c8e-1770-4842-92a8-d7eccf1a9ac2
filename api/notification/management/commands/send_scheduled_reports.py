import csv
from io import StringIO
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.http import HttpRequest, HttpResponse
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.conf import settings
from post_office import mail
from salesman.orders.models import Order

from catalog.reports import generate_analytics_report, generate_catalog_report
from notification.models import ReportSchedule
from shop.views.order_admin import render_orders_csv

import logging
import traceback

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Send scheduled reports"

    def handle(self, *args, **options):
        logging.basicConfig(level=logging.DEBUG)
        schedules = ReportSchedule.objects.filter(is_active=True)

        for schedule in schedules:
            if not schedule.should_send_today():
                continue

            try:
                logger.debug(f"Processing report schedule: {schedule}")
                logger.debug(f"Report type: {schedule.report_type}")
                logger.debug(f"Recipients: {schedule.recipients}")

                # Create mock request with explicit date range and required attributes
                request = HttpRequest()
                request.method = "POST"
                start_date, end_date = schedule.get_date_range()
                request.POST = {
                    "datetime_from": start_date.strftime("%Y-%m-%d %H:%M"),
                    "datetime_to": end_date.strftime("%Y-%m-%d %H:%M"),
                }

                # Add required attributes for report generation
                User = get_user_model()
                try:
                    # Try to get a superuser for analytics reports
                    request.user = User.objects.filter(is_superuser=True).first()
                    if not request.user:
                        # Create a mock superuser object if none exists
                        request.user = type(
                            "MockUser",
                            (),
                            {
                                "is_superuser": True,
                                "is_authenticated": True,
                                "is_staff": True,
                            },
                        )()
                except Exception:
                    # Fallback mock user
                    request.user = type(
                        "MockUser",
                        (),
                        {
                            "is_superuser": True,
                            "is_authenticated": True,
                            "is_staff": True,
                        },
                    )()

                # Set language code for translation
                request.LANGUAGE_CODE = getattr(settings, "LANGUAGE_CODE", "en")

                # Add session and messages attributes to prevent middleware errors
                request.session = {}
                request._messages = []

                # Generate report
                if schedule.report_type == ReportSchedule.ReportType.CATALOG:
                    response = generate_catalog_report(request)
                    report_name = "Catalog"
                elif schedule.report_type == ReportSchedule.ReportType.ANALYTICS:
                    response = generate_analytics_report(request)
                    report_name = "Analytics"
                elif schedule.report_type == ReportSchedule.ReportType.ORDERS:
                    qs = Order.objects.exclude(
                        status=Order.get_statuses().NEW
                    ).prefetch_related("items", "payments")

                    # Filter for paid orders
                    paid_order_ids = [order.id for order in qs if order.is_paid]
                    qs = qs.filter(id__in=paid_order_ids)

                    logger.debug(f"Found {qs.count()} paid orders")

                    # Process in chunks
                    output = StringIO()
                    writer = csv.writer(output)
                    chunk_size = 100
                    first_chunk = True

                    for i in range(0, qs.count(), chunk_size):
                        chunk = list(qs[i : i + chunk_size])
                        if first_chunk:
                            report_content = render_orders_csv(request, chunk)
                            output.write(report_content)
                            first_chunk = False
                        else:
                            report_content = render_orders_csv(
                                request, chunk, write_headers=False
                            )
                            output.write(report_content)

                    response = HttpResponse(output.getvalue(), content_type="text/csv")
                    output.close()
                    report_name = "Paid Orders"
                else:
                    logger.error(f"Unsupported report type: {schedule.report_type}")
                    continue

                # Get report content
                report_content = response.content
                logger.debug(f"Report content length: {len(report_content)} bytes")

                if not report_content:
                    logger.error(f"{report_name} report generated with zero bytes")
                    continue

                # Send email
                recipients = [email.strip() for email in schedule.recipients.split(",")]
                logger.debug(f"Sending report to: {recipients}")

                mail.send(
                    recipients=recipients,
                    subject=f'{report_name} Report - {timezone.now().strftime("%Y-%m-%d")}',
                    message=f"Please find attached the {report_name} report for paid orders.",
                    html_message=f"<p>Please find attached the {report_name} report for paid orders.</p>",
                    attachments={
                        f"{report_name.lower()}_report_{timezone.now().strftime('%Y-%m-%d')}.csv": report_content
                    },
                )

                schedule.mark_sent()
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully sent {report_name} report to {schedule.recipients}"
                    )
                )

            except Exception as e:
                logger.error(f"Error processing report: {str(e)}")
                logger.error(traceback.format_exc())
                self.stdout.write(
                    self.style.ERROR(
                        f"Failed to send report {schedule.report_type}: {str(e)}"
                    )
                )
                continue
