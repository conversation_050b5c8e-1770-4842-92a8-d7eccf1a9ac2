# Generated by Django 3.2.25 on 2025-07-15 14:10

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0005_alter_notificationtrigger_trigger'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('catalog', 'Catalog Report'), ('analytics', 'Analytics Report'), ('orders', 'Orders Report')], help_text='Type of report to generate', max_length=20)),
                ('recipients', models.TextField(help_text='Comma separated email addresses')),
                ('day_of_month', models.IntegerField(help_text='Day of month to send report (1-31)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)])),
                ('duration_days', models.IntegerField(default=30, help_text='Number of days to include in report (max 30)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(30)])),
                ('is_active', models.BooleanField(default=True, help_text='Whether this schedule is active')),
                ('last_sent', models.DateTimeField(blank=True, help_text='Last time this report was sent', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AlterField(
            model_name='notificationtrigger',
            name='trigger',
            field=models.CharField(choices=[('', '---------'), ('NEW', 'Status changed to "New"'), ('CREATED', 'Status changed to "Created"'), ('HOLD', 'Status changed to "Hold"'), ('FAILED', 'Status changed to "Failed"'), ('CANCELLED', 'Status changed to "Cancelled"'), ('PROCESSING', 'Status changed to "Processing"'), ('SHIPPED', 'Status changed to "Shipped"'), ('COMPLETED', 'Status changed to "Completed"'), ('REFUNDED', 'Status changed to "Refunded"'), ('', '---------'), ('CUSTOMER_REGISTERED', 'Customer registered'), ('RESET_PASSWORD', 'Reset password requested'), ('SENDCLOUD_FAILED', 'Sendcloud parcel(s) failed'), ('ORDER_IN_PRODUCTION', 'Order in production ( 3 day trigger)'), ('BASKET_ABANDONED', 'Basket abandoned')], default='', help_text='Select trigger for this notification.', max_length=128),
        ),
    ]
