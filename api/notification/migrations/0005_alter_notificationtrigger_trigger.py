# Generated by Django 3.2.21 on 2024-07-10 13:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0004_alter_notificationtrigger_trigger'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notificationtrigger',
            name='trigger',
            field=models.CharField(choices=[('', '---------'), ('NEW', 'Status changed to "New"'), ('CREATED', 'Status changed to "Created"'), ('HOLD', 'Status changed to "Hold"'), ('FAILED', 'Status changed to "Failed"'), ('CANCELLED', 'Status changed to "Cancelled"'), ('PROCESSING', 'Status changed to "Processing"'), ('SHIPPED', 'Status changed to "Shipped"'), ('COMPLETED', 'Status changed to "Completed"'), ('REFUNDED', 'Status changed to "Refunded"'), ('', '---------'), ('CUSTOMER_REGISTERED', 'Customer registered'), ('RESET_PASSWORD', 'Reset password requested'), ('SENDCLOUD_FAILED', 'Sendcloud parcel(s) failed'), ('ORDER_IN_PRODUCTION', 'Order in production ( 3 day trigger)')], default='', help_text='Select trigger for this notification.', max_length=128),
        ),
    ]
