# Generated by Django 3.2.8 on 2021-10-14 12:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notificationtrigger',
            name='trigger',
            field=models.CharField(choices=[('', '---------'), ('NEW', 'Status changed to "New"'), ('CREATED', 'Status changed to "Created"'), ('HOLD', 'Status changed to "Hold"'), ('FAILED', 'Status changed to "Failed"'), ('CANCELLED', 'Status changed to "Cancelled"'), ('PROCESSING', 'Status changed to "Processing"'), ('SHIPPED', 'Status changed to "Shipped"'), ('COMPLETED', 'Status changed to "Completed"'), ('REFUNDED', 'Status changed to "Refunded"'), ('', '---------'), ('CUSTOMER_REGISTERED', 'Customer registered'), ('PASSWORD_RESET', 'Password reset requested')], default='', help_text='Select trigger for this notification.', max_length=128),
        ),
    ]
