# Generated by Django 3.2.8 on 2021-10-12 11:03

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields
import post_office.validators
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('wagtailcore', '0062_comment_models_and_pagesubscription'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('path', models.<PERSON>r<PERSON>ield(max_length=255, unique=True)),
                ('depth', models.PositiveIntegerField()),
                ('numchild', models.PositiveIntegerField(default=0)),
                ('translation_key', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('name', models.Char<PERSON>ield(help_text='e.g: "Welcome email"', max_length=255, verbose_name='Name')),
                ('subject', models.Char<PERSON>ield(blank=True, help_text='Depending on email trigger used to dispatch this template you will have access to different template variables in your subject and content. Subject and content fields are renedered using Django templates. https://docs.djangoproject.com/en/3.2/topics/templates/#the-django-template-language', max_length=255, validators=[post_office.validators.validate_template_syntax], verbose_name='Subject')),
                ('content', models.TextField(blank=True, validators=[post_office.validators.validate_template_syntax], verbose_name='Content')),
                ('html_content', models.TextField(blank=True, validators=[post_office.validators.validate_template_syntax], verbose_name='HTML content')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('_tree_data', models.JSONField(editable=False, null=True)),
                ('locale', models.ForeignKey(editable=False, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='wagtailcore.locale')),
                ('parent', models.ForeignKey(blank=True, help_text='Select a parent template, enables template extension in "Content" and "HTML content" fields using the {% block %} syntax.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children_set', to='notification.emailtemplate')),
            ],
            options={
                'abstract': False,
                'unique_together': {('translation_key', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='NotificationTrigger',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('trigger', models.CharField(choices=[('', '---------'), ('NEW', 'Status changed to "Nuevo"'), ('CREATED', 'Status changed to "Creado en"'), ('HOLD', 'Status changed to "Hold"'), ('FAILED', 'Status changed to "Failed"'), ('CANCELLED', 'Status changed to "Cancelado"'), ('PROCESSING', 'Status changed to "Processing"'), ('SHIPPED', 'Status changed to "Shipped"'), ('COMPLETED', 'Status changed to "Completed"'), ('REFUNDED', 'Status changed to "Refunded"'), ('', '---------'), ('CUSTOMER_REGISTERED', 'Customer registered'), ('PASSWORD_RESET', 'Password reset requested')], default='', help_text='Select trigger for this notification.', max_length=128)),
                ('recipient', models.CharField(blank=True, choices=[('CUSTOMER', 'Customer'), ('ADMINS', 'Admins')], help_text='To whom should this notification be sent? Leave empty to enter a custom email below.', max_length=128, verbose_name='Recipient')),
                ('recipient_email', models.EmailField(blank=True, help_text='Optionally specify a custom recipient email.', max_length=254, verbose_name='Recipient email')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('email_template', modelcluster.fields.ParentalKey(help_text='Select an email template to send with on this trigger.', on_delete=django.db.models.deletion.CASCADE, related_name='triggers', to='notification.emailtemplate', verbose_name='Email Template')),
            ],
            options={
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
    ]
