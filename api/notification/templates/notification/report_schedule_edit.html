{% extends "modeladmin/edit.html" %}
{% load i18n wagtailadmin_tags modeladmin_tags %}

{% block content %}
<header class="nice-header">
    <div class="row">
        <div class="left col">
            <div class="header-title">
                <h1 class="icon icon-date">
                    Report Schedules
                </h1>
                <span class="header-subtitle">Manage Automated Reports</span>
            </div>
        </div>
        <div class="right col">
            <div class="header-meta"></div>
        </div>
    </div>
</header>

<div class="nice-padding">
    {% if instance.pk %}
    <div style="margin-bottom: 20px;">
        <a href="{% url 'reportschedule_modeladmin_send_now' instance_pk=instance.pk %}"
            class="button button-secondary">
            Send Report Now
        </a>
    </div>
    {% endif %}

    <form action="{{ view.get_edit_url }}" method="POST" novalidate>
        {% csrf_token %}
        {{ edit_handler.render_form_content }}

        <footer class="footer">
            <ul>
                <li class="actions">
                    <div class="dropdown dropup dropdown-button match-width">
                        <button type="submit" class="button action-save button-longrunning"
                            data-clicked-text="{% trans 'Saving…' %}">
                            {% icon name="spinner" %}
                            <em>{% trans 'Save' %}</em>
                        </button>
                    </div>
                </li>
            </ul>
        </footer>
    </form>
</div>
{% endblock %}
