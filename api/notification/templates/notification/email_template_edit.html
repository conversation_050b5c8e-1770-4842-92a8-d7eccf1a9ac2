{% extends "modeladmin/edit.html" %}
{% load wagtailadmin_tags %}

{% block form_actions %}
  <div class="dropdown dropup dropdown-button match-width">
    <button type="submit" class="button action-save button-longrunning" data-clicked-text="Saving…">
      {% icon name="spinner" %}<em>Save</em>
    </button>

    <div class="dropdown-toggle">{% icon name="arrow-up" %}</div>
    <ul>
      {% if user_can_delete %}
        <li><a href="{{ view.delete_url }}" class="shortcut">Delete</a></li>
      {% endif %}
      <li><a href="{% url 'notification_emailtemplate_modeladmin_preview' instance.id %}?subject=1" target="_blank">Preview subject</a></li>
      <li><a href="{% url 'notification_emailtemplate_modeladmin_preview' instance.id %}" target="_blank">Preview content</a></li>
      <li><a href="{% url 'notification_emailtemplate_modeladmin_preview' instance.id %}?html=1" target="_blank">Preview HTML content</a></li>
    </ul>
  </div>
{% endblock %}
