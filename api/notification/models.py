from __future__ import annotations

from typing import Optional, Union

from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import models
from django.dispatch import receiver
from django.template import Context, Engine
from django.utils import timezone
from django.utils.safestring import mark_safe
from modelcluster.fields import <PERSON><PERSON><PERSON><PERSON>ey
from post_office import mail
from post_office.models import Email
from post_office.validators import validate_template_syntax
from salesman.conf import app_settings as salesman_settings
from salesman.orders.signals import status_changed
from treebeard.mp_tree import MP_Node
from wagtail.admin.edit_handlers import FieldPanel, InlinePanel, MultiFieldPanel
from wagtail.core.models import ClusterableModel, TranslatableMixin
from wagtailorderable.models import Orderable

from extra.edit_handlers import (
    CodeMirrorDjangoHTMLPanel,
    ReadOnlyDatePanel,
    ReadOnlyPanel,
)
from user.models import User

from .utils import get_email_template_context


class EmailTemplate(TranslatableMixin, MP_Node, ClusterableModel):
    name = models.CharField("Name", max_length=255, help_text='e.g: "Welcome email"')
    parent = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="children_set",
        help_text=(
            'Select a parent template, enables template extension in "Content" and '
            '"HTML content" fields using the {% block %} syntax.'
        ),
    )
    subject = models.CharField(
        "Subject",
        max_length=255,
        blank=True,
        validators=[validate_template_syntax],
        help_text=(
            "Depending on email trigger used to dispatch this template you will have "
            "access to different template variables in your subject and content. "
            "Subject and content fields are renedered using Django templates. "
            "https://docs.djangoproject.com/en/3.2/topics/templates/#the-django-template-language"  # noqa
        ),
    )
    content = models.TextField(
        "Content", blank=True, validators=[validate_template_syntax]
    )
    html_content = models.TextField(
        "HTML content", blank=True, validators=[validate_template_syntax]
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Stored ancestor tree data with content
    _tree_data = models.JSONField(null=True, editable=False)

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("name"),
                FieldPanel("parent"),
            ],
            heading="Info",
        ),
        MultiFieldPanel(
            [
                FieldPanel("subject"),
                CodeMirrorDjangoHTMLPanel("content"),
                CodeMirrorDjangoHTMLPanel("html_content"),
            ],
            heading="Content",
        ),
        MultiFieldPanel(
            [
                ReadOnlyDatePanel("created_at"),
                ReadOnlyDatePanel("updated_at"),
            ],
            heading="Timestamps",
        ),
        InlinePanel("triggers", heading="Triggers"),
    ]

    debug_panels = [
        ReadOnlyPanel("_tree_data", heading="Tree data"),
    ]

    if settings.DEBUG:
        panels += debug_panels

    _saving = False

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._init_parent_id = self.parent_id

    def __str__(self):
        indent_str = "-" * (self.depth - 1)
        return f"{indent_str} {self.name}".strip()

    def save(self, *args, **kwargs):
        if self._saving:
            self._tree_data = self.build_tree_data()
            super().save(*args, **kwargs)

            # Update descendants tree data
            objs = []
            for node in self.get_descendants():
                node._tree_data = node.build_tree_data()
                objs.append(node)
            EmailTemplate.objects.bulk_update(objs, ["_tree_data"])
        else:
            # Handle tree operations based on parent
            self._saving = True
            if not self.pk:
                if self.parent_id:
                    self.parent.add_child(instance=self)
                else:
                    self.add_root(instance=self)
            else:
                if self.parent_id != self._init_parent_id:
                    self.move(self.parent, pos="sorted-child")
                else:
                    self.save(*args, **kwargs)

    def build_tree_data(self):
        data = {}
        c = 0
        for parent in self.get_ancestors():
            c += 1
            key = self.build_parent_key(parent.id)
            html_key = self.build_parent_key(parent.id, html=True)
            data[key] = parent.get_content()
            data[html_key] = parent.get_content(html=True)
        return data

    def build_parent_key(self, id: Optional[int] = None, html: bool = False) -> str:
        pid = int(id or self.parent_id or 0)
        key = f"parent_{pid}"
        if html:
            key = f"{key}_html"
        return key

    def get_context(self, context: dict) -> dict:
        engine = Engine.get_default()
        context = context.copy()
        for key, value in self._tree_data.items():
            context[key] = engine.from_string(value)
        return context

    def get_content(self, html: bool = False) -> str:
        content = self.html_content if html else self.content
        if self.parent_id:
            content = "{% extends " + self.build_parent_key(html=html) + " %}" + content
        return content

    def prepare(self, context: dict) -> dict:
        return {
            "context": self.get_context(context),
            "subject": self.subject,
            "message": self.get_content(),
            "html_message": self.get_content(html=True),
        }

    def render(self, context: dict, html: bool = False) -> str:
        engine = Engine.get_default()
        data = self.prepare(context)
        template = engine.from_string(data["html_message" if html else "message"])
        return template.render(Context(data["context"]))

    def render_subject(self, context: dict) -> str:
        engine = Engine.get_default()
        template = engine.from_string(self.subject)
        return template.render(Context(context))


# Triggers type is a dict with keys as identifiers and values as tuple of
# Display label and a list of required context variables for the trigger.
_TRIGGERS = dict[str, tuple[str, list[str]]]

ORDER_TRIGGERS: _TRIGGERS = {
    k: (f'Status changed to "{v}"', ["request", "customer", "order"])
    for k, v in salesman_settings.SALESMAN_ORDER_STATUS.choices
}
OTHER_TRIGGERS: _TRIGGERS = {
    "CUSTOMER_REGISTERED": (
        "Customer registered",
        ["request", "customer", "return_url"],
    ),
    "RESET_PASSWORD": (
        "Reset password requested",
        ["request", "customer", "return_url"],
    ),
    "SENDCLOUD_FAILED": (
        "Sendcloud parcel(s) failed",
        ["admin_order_urls"],
    ),
    "ORDER_IN_PRODUCTION": (
        "Order in production ( 3 day trigger)",
        ["request", "customer", "order"],
    ),
    "BASKET_ABANDONED": (
        "Basket abandoned",
        ["customer", "basket"],
    ),
}
TRIGGERS = ORDER_TRIGGERS | OTHER_TRIGGERS


def get_trigger_choices(triggers: _TRIGGERS) -> tuple[tuple[str, str], ...]:
    return tuple((k, v[0]) for k, v in triggers.items())


class NotificationTrigger(Orderable):
    email_template = ParentalKey(
        EmailTemplate,
        on_delete=models.CASCADE,
        related_name="triggers",
        verbose_name="Email Template",
        help_text="Select an email template to send with on this trigger.",
    )

    TRIGGER_CHOICES = (
        ("", "---------"),
        *get_trigger_choices(ORDER_TRIGGERS),
        ("", "---------"),
        *get_trigger_choices(OTHER_TRIGGERS),
    )

    trigger = models.CharField(
        max_length=128,
        choices=TRIGGER_CHOICES,
        default="",
        help_text="Select trigger for this notification.",
    )

    class Recipient(models.TextChoices):
        CUSTOMER = "CUSTOMER"
        ADMINS = "ADMINS"

    recipient = models.CharField(
        "Recipient",
        max_length=128,
        choices=Recipient.choices,
        blank=True,
        help_text=(
            "To whom should this notification be sent? "
            "Leave empty to enter a custom email below."
        ),
    )

    recipient_email = models.EmailField(
        "Recipient email",
        blank=True,
        help_text="Optionally specify a custom recipient email.",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("trigger"),
        ReadOnlyPanel(
            "admin_context_vars",
            heading="Variables",
            help_text="Template variables available in subject and contents.",
        ),
        FieldPanel("recipient"),
        FieldPanel("recipient_email"),
    ]

    def __str__(self):
        return f"{self.get_trigger_display()} -> {self.email_template.name}"

    def clean(self):
        super().clean()
        if not self.recipient and not self.recipient_email:
            raise ValidationError(
                {"recipient": 'Either "Recipient" or "Recipient email" must be set.'}
            )

    def get_recipients(self, context: dict) -> list[str]:
        data = []
        if self.recipient_email:
            data = [self.recipient_email]
        elif self.recipient == self.Recipient.ADMINS:
            data = [x[0] for x in settings.MANAGERS]
        elif self.recipient == self.Recipient.CUSTOMER:
            customer = context.get("customer", None)
            if not customer:
                raise ValueError("Missing `customer` in trigger context.")
            email = None
            if isinstance(customer, User):
                email = customer.email
            elif isinstance(customer, dict) and "email" in customer:
                email = customer["email"]
            if email:
                data = [email]
        return data

    def dispatch(self, context: dict, commit: bool = True) -> Union[Email, dict]:
        for var in self.context_vars:
            if var not in context:
                raise ValueError(f"Trigger context missing `{var}` variable.")
        data = self.email_template.prepare(context)
        data["recipients"] = self.get_recipients(context)
        if commit:
            return mail.send(**data)
        return data

    @property
    def context_vars(self):
        if self.trigger:
            return TRIGGERS.get(self.trigger)[1]

    @property
    def admin_context_vars(self):
        if not self.context_vars:
            return mark_safe("<i>Save to display trigger variables.</i>")
        body = ""
        for var in self.context_vars:
            body += f"<code>{var}</code> "
        return mark_safe(body) or "-"

    @classmethod
    def dispatch_triggers(cls, trigger: str, context: dict) -> int:
        if not trigger or trigger not in dict(cls.TRIGGER_CHOICES):
            raise ValueError(f"Invalid trigger `{trigger}`")

        data_list = []
        for obj in cls.objects.filter(trigger=trigger).select_related("email_template"):
            data = obj.dispatch(context, commit=False)
            data_list.append(data)

        count = len(data_list)
        if count == 1:
            mail.send(**data_list[0])
        elif count > 1:
            mail.send_many(data_list)
        return count


def get_context_and_notify_status_changed(order, new_status):
    """
    Helper method that builds context and triggers notifications for order status.
    """
    context = get_email_template_context(customer=order.user, order=order)
    NotificationTrigger.dispatch_triggers(new_status, context)


@receiver(status_changed, dispatch_uid="notify_status_changed")
def notify_status_changed(sender, order, new_status, old_status, **kwargs):
    get_context_and_notify_status_changed(order, new_status)


class ReportSchedule(models.Model):
    class ReportType(models.TextChoices):
        CATALOG = "catalog", "Catalog Report"
        ANALYTICS = "analytics", "Analytics Report"
        ORDERS = "orders", "Orders Report"

    report_type = models.CharField(
        max_length=20,
        choices=ReportType.choices,
        help_text="Type of report to generate",
    )

    recipients = models.TextField(help_text="Comma separated email addresses")

    day_of_month = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(31)],
        help_text="Day of month to send report (1-31)",
    )

    duration_days = models.IntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(30)],
        help_text="Number of days to include in report (max 30)",
    )

    is_active = models.BooleanField(
        default=True, help_text="Whether this schedule is active"
    )

    last_sent = models.DateTimeField(
        null=True, blank=True, help_text="Last time this report was sent"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_report_type_display()} - Day {self.day_of_month}"

    def should_send_today(self) -> bool:
        """Check if report should be sent today"""
        today = timezone.now()
        return today.day == self.day_of_month

    def get_date_range(self):
        """Get date range for report"""
        today = timezone.now()
        end_date = today
        start_date = end_date - timezone.timedelta(days=self.duration_days)
        return start_date, end_date

    def mark_sent(self):
        """Mark report as sent"""
        self.last_sent = timezone.now()
        self.save(update_fields=["last_sent"])
