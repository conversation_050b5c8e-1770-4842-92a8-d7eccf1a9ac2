from typing import Optional

from django.http.request import HttpRequest
from salesman.orders.models import Order

from core.serializers import serializer_to_dict
from user.models import User


def get_email_template_context(
    request: Optional[HttpRequest] = None,
    customer: Optional[User] = None,
    order: Optional[Order] = None,
    **kwargs,
) -> dict:
    """
    Returns context for rendering order instance in an email template.
    """
    context = {"request": request}

    if customer:
        from user.serializers import CustomerSerializer

        context["customer"] = serializer_to_dict(
            CustomerSerializer(customer, context=context)
        )
    if order:
        from shop.serializers.order import OrderEmailSerializer

        context["order"] = serializer_to_dict(
            OrderEmailSerializer(order, context=context)
        )

    if not customer and order:
        context["customer"] = {"email": order.email}

    if kwargs:
        context.update(kwargs)
    return context
