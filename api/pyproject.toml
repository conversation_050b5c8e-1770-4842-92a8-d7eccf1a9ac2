[tool.poetry]
name = "rubiuscorp"
version = "1.0.0"
description = ""
authors = ["BrightDock Dev Team <<EMAIL>>"]

[tool.poetry.dependencies]
python = "~3.9.0"
importlib_metadata = "*"

grpcio = "^1.37.1"
gunicorn = "~20.1.0"
gevent = "~21.12.0"
psycopg2 = "^2.8.6"
psycogreen = "~1.0.2"
PyYAML = "~6.0"
msgpack = "*"
uritemplate = "~3.0.1"
google-cloud-secret-manager = "~2.6.0"
google-cloud-logging = "~2.7.0"
schedule = "~1.1.0"
Pygments = "~2.10.0"
python-redsys = "~1.1.0"
requests = "~2.26.0"
pdfkit = "~0.6.1"

Django = "~3.2.7"
django-environ = "~0.7.0"
djangorestframework = "~3.12.4"
djangorestframework-simplejwt = "~5.0.0"
django-cors-headers = "~3.7.0"
django-storages = {extras = ["google"], version = "~1.11.1"}
django-debug-toolbar = "~3.2.2"
Collectfast = "~2.2.0"
django-salesman = "~1.0.5"
django-money = {extras = ["exchange"], version = "~2.1.0"}
django-post-office = {extras = ["prevent-XSS"], version = "~3.5.3"}
django-codemirror-widget = "~0.5.0"
django-treebeard = "~4.5.1"
django-modeltranslation = "~0.17.3"
django-sendgrid-v5 = "~1.1.1"
django-redis = "~5.0.0"

wagtail = "~2.15.0"
wagtail-localize = "~1.0.0"
wagtail-orderable = "~1.0.3"
wagtail-generic-chooser = "~0.3.0"

wagtail-2fa = "1.6.5"

# tests
coverage = {extras = ["toml"], version = "^6.0", optional = true}
pytest-django = {version = "^4.4.0", optional = true}
pytest-cov = {version = "^3.0.0", optional = true}

# loadtest
locust = {version = "^2.5.0", optional = true}
dj-database-url = "^2.1.0"
environ = "^1.0"
sentry-sdk = {extras = ["django"], version = "^2.15.0"}
pytest = "^8.4.1"

[tool.poetry.group.dev.dependencies]
black = "*"
isort = "~5.9.3"
mypy = "~0.910"
flake8 = "~3.9.2"
flake8-bugbear = "~21.4.3"
django-querycount = "~0.7.0"

[tool.poetry.extras]
tests = ["coverage", "pytest", "pytest-django", "pytest-cov"]
loadtest = ["locust"]

[tool.black]
exclude = '/(\.git|\.venv|migrations)/'

[tool.isort]
profile = "black"
skip = ".git,.venv,migrations"

[tool.mypy]
ignore_missing_imports = true
exclude = ".git|.venv|migrations"

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "project.settings.test"
python_files = ["tests.py", "test_*.py"]
filterwarnings = ["ignore::PendingDeprecationWarning", "ignore::DeprecationWarning"]
addopts = "--cov=."

[tool.coverage.run]
omit = [
    ".venv/*",
    "*/migrations/*",
    "*/tests/*",
    "*/tests.py",
    "*/test_*.py",
    "manage.py",
    "project/*",
    "gunicorn_config.py",
    "worker.py"
]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
