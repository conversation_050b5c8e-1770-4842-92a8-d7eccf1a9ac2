clean:
	rm -rf dist *.egg-info .pytest_cache htmlcov .coverage coverage.xml .DS_Store
	find . -name "*.pyc" -exec rm -f {} \;

check:
	poetry run isort . --check
	poetry run black --check .
	poetry run flake8 .
	poetry run mypy .

test:
	poetry run pytest --reuse-db --cov-report=html

runserver:
	poetry run gunicorn --reload -c python:gunicorn_config project.wsgi:application

runworker:
	poetry run python -m worker

runloadtest:
	poetry run locust --host http://localhost:8000 -f loadtest.locustfile.py

setup-discount-tests:
	poetry run python setup_discount_tests.py
