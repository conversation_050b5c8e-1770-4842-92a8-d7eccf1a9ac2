# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-05 09:11+0200\n"
"PO-Revision-Date: 2022-03-17 14:34+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0\n"

#: catalog/serializers.py:140
msgctxt "Product"
msgid "Delivery estimated in 1-5 business days."
msgstr "Entrega estimada en 1-5 días hábiles."

#: catalog/serializers.py:142
msgctxt "Product"
msgid "Details"
msgstr "Detalles"

#: catalog/serializers.py:143
msgctxt "Product"
msgid "Product code"
msgstr "Código de producto"

#: catalog/serializers.py:144
msgctxt "Product"
msgid "Shop similar"
msgstr "Comprar similar"

#: home/serializers.py:32
msgid "Account"
msgstr "Cuenta"

#: home/serializers.py:33
msgid "Cart"
msgstr "Carrito"

#: home/serializers.py:34
msgid "Log in / Register"
msgstr "Ingresar / Registrarse"

#: home/serializers.py:35
msgid "Currency"
msgstr "Divisa"

#: shop/modifiers.py:40
msgctxt "Basket"
msgid "This product is available."
msgstr "Este producto está disponible."

#: shop/modifiers.py:46
msgctxt "Basket"
msgid "This product is no longer available."
msgstr "Este producto ya no esta disponible."

#: shop/modifiers.py:49
#, python-format
msgctxt "Basket"
msgid "Only %d of this item is available."
msgstr "Solo %d de este artículo están disponibles."

#: shop/modifiers.py:175
msgid "Tax"
msgstr "Impuesto"

#: shop/payment/base.py:21
msgctxt "Checkout"
msgid "Some products are not available."
msgstr "Algunos productos no están disponibles."

#: shop/payment/base.py:30
msgctxt "Checkout"
msgid "Some products are not available for shipping outside Spain."
msgstr "Algunos productos no están disponibles para envío fuera de España."

#: shop/payment/base.py:40
#, fuzzy
#| msgctxt "Checkout"
#| msgid "Some products are not available for shipping outside Spain."
msgctxt "Checkout"
msgid "Some products are not available for shipping to your Region"
msgstr "Algunos productos no están disponibles para envío a su región"

#: shop/serializers/address.py:26 shop/validators.py:20
msgctxt "Country"
msgid "Invalid country provided."
msgstr "País no válido proporcionado."

#: shop/serializers/address.py:32
msgctxt "Country"
msgid "Invalid Canada province provided."
msgstr "Provincia de Canadá no válida proporcionada."

#: shop/serializers/address.py:36
msgctxt "Country"
msgid "Invalid Italy province provided."
msgstr "Provincia de Italia no válida proporcionada."

#: shop/serializers/address.py:40
msgctxt "Country"
msgid "Invalid US state provided."
msgstr "Estado de EE. UU. no válido proporcionado."

#: shop/status.py:6
msgctxt "Status"
msgid "New"
msgstr "Nuevo"

#: shop/status.py:7
msgctxt "Status"
msgid "Created"
msgstr "Creado"

#: shop/status.py:8
msgctxt "Status"
msgid "Hold"
msgstr "En Espera"

#: shop/status.py:9
msgctxt "Status"
msgid "Failed"
msgstr "Fallida"

#: shop/status.py:10
msgctxt "Status"
msgid "Cancelled"
msgstr "Cancelada"

#: shop/status.py:11
msgctxt "Status"
msgid "Processing"
msgstr "Procesando"

#: shop/status.py:12
msgctxt "Status"
msgid "Shipped"
msgstr "Enviada"

#: shop/status.py:13
msgctxt "Status"
msgid "Completed"
msgstr "Terminada"

#: shop/status.py:14
msgctxt "Status"
msgid "Refunded"
msgstr "Reintegrada"

#: shop/templates/shop/order_print.html:6
#: shop/templates/shop/order_print.html:108
msgctxt "Invoice"
msgid "Invoice"
msgstr "Factura"

#: shop/templates/shop/order_print.html:111 shop/views/order_admin.py:104
msgctxt "Invoice"
msgid "Order ID"
msgstr "Número de factura"

#: shop/templates/shop/order_print.html:112 shop/views/order_admin.py:106
msgctxt "Invoice"
msgid "Issue date"
msgstr "Fecha de asunto"

#: shop/templates/shop/order_print.html:121 shop/views/order_admin.py:107
msgctxt "Invoice"
msgid "Supplier"
msgstr "Proveedor"

#: shop/templates/shop/order_print.html:132
msgctxt "Invoice"
msgid "Shipping address"
msgstr "Dirección de envío"

#: shop/templates/shop/order_print.html:158
msgctxt "Invoice"
msgid "Billing address"
msgstr "Dirección de facturación"

#: shop/templates/shop/order_print.html:190 shop/views/order_admin.py:117
msgctxt "Invoice"
msgid "Article"
msgstr "Artículo"

#: shop/templates/shop/order_print.html:191 shop/views/order_admin.py:119
msgctxt "Invoice"
msgid "Unit price"
msgstr "Precio unitario"

#: shop/templates/shop/order_print.html:192 shop/views/order_admin.py:116
msgctxt "Invoice"
msgid "Quantity"
msgstr "Cantidad"

#: shop/templates/shop/order_print.html:193
#: shop/templates/shop/order_print.html:231 shop/views/order_admin.py:120
msgctxt "Invoice"
msgid "Subtotal"
msgstr "Precio neto"

#: shop/templates/shop/order_print.html:194
#: shop/templates/shop/order_print.html:195
#: shop/templates/shop/order_print.html:238 shop/views/order_admin.py:121
msgctxt "Invoice"
msgid "Tax"
msgstr "Impuesto"

#: shop/templates/shop/order_print.html:196
#: shop/templates/shop/order_print.html:245 shop/views/order_admin.py:123
msgctxt "Invoice"
msgid "Total"
msgstr "Total"

#: shop/templates/shop/order_print.html:219
msgctxt "Invoice"
msgid "Shipping method"
msgstr "Método de envío"

#: shop/templates/shop/order_print.html:224
msgctxt "Invoice"
msgid "Country of origin"
msgstr "País de origen"

#: shop/templates/shop/order_print.html:237 shop/views/order_admin.py:122
msgctxt "Invoice"
msgid "Shipping"
msgstr "Envío"

#: shop/templates/shop/order_print.html:257
msgctxt "Invoice"
msgid "Paid out"
msgstr "Pagado"

#: shop/templates/shop/order_print.html:260
msgctxt "Invoice"
msgid "To pay"
msgstr "Pagar"

#: shop/utils.py:63
msgctxt "Product"
msgid "Product is not available."
msgstr "El producto no está disponible."

#: shop/utils.py:70
msgctxt "Product"
msgid "Product is not published."
msgstr "El producto no se publica."

#: shop/validators.py:27
msgctxt "Postal code"
msgid "Postal code to long."
msgstr "Código postal a largo."

#: shop/views/order_admin.py:105
msgctxt "Invoice"
msgid "Internal reference"
msgstr "Referencia interna"

#: shop/views/order_admin.py:108
msgctxt "Invoice"
msgid "Full name"
msgstr "Nombre completo"

#: shop/views/order_admin.py:109
msgctxt "Invoice"
msgid "Address"
msgstr "Dirección"

#: shop/views/order_admin.py:110
msgctxt "Invoice"
msgid "Postal code"
msgstr "Código postal"

#: shop/views/order_admin.py:111
msgctxt "Invoice"
msgid "City"
msgstr "Ciudad"

#: shop/views/order_admin.py:112
msgctxt "Invoice"
msgid "State"
msgstr "Estado"

#: shop/views/order_admin.py:113
msgctxt "Invoice"
msgid "Country Code"
msgstr "Código de país"

#: shop/views/order_admin.py:114
msgctxt "Invoice"
msgid "Phone number"
msgstr "Número de teléfono"

#: shop/views/order_admin.py:115
#, fuzzy
#| msgctxt "Invoice"
#| msgid "Billing address"
msgctxt "Invoice"
msgid "Email address"
msgstr "Dirección de facturación"

#: shop/views/order_admin.py:118
msgctxt "Invoice"
msgid "SKU"
msgstr ""

#: shop/views/order_admin.py:124
msgctxt "Invoice"
msgid "Transaction ID"
msgstr "ID de Transacción"

#: user/management/commands/dump_customers_data.py:26
msgctxt "Customers CSV"
msgid "ID"
msgstr ""

#: user/management/commands/dump_customers_data.py:27
#, fuzzy
#| msgctxt "Invoice"
#| msgid "Billing address"
msgctxt "Customers CSV"
msgid "Email address"
msgstr "Dirección de facturación"

#: user/management/commands/dump_customers_data.py:28
#, fuzzy
#| msgctxt "Invoice"
#| msgid "Full name"
msgctxt "Customers CSV"
msgid "First name"
msgstr "Nombre completo"

#: user/management/commands/dump_customers_data.py:29
msgctxt "Customers CSV"
msgid "Last name"
msgstr ""

#: user/management/commands/dump_customers_data.py:30
msgctxt "Customers CSV"
msgid "Date joined"
msgstr ""

#: user/management/commands/dump_customers_data.py:31
msgctxt "Customers CSV"
msgid "Is activated"
msgstr ""

#: user/management/commands/dump_customers_data.py:32
msgctxt "Customers CSV"
msgid "Number of orders"
msgstr ""

#: user/management/commands/dump_customers_data.py:33
msgctxt "Customers CSV"
msgid "Amount spent"
msgstr ""

#: user/management/commands/dump_customers_data.py:43
msgid "Yes"
msgstr ""

#: user/management/commands/dump_customers_data.py:43
msgid "No"
msgstr ""

#: user/serializers.py:54
msgctxt "User"
msgid "Info saved successfully."
msgstr "Información guardada correctamente."

#: user/serializers.py:96
msgctxt "User"
msgid "Invalid old password provided."
msgstr "Se proporciona una contraseña antigua no válida."

#: user/serializers.py:117
msgctxt "User"
msgid "Password changed successfully."
msgstr "Contraseña cambiada correctamente."

#: user/serializers.py:130
msgctxt "User"
msgid "Invalid login credentials provided."
msgstr "Se proporcionan credenciales de inicio de sesión no válidas."

#: user/serializers.py:136
msgctxt "User"
msgid "Logged in successfully."
msgstr "Ha iniciado sesión correctamente."

#: user/serializers.py:168
msgctxt "User"
msgid "Logged out successfully."
msgstr "Se desconectó correctamente."

#: user/serializers.py:189
msgctxt "User"
msgid "Account already exists."
msgstr "Cuenta ya activada."

#: user/serializers.py:222
msgctxt "User"
msgid ""
"Registered successfully and an activation link was sent to your email "
"address."
msgstr ""
"Se registró correctamente y se envió un enlace de activación a su dirección "
"de correo electrónico."

#: user/serializers.py:235
#, python-format
msgctxt "User"
msgid "A password reset email was sent to \"%s\"."
msgstr "Se envió un correo electrónico para restablecer la contraseña a “%s”."

#: user/serializers.py:258
msgctxt "User"
msgid "Provided UID is invalid."
msgstr "El UID proporcionado no es válido."

#: user/serializers.py:259
msgctxt "User"
msgid "Provided token is invalid."
msgstr "El token proporcionado no es válido."

#: user/serializers.py:283
msgctxt "User"
msgid "Account already activated."
msgstr "Cuenta ya activada."

#: user/serializers.py:295
msgctxt "User"
msgid "Account activated successfully."
msgstr "Cuenta activada con éxito."

#: user/serializers.py:315
msgctxt "User"
msgid "Password reset successfully."
msgstr "Contraseña restablecida con éxito."

#~ msgctxt "Address"
#~ msgid "Address is required."
#~ msgstr "Dirección de envío."

#~ msgctxt "User"
#~ msgid "User with this email already exists."
#~ msgstr "El usuario con este correo electrónico ya existe."

#~ msgctxt "User"
#~ msgid "Registered successfully."
#~ msgstr "Registrado correctamente."
