# fmt: off
from gevent import monkey; monkey.patch_all()  # noqa
import grpc.experimental.gevent as grpc_gevent; grpc_gevent.init_gevent()  # noqa
# fmt: on

import os
from pathlib import Path

import environ

BASE_DIR = Path(__file__).resolve().parent.parent

environ.Env.read_env(str(BASE_DIR / ".env"))

from django.core.wsgi import get_wsgi_application  # noqa

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project.settings.production")

application = get_wsgi_application()
