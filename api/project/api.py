from django.urls import path
from django.views.generic import TemplateView
from rest_framework.schemas import get_schema_view
from salesman.urls import payment_methods_pool, router

from catalog.views import ProductViewSet
from command.views import CommandView
from core.schemas import SchemaGenerator
from core.views import PageViewSet
from home.views import ConfigViewSet
from shop.views import sendcloud_webhook_view
from shop.views.access import validate_product_access_code
from user.views import UserViewSet

router.register("config", ConfigViewSet, basename="config")
router.register("pages", PageViewSet, basename="page")
router.register("products", ProductViewSet, basename="product")
router.register("user", UserViewSet, basename="user")

api_urls = router.urls + payment_methods_pool.get_urls()
api_urls += [
    path("command/<slug:command>/", CommandView.as_view(), name="command"),
    path(
        "shipping/sendcloud/webhook/",
        sendcloud_webhook_view,
        name="sendcloud-webhook",
    ),
    path(
        "openapi/",
        get_schema_view(
            title="Leeos Merch API",
            version="1.0.0",
            patterns=api_urls,
            url="/api/v1/",
            generator_class=SchemaGenerator,
        ),
        name="openapi",
    ),
    path(
        "docs/",
        TemplateView.as_view(
            template_name="api-docs.html",
            extra_context={"schema_url": "openapi"},
        ),
        name="docs",
    ),
    # CODE
    path(
        "product/validate/code/",
        validate_product_access_code,
        name="product-validate-access-code",
    ),
]
