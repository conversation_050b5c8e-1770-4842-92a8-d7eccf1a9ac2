from django.conf import settings
from django.conf.urls.i18n import i18n_patterns
from django.urls import include, path, re_path
from django.views.generic import RedirectView
from wagtail.admin import urls as wagtailadmin_urls
from wagtail.core import urls as wagtail_urls
from wagtail.documents import urls as wagtaildocs_urls

from .api import api_urls

urlpatterns = [
    path("docs/", include(wagtaildocs_urls)),
    path("api/v1/", include(api_urls)),
    path("admin/", include(wagtailadmin_urls)),
]

if settings.DEBUG:
    from django.conf.urls.static import static
    from django.contrib.staticfiles.urls import staticfiles_urlpatterns

    urlpatterns += staticfiles_urlpatterns()
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


if settings.DEBUG_TOOLBAR:
    import debug_toolbar

    urlpatterns += [path("__debug__/", include(debug_toolbar.urls))]


urlpatterns += [
    re_path("^.*", RedirectView.as_view(url="/admin/")),
]

# Needed for URL generation in API.
urlpatterns += i18n_patterns(
    path("", include(wagtail_urls)),
)
