import io
import os
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from typing import Optional

import environ


def get_secret_settings() -> Optional[io.StringIO]:
    """
    Pull settings from Secret Manager.
    """
    secret = os.getenv("DJANGO_SETTINGS_SECRET")
    if not secret:
        print("Missing DJANGO_SETTINGS_SECRET, skipping loading of secret settings...")
        return None

    import google.auth
    from google.auth.exceptions import GoogleAuthError
    from google.cloud import secretmanager

    try:
        # Uses `GOOGLE_APPLICATION_CREDENTIALS` environment variable.
        _, project = google.auth.default()
    except GoogleAuthError:
        print("No Google credentials, skipping loading of secret settings...")
        return None

    if not project:
        print("No Google project, skipping loading of secret settings...")
        return None

    print(f"Loading secret settings `{secret}` from project `{project}`...")
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project}/secrets/{secret}/versions/latest"
    response = client.access_secret_version(request={"name": name})
    payload = response.payload.data.decode("UTF-8")
    return io.StringIO(payload)


BASE_DIR = Path(__file__).resolve().parent.parent.parent
PROJECT_DIR = BASE_DIR / "project"
ENV_FILE = BASE_DIR / ".env"

env = environ.Env()

if ENV_FILE.is_file():
    env.read_env(str(ENV_FILE))
else:
    secret_env = get_secret_settings()
    if secret_env:
        env.read_env(secret_env)

SITE_NAME = env.str("SITE_NAME", default="MadKat")
BASE_URL = env.str("BASE_URL")
SITE_URL = env.str("SITE_URL", default=BASE_URL)
SECRET_KEY = env.str("SECRET_KEY")


# Application definition
INSTALLED_APPS = [
    "modeltranslation",
    # project
    "core",
    "command",
    "tasks",
    "dashboard",
    "user",
    "home",
    "basic",
    "catalog",
    "country",
    "shop",
    "shop_dashboard",
    "stats",
    "notification",
    "extra",
    "loadtest",
    # wagtail
    "wagtail_localize",
    "wagtail_localize.locales",
    "wagtail.contrib.forms",
    "wagtail.contrib.redirects",
    "wagtail.contrib.modeladmin",
    "wagtail.contrib.settings",
    "wagtail.contrib.styleguide",
    "wagtail.contrib.table_block",
    "wagtail.embeds",
    "wagtail.sites",
    "wagtail.users",
    "wagtail.snippets",
    "wagtail.documents",
    "wagtail.images",
    "wagtail.search",
    "wagtail.admin",
    "wagtail.core",
    "wagtail.api.v2",
    "modelcluster",
    "taggit",
    "wagtailorderable",
    "generic_chooser",
    # api
    "rest_framework",
    "corsheaders",
    "django_filters",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    # salesman
    "salesman.core",
    "salesman.basket",
    "salesman.checkout",
    "salesman.orders",
    "salesman.admin",
    # django
    "collectfast",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.postgres",
    # other
    "djmoney",
    "djmoney.contrib.exchange",
    "post_office",
    # otp
    "wagtail_2fa",
    "django_otp",
    "django_otp.plugins.otp_totp",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "core.db.PrimaryReplicaRouterMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "core.middleware.LocaleMiddleware",
    "core.middleware.FixedAdminLocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "wagtail_2fa.middleware.VerifyUserMiddleware",
    # 'wagtail_2fa.middleware.VerifyUserPermissionsMiddleware',
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    # "wagtail.contrib.redirects.middleware.RedirectMiddleware",
]

ROOT_URLCONF = "project.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [PROJECT_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "project.wsgi.application"
SILENCED_SYSTEM_CHECKS = ["models.W036"]

# Database
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": env("DB_NAME"),
        "USER": env("DB_USER", default="postgres"),
        "PASSWORD": env("DB_PASS", default=""),
        "HOST": env("DB_HOST", default="localhost"),
        "PORT": env("DB_PORT", default=""),
        "ATOMIC_REQUESTS": False,
        "CONN_MAX_AGE": 0,
    },
    "replica1": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": env("DB_NAME"),
        "USER": env("DB_USER", default="postgres"),
        "PASSWORD": env("DB_PASS", default=""),
        "HOST": env("DB_REPLICA_1_HOST", default="localhost"),
        "PORT": env("DB_PORT", default=""),
        "ATOMIC_REQUESTS": False,
        "CONN_MAX_AGE": 0,
    },
}
DATABASE_ROUTERS = ["core.db.PrimaryReplicaRouter"]

# Cache
CACHES = {"default": env.cache(default="locmemcache://leeosmerch")}
if CACHES["default"]["BACKEND"] == "django_redis.cache.RedisCache":
    SESSION_ENGINE = "django.contrib.sessions.backends.cached_db"

    if "OPTIONS" not in CACHES["default"]:
        CACHES["default"]["OPTIONS"] = {}

    # CACHES["default"]["OPTIONS"]["CONNECTION_POOL_KWARGS"] = {
    #     "max_connections": env.int("REDIS_MAX_CONNS", default=50),
    # }


# auth, user
AUTH_USER_MODEL = "user.User"
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"  # noqa
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]
LOGIN_REDIRECT_URL = "/admin/"
LOGIN_URL = "/admin/login/"
USER_LOGIN_AFTER_REGISTRATION = True

# Email, sendgrid
ADMINS = [("BrightDock Dev Team", "<EMAIL>")]
MANAGERS = ADMINS
EMAIL_USE_TLS = env.bool("EMAIL_USE_TLS", default=True)
EMAIL_HOST = env.str("EMAIL_HOST", default="smtp.gmail.com")
EMAIL_HOST_USER = env.str("EMAIL_HOST_USER", default="<EMAIL>")
EMAIL_HOST_PASSWORD = env.str("EMAIL_HOST_PASSWORD", default="")
EMAIL_PORT = env.int("EMAIL_PORT", default=587)
DEFAULT_FROM_EMAIL = f"{SITE_NAME} <{EMAIL_HOST_USER}>"
SERVER_EMAIL = DEFAULT_FROM_EMAIL
EMAIL_SUBJECT_PREFIX = f"[{SITE_NAME}] "
EMAIL_BACKEND = "sendgrid_backend.SendgridBackend"
SENDGRID_API_KEY = env.str("SENDGRID_API_KEY", default="")

# Internationalization
LANGUAGE_CODE = "en"
LANGUAGES = [
    ("en", "English"),
    ("es", "Spanish"),
]
TIME_ZONE = "Europe/Madrid"
USE_I18N = True
USE_L10N = True
USE_TZ = True
LOCALE_PATHS = [PROJECT_DIR / "locale"]

# Static files (CSS, JavaScript, Images)
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]
STATICFILES_DIRS = [PROJECT_DIR / "static"]
STATICFILES_STORAGE = "django.contrib.staticfiles.storage.ManifestStaticFilesStorage"
STATIC_ROOT = env("STATIC_ROOT", default=BASE_DIR / "public/static")
MEDIA_ROOT = env("MEDIA_ROOT", default=BASE_DIR / "public/media")
MEDIA_LOCATION = "media"
STATIC_LOCATION = "static"
STATIC_URL = f"/{STATIC_LOCATION}/"
MEDIA_URL = f"/{MEDIA_LOCATION}/"

# Default primary key field type
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

# rest_framework
REST_FRAMEWORK = {
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        "rest_framework.renderers.BrowsableAPIRenderer",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        # "user.authentication.TokenAuthentication",
        "user.authentication.JWTTokenUserAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.AllowAny",
    ],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "burst": "60/minute",
        "page_submit": "5/hour",
    },
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 10,
    "COERCE_DECIMAL_TO_STRING": False,
}

RATE_LIMIT_API = env.bool("RATE_LIMIT_API", default=True)
if RATE_LIMIT_API:
    REST_FRAMEWORK["DEFAULT_THROTTLE_CLASSES"] = ["core.throttling.BurstRateThrottle"]

# rest_framework_simplejwt
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=1),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "TOKEN_USER_CLASS": "user.models.TokenUser",
}

# command
COMMAND_TOKEN = env.str("COMMAND_TOKEN", default=None)
COMMAND_ALLOWED_COMMADS = [
    "clear_cache",
    "run_tasks_every_minute",
    "run_tasks_every_hour",
    "run_tasks_every_day",
    "run_tasks_every_week",
]

# wagtail
WAGTAIL_SITE_NAME = SITE_NAME
WAGTAIL_ENABLE_UPDATE_CHECK = False
WAGTAIL_ALLOW_UNICODE_SLUGS = False
WAGTAIL_I18N_ENABLED = True
WAGTAIL_CONTENT_LANGUAGES = LANGUAGES

# wagtailadmin
WAGTAILADMIN_PERMITTED_LANGUAGES = [("en", "English")]
WAGTAILADMIN_STATIC_FILE_VERSION_STRINGS = False
WAGTAILADMIN_RICH_TEXT_EDITORS = {
    "default": {
        "WIDGET": "wagtail.admin.rich_text.DraftailRichTextArea",
        "OPTIONS": {
            "features": [
                "h2",
                "h3",
                "h4",
                "bold",
                "italic",
                "link",
                "ol",
                "ul",
                "hr",
                "table",
            ]
        },
    },
}

# wagtailimages
WAGTAILIMAGES_MAX_UPLOAD_SIZE = 2 * 1024 * 1024  # 2MB
WAGTAILIMAGES_MAX_IMAGE_PIXELS = 8000000  # 8 megapixels

# wagtaildocs
WAGTAILDOCS_EXTENSIONS = ["pdf", "mp3"]

# wagtailserach
WAGTAILSEARCH_BACKENDS = {
    "default": {
        "BACKEND": "wagtail.search.backends.database",
    },
}

# salesman, shop
SALESMAN_PRODUCT_TYPES = {
    "catalog.SingleProductVariant": "catalog.serializers.BasketVariantSerializer",
    "catalog.ColorProductVariant": "catalog.serializers.BasketVariantSerializer",
    "catalog.SizeProductVariant": "catalog.serializers.BasketVariantSerializer",
    "catalog.SizeColorProductVariant": "catalog.serializers.BasketVariantSerializer",
    "catalog.NumericSizeProductVariant": "catalog.serializers.BasketVariantSerializer",
    "catalog.NumericSizeColorProductVariant": "catalog.serializers.BasketVariantSerializer",
}
SALESMAN_BASKET_MODIFIERS = [
    "shop.modifiers.AvailabilityModifier",
    "shop.modifiers.CurrencyModifier",
    "shop.modifiers.WeightModifier",
    "shop.modifiers.ShippingModifier",
    "shop.modifiers.ShippingAvailabilityModifier",
    "shop.modifiers.ShippingRegionAvailabilityModifier",
    "shop.modifiers.DiscountModifier",  # ✅ Apply discounts first
    "shop.modifiers.AutomaticShippingDiscountModifier",  # Must run after shipping calculation
    "shop.modifiers.TaxModifier",  # ✅ Calculate VAT AFTER discounts
]
SALESMAN_PAYMENT_METHODS = [
    "shop.payment.redsys.RedSysPayment",
    "shop.payment.bizum.BizumPayment",
]
SALESMAN_PRICE_FORMATTER = "shop.formatters.price_format"
SALESMAN_BASKET_ITEM_VALIDATOR = "shop.validators.validate_basket_item"
SALESMAN_EXTRA_VALIDATOR = "shop.validators.validate_extra"
SALESMAN_ADDRESS_VALIDATOR = "shop.validators.validate_address"
SALESMAN_ORDER_STATUS = "shop.status.OrderStatus"
SALESMAN_ORDER_REFERENCE_GENERATOR = "shop.utils.generate_ref"
SALESMAN_ORDER_SUMMARY_SERIALIZER = "shop.serializers.order.OrderSummarySerializer"
SALESMAN_ADMIN_REGISTER = False
SALESMAN_ADMIN_CUSTOMER_FORMATTER = "shop.formatters.admin_customer_format"
SHOP_ORDER_REFERENCE_TWO_LETTER_PREFIX = "mk"
# SHOP_COMPANY_ADDRESS = "Madkat Store"
SHOP_COMPANY_ADDRESS = """Leeosmerch, S.L.
Diputacio 321, Est 5
Barcelona, 08009, Spain
CIF: B67608836"""
SHOP_COMPANY_EMAIL = "<EMAIL>"

# djmoney, exchange
CURRENCIES = ("EUR", "USD", "MXN", "CLP", "COP")
CURRENCY_SYMBOLS = {
    "EUR": "€",
    "USD": "$USD",
    "MXN": "MEX$",
    "CLP": "CLP$",
    "COP": "COP$",
}
EXCHANGE_BACKEND = "djmoney.contrib.exchange.backends.FixerBackend"
FIXER_ACCESS_KEY = env.str("FIXER_ACCESS_KEY", default="")
FIXER_URL = f"http://data.fixer.io/api/latest?symbols={','.join(CURRENCIES)}"
BASE_CURRENCY = "EUR"
RATES_CACHE_TIMEOUT = 10800  # 3h

# redsys
REDSYS_MERCHANT_NAME = env.str("REDSYS_MERCHANT_NAME", default="")
REDSYS_MERCHANT_CODE = env.str("REDSYS_MERCHANT_CODE", default="")
REDSYS_MERCHANT_TERMINAL = env.str("REDSYS_MERCHANT_TERMINAL", default="")
REDSYS_SECRET_KEY = env.str("REDSYS_SECRET_KEY", default="")
REDSYS_PRODUCTION = env.bool("REDSYS_PRODUCTION", default=False)
REDSYS_URL = (
    "https://sis.redsys.es/sis/realizarPago"
    if REDSYS_PRODUCTION
    else "https://sis-t.redsys.es:25443/sis/realizarPago"
)
REDSYS_REDIRECT_URL = env.str("REDSYS_REDIRECT_URL", default=REDSYS_URL)

# sendcloud
SENDCLOUD_API_KEY = env.str("SENDCLOUD_API_KEY", default="")
SENDCLOUD_API_SECRET = env.str("SENDCLOUD_API_SECRET", default="")

# Logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "null": {
            "class": "logging.NullHandler",
        },
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
        },
        "mail_admins": {
            "level": "ERROR",
            "class": "django.utils.log.AdminEmailHandler",
        },
    },
    "loggers": {
        "django.security.DisallowedHost": {
            "handlers": ["null"],
            "propagate": False,
        },
        "django": {
            "handlers": ["console"],
            "level": "INFO",
        },
    },
}

# debug_toolbar
DEBUG_TOOLBAR = env.bool("DEBUG_TOOLBAR", default=False)
if DEBUG_TOOLBAR:
    INSTALLED_APPS += ["debug_toolbar"]
    MIDDLEWARE.insert(0, "debug_toolbar.middleware.DebugToolbarMiddleware")
    DEBUG_TOOLBAR_CONFIG = {
        "SHOW_TOOLBAR_CALLBACK": lambda r: (
            True
            if r.path.startswith("/api/v1/") or r.path.startswith("/__debug__/")
            else False
        )
    }


# django-otp
WAGTAIL_2FA_REQUIRED = False
WAGTAIL_2FA_OTP_TOTP_NAME = "Leeos MadKat"
