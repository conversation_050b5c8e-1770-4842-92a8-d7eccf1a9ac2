from .base import *  # noqa
from .base import DATABASES, SALESMAN_PAYMENT_METHODS

DEBUG = True
INTERNAL_IPS = ["127.0.0.1"]
ALLOWED_HOSTS = ["*"]
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
# SENDGRID_SANDBOX_MODE_IN_DEBUG = False

# querycount
# if DEBUG_TOOLBAR:
#     MIDDLEWARE += ["querycount.middleware.QueryCountMiddleware"]

# corsheaders
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# salesman
SALESMAN_PAYMENT_METHODS += [
    "shop.payment.dummy.DummyPayment",
]

# Reset databases
DATABASE_ROUTERS = []

if "replica1" in DATABASES:
    del DATABASES["replica1"]
