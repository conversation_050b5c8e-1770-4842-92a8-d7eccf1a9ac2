from .base import REST_FRAMEWORK
from .staging import *  # noqa

CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOWED_ORIGINS = [
    "https://cms.madkat.store",
    "https://madkat.store",
    "https://www.madkat.store",
    "https://leeosmerch.com",
    "https://cms.leeosmerch.com",
    "https://rubiuscorp.com",
    "https://cms.rubiuscorp.com",
    # RedSys IPs - Must also allow access on a domain level (Cloudflare IP Access Rules)
    "https://************",
    "https://************",
    # Sendcloud IPs
    "https://*************",
    "https://************",
    "https://************",
    "https://*************",
    "https://***********",
    "https://***********",
    "https://************",
    "https://*************",
    "https://************",
]

# rest_framework
if (
    "rest_framework.renderers.BrowsableAPIRenderer"
    in REST_FRAMEWORK["DEFAULT_RENDERER_CLASSES"]
):
    REST_FRAMEWORK["DEFAULT_RENDERER_CLASSES"].remove(
        "rest_framework.renderers.BrowsableAPIRenderer"
    )


# Security
CSRF_TRUSTED_ORIGINS = CORS_ALLOWED_ORIGINS
