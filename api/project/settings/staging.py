import logging
from typing import Union

import google.cloud.logging
from google.auth.exceptions import GoogleAuthError

import sentry_sdk

from .base import *  # noqa
from .base import CACHES, MEDIA_LOCATION, STATIC_LOCATION, env

# Setup logging for GCP
try:
    google_logging_client = google.cloud.logging.Client()
    google_logging_client.setup_logging()
except GoogleAuthError:
    pass


DEBUG = env.bool("DEBUG", default=False)
ALLOWED_HOSTS = [
    ".madkat.store",
    ".madkatstore.com",
    ".madkatshop.com",
    ".leeosmerch.com",
    ".rubiuscorp.com",
    ".brightdock.com",
    ".a.run.app",
]

# corsheaders
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Static files, storages
GS_BUCKET_NAME = "leeos-merch"
GS_DEFAULT_ACL = "publicRead"
GS_FILE_OVERWRITE = False
DEFAULT_FILE_STORAGE = "core.storages.GoogleCloudMediaStorage"
STATICFILES_STORAGE = "core.storages.GoogleCloudStaticStorage"
STORAGE_URL = f"https://{GS_BUCKET_NAME}.storage.googleapis.com"
MEDIA_URL = f"{STORAGE_URL}/{MEDIA_LOCATION}/"
STATIC_URL = f"{STORAGE_URL}/{STATIC_LOCATION}/"

# collectfast
CACHES["collectfast"] = env.cache(
    "CACHE_URL_COLLECTFAST",
    default="dbcache://cache_collectfast?timeout=None&max_entries=1000",
)
COLLECTFAST_STRATEGY = "collectfast.strategies.gcloud.GoogleCloudStrategy"
COLLECTFAST_CACHE = "collectfast"

# Security
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_SAMESITE = "None"
CSRF_COOKIE_SECURE = True
CSRF_TRUSTED_ORIGINS = ["*"]
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_SECONDS = 31536000
SECURE_REDIRECT_EXEMPT: list = []
SECURE_SSL_HOST: Union[str, bool, None] = None
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Sentry
SENTRY_DSN = env(
    "SENTRY_DSN",
    default="https://<EMAIL>/4508050003066960",
)
SENTRY_ENVIRONMENT = env("SENTRY_ENVIRONMENT", default="production")


if SENTRY_DSN:
    try:
        sentry_sdk.init(
            dsn=SENTRY_DSN,
            environment=SENTRY_ENVIRONMENT,
            send_default_pii=True,
            profiles_sample_rate=0.1,
            traces_sample_rate=0.1,
            profile_lifecycle="trace",
        )
        logger.info("Sentry initialized")
    except Exception as e:
        logger.error(f"Failed to initialize Sentry: {str(e)}")
