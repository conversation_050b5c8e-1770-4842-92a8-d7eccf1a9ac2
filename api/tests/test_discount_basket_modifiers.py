import random
import resource
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from decimal import Decimal
from unittest.mock import MagicMock, patch

from django.core.cache import cache
from django.db import connection, transaction
from django.test import RequestFactory, TestCase, override_settings
from django.test.utils import CaptureQueriesContext
from django.utils import timezone
from salesman.basket.models import Basket
from wagtail.core.models import Locale, Page

from catalog.models import SingleProduct
from catalog.models.product import ProductVariant
from shop.discount.validators import DiscountValidator
from shop.models.discount import Discount, AutomaticShippingDiscount
from shop.models.shipping import ShippingMethod
from shop.modifiers import DiscountModifier, AutomaticShippingDiscountModifier
from user.models import User


class TestDiscountModifier(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser")
        self.request = RequestFactory().get("/")
        self.request.user = self.user
        self.basket = Basket.objects.get_or_create_from_request(self.request)[0]
        self.root = Page.objects.get(depth=1)

        # Create a base product using SingleProduct (no locale required)
        self.product = SingleProduct(
            title="Test Product",
            base_price=Decimal("100.00"),
            old_price=Decimal("120.00"),
            base_weight=Decimal("1.500"),
            origin_country="ES",
        )
        self.root.add_child(instance=self.product)

        # Create a shipping method for shipping discount tests
        self.shipping_method = ShippingMethod.objects.create(
            name="Standard Shipping", countries=["ES"], tax_percentage=21
        )

    def create_variant(self, **kwargs):
        """Helper method to create a variant with default values"""
        # Use the proper variant creation method for SingleProduct
        combo = self.product.get_variant_combinations()[0]
        variant = combo.get_or_create_variant(**kwargs)[0]
        return variant

    def test_percentage_discount_on_subtotal(self):
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 2)  # Total: 200.00

        # Create a 20% discount
        discount = Discount.objects.create(
            code="TEST20",
            discount_type="PERCENT_SUBTOTAL",
            value=20,
            min_basket_value=Decimal("0.00"),
            max_uses=None,
            expires_at=timezone.now() + timezone.timedelta(days=30),
        )

        # Apply discount to basket
        self.basket.extra["discount_code"] = discount.code
        self.basket.update(self.request)

        # Check discount was applied correctly
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["extra"].get("discount_code") == "TEST20"
            ),
            None,
        )

        self.assertIsNotNone(discount_row, "Discount row not found in basket")
        self.assertEqual(Decimal(discount_row.instance["amount"]), Decimal("-40.00"))
        self.assertEqual(self.basket.total, Decimal("160.00"))

    def test_fixed_amount_discount(self):
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 1)  # Total: 100.00

        # Create a $25 fixed discount
        discount = Discount.objects.create(
            code="FIXED25",
            discount_type="FIXED_AMOUNT",
            value=25,
            min_basket_value=Decimal("50.00"),
            max_uses=None,
            expires_at=timezone.now() + timezone.timedelta(days=30),
        )

        # Apply discount to basket
        self.basket.extra["discount_code"] = discount.code
        self.basket.update(self.request)

        # Check discount was applied correctly
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["extra"].get("discount_code") == "FIXED25"
            ),
            None,
        )

        self.assertIsNotNone(discount_row)
        self.assertEqual(Decimal(discount_row.instance["amount"]), Decimal("-25.00"))
        self.assertEqual(self.basket.total, Decimal("75.00"))

    def test_shipping_discount(self):
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 1)

        # Set shipping country to trigger shipping calculation
        self.basket.extra["country"] = "ES"

        # Create a 50% shipping discount
        discount = Discount.objects.create(
            code="SHIP50",
            discount_type="PERCENT_SHIPPING",
            value=50,
            min_basket_value=Decimal("0.00"),
            max_uses=None,
            expires_at=timezone.now() + timezone.timedelta(days=30),
        )

        # Apply discount to basket
        self.basket.extra["discount_code"] = discount.code
        self.basket.update(self.request)

        # Check discount was applied correctly
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["extra"].get("discount_code") == "SHIP50"
            ),
            None,
        )

        self.assertIsNotNone(discount_row)

        shipping_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["label"] == "Standard Shipping"
            ),
            None,
        )

        if shipping_row:  # Only test if shipping was applied
            shipping_amount = Decimal(
                shipping_row.instance["extra"]["price"].strip("Decimal('").strip("')")
            )
            expected_discount = -(shipping_amount * Decimal("0.50"))
            self.assertEqual(
                Decimal(discount_row.instance["amount"]), expected_discount
            )

    def test_invalid_discount_code(self):
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 1)

        # Apply non-existent discount code
        self.basket.extra["discount_code"] = "INVALID"
        self.basket.update(self.request)

        # Check error message exists
        self.assertEqual(
            self.basket.extra.get("discount_error"), "Invalid discount code"
        )

        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["extra"].get("discount_code") == "INVALID"
            ),
            None,
        )

        self.assertIsNone(discount_row)

    def test_empty_discount_code(self):
        """Test that an empty discount code does not cause an error."""
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 1)

        # Apply an empty discount code
        self.basket.extra["discount_code"] = ""
        self.basket.update(self.request)

        # Check that no error message exists
        self.assertNotIn("discount_error", self.basket.extra)

        # Check that no discount was applied
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["extra"].get("discount_code") == ""
            ),
            None,
        )
        self.assertIsNone(discount_row)

    def test_minimum_basket_value(self):
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 1)  # Total: 40.00

        # Create a discount with minimum basket value of 50.00
        discount = Discount.objects.create(
            code="MIN50",
            discount_type="FIXED_AMOUNT",
            value=10,
            min_basket_value=Decimal("500.00"),
            max_uses=None,
            expires_at=timezone.now() + timezone.timedelta(days=30),
        )

        # Apply discount to basket
        self.basket.extra["discount_code"] = discount.code
        self.basket.update(self.request)

        # Check error message exists and no discount was applied

        self.assertIn("discount_error", self.basket.extra)
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["label"] == discount.code
            ),
            None,
        )
        self.assertIsNone(discount_row)

    def test_expired_discount(self):
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 1)

        # Create an expired discount
        discount = Discount.objects.create(
            code="EXPIRED",
            discount_type="FIXED_AMOUNT",
            value=10,
            min_basket_value=Decimal("0.00"),
            max_uses=None,
            expires_at=timezone.now() - timezone.timedelta(days=30),
        )

        # Apply expired discount code
        self.basket.extra["discount_code"] = discount.code
        self.basket.update(self.request)

        # Check error message exists and no discount was applied
        self.assertIn("discount_error", self.basket.extra)
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["label"] == discount.code
            ),
            None,
        )
        self.assertIsNone(discount_row)

    def test_max_uses_discount(self):
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 1)

        # Create a discount with max 1 use
        discount = Discount.objects.create(
            code="MAXUSE1",
            discount_type="FIXED_AMOUNT",
            value=10,
            min_basket_value=Decimal("0.00"),
            max_uses=1,
            times_used=1,  # Already used once
            expires_at=timezone.now() + timezone.timedelta(days=30),
        )

        # Apply discount code
        self.basket.extra["discount_code"] = discount.code
        self.basket.update(self.request)

        # Check error message exists and no discount was applied
        self.assertIn("discount_error", self.basket.extra)
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["label"] == discount.code
            ),
            None,
        )
        self.assertIsNone(discount_row)

    def test_percentage_discount_on_total(self):
        """Test that PERCENT_TOTAL discount type works correctly"""
        # Create a product and add to basket
        product = self.create_variant()
        self.basket.add(product, 2)  # Total: 200.00

        # Set up shipping
        self.basket.extra["country"] = "ES"  # To trigger shipping calculation

        # Get the original total before discount
        self.basket.update(self.request)
        original_total = self.basket.total

        # Create a 100% total discount
        discount = Discount.objects.create(
            code="FREE100",
            description="100% off entire order",
            discount_type="PERCENT_TOTAL",
            value=100,
            min_basket_value=Decimal("0.00"),
            max_uses=None,
            expires_at=timezone.now() + timezone.timedelta(days=30),
        )

        # Apply discount to basket
        self.basket.extra["discount_code"] = discount.code
        self.basket.update(self.request)

        # Check discount was applied correctly
        discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["extra"].get("discount_code") == "FREE100"
            ),
            None,
        )

        self.assertIsNotNone(discount_row, "Discount row not found in basket")

        # The total should be 0 since it's a 100% discount on everything
        self.assertEqual(self.basket.total, Decimal("0.00"))
        self.assertEqual(Decimal(discount_row.instance["amount"]), -original_total)
        self.assertEqual(discount_row.instance["label"], "Free order (100% off)")

        # Test with 50% off total
        half_discount = Discount.objects.create(
            code="HALF50",
            description="50% off entire order",
            discount_type="PERCENT_TOTAL",
            value=50,
            min_basket_value=Decimal("0.00"),
            max_uses=None,
            expires_at=timezone.now() + timezone.timedelta(days=30),
        )

        # Reset basket and apply new discount
        self.basket.extra["discount_code"] = half_discount.code
        self.basket.update(self.request)

        # Check half discount was applied correctly
        half_discount_row = next(
            (
                row
                for row in self.basket.extra_rows.values()
                if row.instance["extra"].get("discount_code") == "HALF50"
            ),
            None,
        )

        self.assertIsNotNone(half_discount_row, "Half discount row not found in basket")
        expected_discount = original_total * Decimal("0.5")
        self.assertEqual(
            Decimal(half_discount_row.instance["amount"]), -expected_discount
        )
        self.assertEqual(self.basket.total, original_total - expected_discount)
        self.assertEqual(half_discount_row.instance["label"], "50% Off total order")


class TestDiscountValidator(TestCase):
    def setUp(self):
        self.discount = Discount.objects.create(
            code="TEST50", discount_type="PERCENT_TOTAL", value=50, max_uses=2
        )
        cache.clear()

    def test_basic_availability(self):
        """Test basic availability check"""
        is_available, message = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available)

        # Check it was tracked in Redis
        active_key = "discount_baskets:TEST50"
        current_baskets = cache.get(active_key)
        self.assertIn("basket1", current_baskets)

    def test_max_uses(self):
        """Test max uses limit"""
        # First basket
        is_available, _ = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available)

        # Second basket
        is_available, _ = DiscountValidator.check_discount_availability(
            "TEST50", "basket2"
        )
        self.assertTrue(is_available)

        # Third basket should fail
        is_available, message = DiscountValidator.check_discount_availability(
            "TEST50", "basket3"
        )
        self.assertFalse(is_available)
        self.assertEqual(message, "This discount code is no longer available")

    def test_release_discount(self):
        """Test releasing a discount"""
        # Reserve discount
        DiscountValidator.check_discount_availability("TEST50", "basket1")

        # Release it
        DiscountValidator.release_discount("TEST50", "basket1")

        # Should be available again
        active_key = "discount_baskets:TEST50"
        current_baskets = cache.get(active_key, set())
        self.assertNotIn("basket1", current_baskets)

    def test_redis_failure(self):
        """Test behavior when Redis is down"""
        with patch("django.core.cache.cache.get") as mock_get:
            mock_get.side_effect = Exception("Redis is down")

            # Should fall back to database check
            is_available, message = DiscountValidator.check_discount_availability(
                "TEST50", "basket1"
            )
            self.assertTrue(is_available)
            self.assertEqual(message, "Available (fallback)")

            # If max_uses reached in database, should still prevent usage
            self.discount.times_used = 2
            self.discount.save()

            is_available, message = DiscountValidator.check_discount_availability(
                "TEST50", "basket1"
            )
            self.assertFalse(is_available)

    def test_same_basket_reuse(self):
        """Test same basket can reuse its code"""
        # First use
        is_available, _ = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available)

        # Same basket should still be allowed
        is_available, message = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available)
        self.assertEqual(message, "Already reserved")

    def test_empty_discount_code(self):
        """Test that an empty discount code is handled gracefully."""
        is_available, message = DiscountValidator.check_discount_availability(
            "", "basket1"
        )
        self.assertFalse(is_available)
        self.assertEqual(message, "Invalid discount code")

    def tearDown(self):
        cache.clear()


class DiscountTestMonitor:
    """Monitor for catching issues during discount tests"""

    def __init__(self):
        self.inconsistencies = []
        self.deadlocks = []
        self.redis_errors = []
        self._start_time = time.time()

    def record_inconsistency(self, expected, actual, context):
        self.inconsistencies.append(
            {
                "time": time.time() - self._start_time,
                "expected": expected,
                "actual": actual,
                "context": context,
            }
        )

    def record_deadlock(self, operation, duration):
        if duration > 5:  # Consider operations taking > 5s as potential deadlocks
            self.deadlocks.append(
                {
                    "time": time.time() - self._start_time,
                    "operation": operation,
                    "duration": duration,
                }
            )

    def record_redis_error(self, error, context):
        self.redis_errors.append(
            {
                "time": time.time() - self._start_time,
                "error": str(error),
                "context": context,
            }
        )

    def check_consistency(self, discount_code: str):
        """Check consistency between Redis and database"""
        try:
            # Get Redis state
            active_key = f"discount_baskets:{discount_code}"
            redis_reservations = cache.get(active_key, set())

            # Get database state
            from shop.models import Discount

            discount = Discount.objects.get(code=discount_code)
            db_uses = discount_code.times_used

            # Compare states
            if len(redis_reservations) + db_uses > discount.max_uses:
                self.record_inconsistency(
                    expected=discount.max_uses,
                    actual=len(redis_reservations) + db_uses,
                    context=f"Total uses exceeds max_uses for {discount_code}",
                )

        except Exception as e:
            self.record_redis_error(e, "consistency_check")

    def report(self):
        """Generate report of monitoring results"""
        return {
            "duration": time.time() - self._start_time,
            "inconsistencies": self.inconsistencies,
            "deadlocks": self.deadlocks,
            "redis_errors": self.redis_errors,
        }


class TestDiscountLoadAndPerformance(TestCase):
    """Test suite for load testing and performance scenarios"""

    def setUp(self):
        self.monitor = DiscountTestMonitor()
        # Create multiple discounts
        self.discounts = []
        for i in range(100):
            discount = Discount.objects.create(
                code=f"LOAD{i}", discount_type="PERCENT_TOTAL", value=50, max_uses=10
            )
            self.discounts.append(discount)
        cache.clear()

    def test_heavy_load(self):
        """Test system under heavy load"""
        num_baskets = 1000
        concurrent_users = 50
        operations_per_user = num_baskets // concurrent_users

        def user_workflow():
            """Simulate realistic user behavior"""
            for _ in range(operations_per_user):
                try:
                    # Pick random discount
                    discount = random.choice(self.discounts)
                    basket_id = (
                        f"basket_{threading.get_ident()}_{random.randint(1, 1000)}"
                    )

                    # Random workflow
                    workflow = random.choice(
                        [
                            "validate_only",
                            "validate_and_release",
                            "multiple_validations",
                            "rapid_operations",
                        ]
                    )

                    start_time = time.time()

                    if workflow == "validate_only":
                        DiscountValidator.check_discount_availability(
                            discount.code, basket_id
                        )

                    elif workflow == "validate_and_release":
                        is_available, _ = DiscountValidator.check_discount_availability(
                            discount.code, basket_id
                        )
                        if is_available:
                            time.sleep(
                                random.uniform(0.1, 0.5)
                            )  # Simulate user activity
                            DiscountValidator.release_discount(discount.code, basket_id)

                    elif workflow == "multiple_validations":
                        # Try multiple discounts
                        for _ in range(3):
                            another_discount = random.choice(self.discounts)
                            DiscountValidator.check_discount_availability(
                                another_discount.code, basket_id
                            )

                    elif workflow == "rapid_operations":
                        # Rapid validate/release cycles
                        for _ in range(5):
                            is_available, _ = (
                                DiscountValidator.check_discount_availability(
                                    discount.code, basket_id
                                )
                            )
                            if is_available:
                                DiscountValidator.release_discount(
                                    discount.code, basket_id
                                )

                    duration = time.time() - start_time
                    if duration > 1.0:  # Flag slow operations
                        self.monitor.record_deadlock(f"workflow_{workflow}", duration)

                except Exception as e:
                    self.monitor.record_redis_error(
                        e, f"workflow_error_{threading.get_ident()}"
                    )

        # Run concurrent user workflows
        threads = []
        for _ in range(concurrent_users):
            thread = threading.Thread(target=user_workflow)
            thread.daemon = True
            threads.append(thread)

        start_time = time.time()
        [t.start() for t in threads]
        [t.join() for t in threads]
        total_time = time.time() - start_time

        # Performance assertions
        self.assertLess(total_time, 60)  # Should complete within 60 seconds

        # Check consistency for all discounts
        for discount in self.discounts:
            self.monitor.check_consistency(discount.code)

    def test_memory_usage(self):
        """Test memory usage under load"""

        def get_memory_usage():
            return resource.getrusage(resource.RUSAGE_SELF).ru_maxrss

        initial_memory = get_memory_usage()

        # Create lots of reservations
        operations = []
        for i in range(1000):
            discount = random.choice(self.discounts)
            operations.append((discount.code, f"basket_memory_{i}"))

        start_time = time.time()
        for discount_code, basket_id in operations:
            DiscountValidator.check_discount_availability(discount_code, basket_id)

        # Check memory usage
        final_memory = get_memory_usage()
        memory_increase = final_memory - initial_memory

        # Memory increase should be reasonable (adjust threshold as needed)
        self.assertLess(memory_increase, 50 * 1024)  # 50MB max increase

        duration = time.time() - start_time
        self.monitor.record_deadlock("memory_test", duration)

    def test_database_load(self):
        """Test database query performance"""

        def measure_queries():
            with CaptureQueriesContext(connection) as context:
                discount = random.choice(self.discounts)
                DiscountValidator.check_discount_availability(
                    discount.code, f"basket_db_{random.randint(1,1000)}"
                )
                return len(context.captured_queries)

        # Measure query counts under load
        query_counts = []
        start_time = time.time()

        for _ in range(100):
            query_counts.append(measure_queries())

        duration = time.time() - start_time

        # Performance assertions
        avg_queries = sum(query_counts) / len(query_counts)
        self.assertLess(
            avg_queries, 3
        )  # Should average less than 3 queries per operation
        self.assertLess(duration, 10)  # Should complete within 10 seconds

    def test_error_recovery(self):
        """Test system recovery after errors"""

        def simulate_error_condition():
            # Simulate various error conditions
            error_types = [
                (cache.delete, "Simulate cache clear"),
                (
                    lambda: cache.set("bad_key", "bad_value"),
                    "Simulate cache corruption",
                ),
                (
                    lambda: setattr(random.choice(self.discounts), "max_uses", 0),
                    "Simulate invalid max_uses",
                ),
            ]

            error_func, error_desc = random.choice(error_types)
            try:
                error_func()
            except Exception as e:
                self.monitor.record_redis_error(e, f"simulated_{error_desc}")

        # Run operations with random errors
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []

            # Normal operations
            for _ in range(50):
                discount = random.choice(self.discounts)
                futures.append(
                    executor.submit(
                        DiscountValidator.check_discount_availability,
                        discount.code,
                        f"basket_recovery_{random.randint(1,1000)}",
                    )
                )

                # Inject random errors
                if random.random() < 0.1:  # 10% chance of error
                    futures.append(executor.submit(simulate_error_condition))

            # All operations should complete
            results = [f.result() for f in as_completed(futures)]

        # Check consistency after errors
        for discount in self.discounts:
            self.monitor.check_consistency(discount.code)

    def tearDown(self):
        report = self.monitor.report()
        if any(
            [report["inconsistencies"], report["deadlocks"], report["redis_errors"]]
        ):
            print("\nLoad Test Report:")
            print(f"Duration: {report['duration']:.2f}s")
            print(f"Inconsistencies: {len(report['inconsistencies'])}")
            print(f"Potential Deadlocks: {len(report['deadlocks'])}")
            print(f"Redis Errors: {len(report['redis_errors'])}")
        cache.clear()


class TestDiscountEdgeCases(TestCase):
    def setUp(self):
        self.monitor = DiscountTestMonitor()
        # Delete any existing discounts with the test codes
        Discount.objects.filter(code__in=["EDGE50"]).delete()
        self.discount = Discount.objects.create(
            code="EDGE50", discount_type="PERCENT_TOTAL", value=50, max_uses=2
        )
        cache.clear()

    def test_concurrent_modifications(self):
        """Test concurrent modifications to max_uses"""
        # Clear any previous state
        cache.delete(f"discount_baskets:EDGE50")

        # Ensure no existing discount
        Discount.objects.filter(code="EDGE50").delete()

        # Create discount in setup with a unique code
        self.discount = Discount.objects.create(
            code="EDGE50",
            discount_type="PERCENT_TOTAL",
            value=50,
            max_uses=2,
            times_used=0,
        )

        @transaction.atomic
        def update_max_uses():
            try:
                # Get fresh copy of discount
                discount = Discount.objects.select_for_update().get(id=self.discount.id)
                time.sleep(0.1)
                discount.max_uses += 1
                discount.save()
                return True
            except Discount.DoesNotExist:
                return False

        def validate_discount():
            return DiscountValidator.check_discount_availability(
                "EDGE50", "basket_test"
            )

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = []
            for _ in range(5):
                futures.append(executor.submit(update_max_uses))
                futures.append(executor.submit(validate_discount))

            results = [f.result() for f in as_completed(futures)]
            self.assertTrue(any(results), "At least one operation should succeed")

    def tearDown(self):
        report = self.monitor.report()
        if any(
            [report["inconsistencies"], report["deadlocks"], report["redis_errors"]]
        ):
            print("\nEdge Cases Test Report:")
            print(f"Duration: {report['duration']:.2f}s")
            print(f"Inconsistencies: {len(report['inconsistencies'])}")
            print(f"Potential Deadlocks: {len(report['deadlocks'])}")
            print(f"Redis Errors: {len(report['redis_errors'])}")
        cache.clear()


class TestDiscountValidator(TestCase):
    def setUp(self):
        cache.clear()  # Start fresh
        # Delete any existing discounts
        Discount.objects.filter(code="TEST50").delete()

        self.discount = Discount.objects.create(
            code="TEST50",
            discount_type="PERCENT_TOTAL",
            value=50,
            max_uses=2,
            times_used=0,
        )

    def test_basic_availability(self):
        """Test basic availability check"""
        is_available, message = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(
            is_available, f"Expected discount to be available. Got message: {message}"
        )

        # Check it was tracked in Redis
        current_baskets = DiscountValidator._get_reserved_baskets("TEST50")
        self.assertIn("basket1", current_baskets)

    def test_max_uses(self):
        """Test max uses limit"""
        # First basket
        is_available, msg = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available, f"First basket should be available. Got: {msg}")

        # Second basket
        is_available, msg = DiscountValidator.check_discount_availability(
            "TEST50", "basket2"
        )
        self.assertTrue(is_available, f"Second basket should be available. Got: {msg}")

        # Third basket should fail
        is_available, message = DiscountValidator.check_discount_availability(
            "TEST50", "basket3"
        )
        self.assertFalse(is_available)
        self.assertEqual(message, "This discount code is no longer available")

    def test_release_discount(self):
        """Test releasing a discount"""
        # First reserve the discount
        is_available, msg = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available, f"Should be able to reserve discount. Got: {msg}")

        # Verify it's reserved
        current_baskets = DiscountValidator._get_reserved_baskets("TEST50")
        self.assertIn("basket1", current_baskets)

        # Release it
        result = DiscountValidator.release_discount("TEST50", "basket1")
        self.assertTrue(result, "Release should succeed")

        # Verify it's released
        current_baskets = DiscountValidator._get_reserved_baskets("TEST50")
        self.assertNotIn("basket1", current_baskets)

    def test_redis_failure(self):
        """Test behavior when Redis is down"""
        with patch("django.core.cache.cache.get") as mock_get:
            mock_get.side_effect = Exception("Redis is down")

            # Should fall back to database check
            is_available, message = DiscountValidator.check_discount_availability(
                "TEST50", "basket1"
            )
            self.assertTrue(is_available)
            self.assertEqual(message, "Available (fallback)")

            # If max_uses reached in database, should still prevent usage
            self.discount.times_used = 2
            self.discount.save()

            is_available, message = DiscountValidator.check_discount_availability(
                "TEST50", "basket1"
            )
            self.assertFalse(is_available)

    def test_same_basket_reuse(self):
        """Test same basket can reuse its code"""
        # First use
        is_available, msg = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available, f"First use should succeed. Got: {msg}")

        # Same basket should still be allowed
        is_available, message = DiscountValidator.check_discount_availability(
            "TEST50", "basket1"
        )
        self.assertTrue(is_available)
        self.assertEqual(message, "Already reserved")

    def tearDown(self):
        cache.clear()


class TestAutomaticShippingDiscountModifier(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser")
        self.request = RequestFactory().get("/")
        self.request.user = self.user
        self.basket = Basket.objects.get_or_create_from_request(self.request)[0]
        self.root = Page.objects.get(depth=1)

        # Create a base product using SingleProduct (no locale required)
        self.product = SingleProduct(
            title="Test Product",
            base_price=Decimal("100.00"),
            old_price=Decimal("120.00"),
            base_weight=Decimal("1.500"),
            origin_country="ES",
        )
        self.root.add_child(instance=self.product)

        # Create shipping method
        self.shipping_method = ShippingMethod.objects.create(
            name="Standard Shipping",
            countries=["ES", "FR", "DE"],
            base_price=Decimal("10.00"),
            tax_percentage=21,
        )

        # Set up basket with country and shipping
        self.basket.extra["country"] = "ES"
        self.basket.extra["postal_code"] = "08001"

        # Add a product to basket to have some subtotal
        self.variant = self.create_variant()
        self.basket.add(self.variant, quantity=1)

        # Create modifier instance
        self.modifier = AutomaticShippingDiscountModifier()

    def create_variant(self, **kwargs):
        """Helper method to create a variant with default values"""
        # Use the proper variant creation method for SingleProduct
        combo = self.product.get_variant_combinations()[0]
        variant = combo.get_or_create_variant(**kwargs)[0]
        return variant

    def add_shipping_row_to_basket(self, shipping_amount=Decimal("10.00")):
        """Helper to simulate shipping calculation"""
        # First update the basket to initialize extra_rows
        self.basket.update(self.request)

        # Create a mock shipping row using the same structure as ShippingModifier
        from salesman.basket.serializers import ExtraRowSerializer

        instance = {
            "label": self.shipping_method.name,
            "amount": shipping_amount,
            "extra": {"price": f"Decimal('{shipping_amount}')"},
        }
        context = {"request": self.request}
        self.basket.extra_rows["shipping"] = ExtraRowSerializer(
            instance, context=context
        )

    def test_no_discount_without_shipping(self):
        """Test that no discount is applied when shipping hasn't been calculated"""
        # Create automatic discount
        AutomaticShippingDiscount.objects.create(
            name="Test Discount",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("20.00"),
            is_active=True,
        )

        # Process basket without shipping row
        self.modifier.process_basket(self.basket, self.request)

        # Should not apply discount
        # Check that either extra_rows doesn't exist or doesn't contain our discount
        if hasattr(self.basket, "extra_rows") and self.basket.extra_rows:
            self.assertNotIn("auto_shipping_discount", self.basket.extra_rows)
        # If extra_rows doesn't exist, that's also fine - no discount was applied

    def test_automatic_shipping_discount_applied(self):
        """Test that automatic shipping discount is applied when conditions are met"""
        # Create automatic discount
        discount = AutomaticShippingDiscount.objects.create(
            name="20% Off Shipping",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("20.00"),
            is_active=True,
        )

        # Add shipping row
        self.add_shipping_row_to_basket(Decimal("10.00"))

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Verify discount was applied
        self.assertIn("auto_shipping_discount", self.basket.extra_rows)
        discount_row = self.basket.extra_rows["auto_shipping_discount"]
        self.assertEqual(discount_row.instance["label"], "20% Off Shipping")
        self.assertEqual(
            discount_row.instance["amount"], Decimal("-2.00")
        )  # 20% of 10.00
        self.assertEqual(
            discount_row.instance["extra"]["auto_discount_id"], discount.id
        )

    def test_free_shipping_discount(self):
        """Test 100% shipping discount (free shipping)"""
        # Create 100% discount
        AutomaticShippingDiscount.objects.create(
            name="Free Shipping",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("100.00"),
            is_active=True,
        )

        # Add shipping row
        self.add_shipping_row_to_basket(Decimal("15.00"))

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Verify free shipping
        discount_row = self.basket.extra_rows["auto_shipping_discount"]
        self.assertEqual(discount_row.instance["label"], "Free Shipping")
        self.assertEqual(discount_row.instance["amount"], Decimal("-15.00"))

    def test_basket_value_too_low(self):
        """Test that discount is not applied when basket value is too low"""
        # Create discount with high minimum
        AutomaticShippingDiscount.objects.create(
            name="High Minimum Discount",
            min_basket_value=Decimal("200.00"),  # Higher than our basket
            discount_percentage=Decimal("20.00"),
            is_active=True,
        )

        # Add shipping row
        self.add_shipping_row_to_basket()

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Should not apply discount
        self.assertNotIn("auto_shipping_discount", self.basket.extra_rows)

    def test_basket_value_too_high(self):
        """Test that discount is not applied when basket value exceeds maximum"""
        # Create discount with maximum limit
        AutomaticShippingDiscount.objects.create(
            name="Limited Discount",
            min_basket_value=Decimal("50.00"),
            max_basket_value=Decimal("80.00"),  # Lower than our basket
            discount_percentage=Decimal("20.00"),
            is_active=True,
        )

        # Add shipping row
        self.add_shipping_row_to_basket()

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Should not apply discount
        self.assertNotIn("auto_shipping_discount", self.basket.extra_rows)

    def test_inactive_discount_not_applied(self):
        """Test that inactive discounts are not applied"""
        # Create inactive discount
        AutomaticShippingDiscount.objects.create(
            name="Inactive Discount",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("20.00"),
            is_active=False,  # Inactive
        )

        # Add shipping row
        self.add_shipping_row_to_basket()

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Should not apply discount
        self.assertNotIn("auto_shipping_discount", self.basket.extra_rows)

    def test_shipping_method_restriction(self):
        """Test that discount only applies to specific shipping methods"""
        # Create another shipping method
        other_shipping = ShippingMethod.objects.create(
            name="Express Shipping",
            countries=["ES"],
            base_price=Decimal("20.00"),
        )

        # Create discount limited to specific shipping method
        AutomaticShippingDiscount.objects.create(
            name="Express Only Discount",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("30.00"),
            is_active=True,
        ).shipping_methods.set([other_shipping])

        # Add shipping row for standard shipping (not express)
        self.add_shipping_row_to_basket()

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Should not apply discount (wrong shipping method)
        self.assertNotIn("auto_shipping_discount", self.basket.extra_rows)

    def test_country_restriction(self):
        """Test that discount only applies to specific countries"""
        # Create discount limited to France only
        AutomaticShippingDiscount.objects.create(
            name="France Only Discount",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("25.00"),
            countries=["FR"],  # Only France
            is_active=True,
        )

        # Basket is set to Spain
        self.add_shipping_row_to_basket()

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Should not apply discount (wrong country)
        self.assertNotIn("auto_shipping_discount", self.basket.extra_rows)

    def test_priority_selection(self):
        """Test that highest priority discount is selected when multiple match"""
        # Create two matching discounts with different priorities
        low_priority = AutomaticShippingDiscount.objects.create(
            name="Low Priority",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("10.00"),
            priority=1,
            is_active=True,
        )

        high_priority = AutomaticShippingDiscount.objects.create(
            name="High Priority",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("15.00"),
            priority=5,  # Higher priority
            is_active=True,
        )

        # Add shipping row
        self.add_shipping_row_to_basket()

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Should apply the high priority discount
        self.assertIn("auto_shipping_discount", self.basket.extra_rows)
        discount_row = self.basket.extra_rows["auto_shipping_discount"]
        self.assertEqual(
            discount_row.instance["extra"]["auto_discount_id"], high_priority.id
        )
        self.assertEqual(discount_row.instance["label"], "15% Off Shipping")

    def test_date_range_restrictions(self):
        """Test that discount respects date range restrictions"""
        from django.utils import timezone
        from datetime import timedelta

        # Create discount that expired yesterday
        AutomaticShippingDiscount.objects.create(
            name="Expired Discount",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("20.00"),
            valid_until=timezone.now() - timedelta(days=1),  # Expired
            is_active=True,
        )

        # Add shipping row
        self.add_shipping_row_to_basket()

        # Process basket
        self.modifier.process_basket(self.basket, self.request)

        # Should not apply expired discount
        self.assertNotIn("auto_shipping_discount", self.basket.extra_rows)
