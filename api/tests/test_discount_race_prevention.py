"""
Focused tests for discount code race condition prevention.
These tests verify the core functionality works correctly.
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from decimal import Decimal
from unittest.mock import patch

from django.core.cache import cache
from django.test import TestCase

from shop.models import Discount, DiscountUsage
from shop.discount.validators import DiscountValidator


class DiscountRacePreventionTests(TestCase):
    """Test race condition prevention for discount codes."""
    
    def setUp(self):
        cache.clear()
        # Create a limited discount code
        self.discount = Discount.objects.create(
            code="RACE50",
            discount_type="PERCENT_TOTAL",
            value=50,
            max_uses=5,  # Small limit for easier testing
            times_used=0,
            is_active=True,
        )
    
    def tearDown(self):
        cache.clear()

    def test_basic_reservation_functionality(self):
        """Test basic reservation functionality works"""
        is_available, message = DiscountValidator.check_discount_availability(
            "RACE50", "basket_1"
        )
        self.assertTrue(is_available)
        self.assertIn("available", message.lower())
        
        # Verify reservation exists
        reserved_baskets = DiscountValidator._get_reserved_baskets("RACE50")
        self.assertIn("basket_1", reserved_baskets)

    def test_duplicate_reservation_same_basket(self):
        """Test that same basket can reserve multiple times"""
        # First reservation
        is_available1, _ = DiscountValidator.check_discount_availability(
            "RACE50", "basket_1"
        )
        self.assertTrue(is_available1)
        
        # Second reservation for same basket should succeed
        is_available2, message = DiscountValidator.check_discount_availability(
            "RACE50", "basket_1"
        )
        self.assertTrue(is_available2)
        self.assertIn("already reserved", message.lower())

    def test_reservation_limit_enforcement(self):
        """Test that reservation limits are enforced"""
        # Reserve all available slots
        for i in range(5):
            is_available, _ = DiscountValidator.check_discount_availability(
                "RACE50", f"basket_{i}"
            )
            self.assertTrue(is_available, f"Reservation {i} should succeed")
        
        # Next reservation should fail
        is_available, message = DiscountValidator.check_discount_availability(
            "RACE50", "basket_overflow"
        )
        self.assertFalse(is_available)
        self.assertIn("no longer available", message.lower())

    def test_concurrent_reservations_small_scale(self):
        """Test concurrent reservations with small thread count"""
        num_threads = 8
        results = []
        
        def try_reserve(basket_id):
            is_available, message = DiscountValidator.check_discount_availability(
                "RACE50", f"basket_{basket_id}"
            )
            results.append((basket_id, is_available, message))
            return is_available
        
        # Run concurrent reservations
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(try_reserve, i) for i in range(num_threads)]
            
            for future in as_completed(futures):
                future.result()
        
        # Count successful reservations
        successful = sum(1 for _, success, _ in results if success)
        
        # Should not exceed limit
        self.assertLessEqual(successful, 5, f"Too many successful reservations: {successful}")
        
        # Verify Redis state
        reserved_baskets = DiscountValidator._get_reserved_baskets("RACE50")
        self.assertLessEqual(len(reserved_baskets), 5)
        
        print(f"Concurrent test: {successful}/5 successful reservations from {num_threads} attempts")

    def test_confirmation_functionality(self):
        """Test discount confirmation works"""
        # Make a reservation
        is_available, _ = DiscountValidator.check_discount_availability(
            "RACE50", "basket_confirm"
        )
        self.assertTrue(is_available)
        
        # Confirm the reservation
        success = DiscountValidator.confirm_discount_usage(
            "RACE50", "basket_confirm"
        )
        self.assertTrue(success)
        
        # Verify database was updated
        self.discount.refresh_from_db()
        self.assertEqual(self.discount.times_used, 1)
        
        # Verify reservation was released
        reserved_baskets = DiscountValidator._get_reserved_baskets("RACE50")
        self.assertNotIn("basket_confirm", reserved_baskets)

    def test_redis_fallback_behavior(self):
        """Test behavior when Redis operations fail"""
        with patch('django.core.cache.cache.get', side_effect=Exception("Redis error")):
            with patch('django.core.cache.cache.set', side_effect=Exception("Redis error")):
                # Should fall back to database-only validation
                is_available, message = DiscountValidator.check_discount_availability(
                    "RACE50", "basket_fallback"
                )
                
                # Should still work with fallback
                self.assertTrue(is_available)
                self.assertIn("fallback", message.lower())

    def test_unlimited_discount_handling(self):
        """Test that unlimited discounts (max_uses=None) work correctly"""
        unlimited_discount = Discount.objects.create(
            code="UNLIMITED",
            discount_type="PERCENT_TOTAL",
            value=10,
            max_uses=None,  # Unlimited
            times_used=0,
            is_active=True,
        )
        
        # Should always be available
        for i in range(20):  # Try many times
            is_available, message = DiscountValidator.check_discount_availability(
                "UNLIMITED", f"basket_{i}"
            )
            self.assertTrue(is_available)
            self.assertEqual(message, "Available")

    def test_release_reservation(self):
        """Test releasing reservations works"""
        # Make a reservation
        is_available, _ = DiscountValidator.check_discount_availability(
            "RACE50", "basket_release"
        )
        self.assertTrue(is_available)
        
        # Verify reservation exists
        reserved_baskets = DiscountValidator._get_reserved_baskets("RACE50")
        self.assertIn("basket_release", reserved_baskets)
        
        # Release the reservation
        success = DiscountValidator.release_discount("RACE50", "basket_release")
        self.assertTrue(success)
        
        # Verify reservation is gone
        reserved_baskets = DiscountValidator._get_reserved_baskets("RACE50")
        self.assertNotIn("basket_release", reserved_baskets)

    def test_mixed_database_and_redis_usage(self):
        """Test system works with existing database usage"""
        # Set some existing usage in database
        self.discount.times_used = 3
        self.discount.save()
        
        # Should only allow 2 more reservations (5 total - 3 used = 2 remaining)
        successful_reservations = 0
        for i in range(5):
            is_available, _ = DiscountValidator.check_discount_availability(
                "RACE50", f"basket_{i}"
            )
            if is_available:
                successful_reservations += 1
        
        self.assertLessEqual(successful_reservations, 2, 
                           f"Should only allow 2 more reservations, got {successful_reservations}")

    def test_performance_under_load(self):
        """Test system performance under moderate load"""
        num_attempts = 50
        start_time = time.time()
        
        results = []
        
        def rapid_attempts(thread_id):
            for i in range(5):  # 5 attempts per thread
                is_available, _ = DiscountValidator.check_discount_availability(
                    "RACE50", f"basket_{thread_id}_{i}"
                )
                results.append(is_available)
                time.sleep(0.001)  # Small delay
        
        # Run with multiple threads
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(rapid_attempts, i) for i in range(10)]
            
            for future in as_completed(futures):
                future.result()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Performance check
        self.assertLess(duration, 10, f"Performance test took too long: {duration:.2f}s")
        
        # Verify no over-allocation
        successful = sum(1 for result in results if result)
        self.assertLessEqual(successful, 5, f"Over-allocation detected: {successful}")
        
        print(f"Performance test: {successful}/5 successful from {len(results)} attempts in {duration:.2f}s")
