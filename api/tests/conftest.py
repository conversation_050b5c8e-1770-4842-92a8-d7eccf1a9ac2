import pytest


@pytest.fixture
def client():
    from rest_framework.test import APIClient

    return APIClient()


@pytest.fixture
def root_page():
    from wagtail.core.models import Page

    return Page.objects.get(depth=1)


@pytest.fixture
def test_user():
    from user.models import User

    return User.objects.get_or_create(username="test")[0]


@pytest.fixture
def test_request(test_user):
    from django.test import RequestFactory

    request = RequestFactory().get("/")
    request.user = test_user
    return request


@pytest.fixture
def basket(test_request):
    from salesman.basket.models import Basket

    basket = Basket.objects.get_or_create_from_request(test_request)[0]
    basket.update(test_request)
    return basket


@pytest.fixture
def create_page(root_page):
    from core.models import CorePage

    def fn(title, parent=None, **kwargs):
        page = CorePage(title=title, **kwargs)
        parent = parent or root_page
        parent.add_child(instance=page)
        return page

    return fn


@pytest.fixture
def create_product_with_variant(root_page):
    from catalog.models import SingleProduct

    def fn(title, quantity=None, **kwargs):
        product = SingleProduct(title=title, origin_country="HR", **kwargs)
        root_page.add_child(instance=product)
        combo = product.get_variant_combinations()[0]
        variant = combo.get_or_create_variant(quantity=quantity)[0]
        return product, variant

    return fn


@pytest.fixture
def csv_export_test_data(root_page):
    """Create comprehensive test data for CSV export testing"""
    from catalog.models.catalog import (
        ProductCollection,
        SingleProduct,
        SingleProductVariant,
    )
    from catalog.models.attribute import ProductColor

    # Create collection
    collection = ProductCollection(
        title="CSV Test Collection",
        slug="csv-test-collection",
        caption="Collection for CSV export testing",
    )
    root_page.add_child(instance=collection)

    # Create products with variants
    products_data = []

    # Single product with variant
    product1 = SingleProduct(
        title="CSV Test Product 1",
        slug="csv-test-product-1",
        details="First test product for CSV export",
        origin_country="HR",
        base_weight=100,
    )
    collection.add_child(instance=product1)

    # Create variant using proper method
    combo1 = product1.get_variant_combinations()[0]
    variant1 = combo1.get_or_create_variant(quantity=100)[0]

    products_data.append((product1, [variant1]))

    # Another single product
    product2 = SingleProduct(
        title="CSV Test Product 2",
        slug="csv-test-product-2",
        details="Second test product for CSV export",
        origin_country="ES",
        base_weight=200,
    )
    collection.add_child(instance=product2)

    # Create variant using proper method
    combo2 = product2.get_variant_combinations()[0]
    variant2 = combo2.get_or_create_variant(quantity=50)[0]

    products_data.append((product2, [variant2]))

    return {
        "collection": collection,
        "products": products_data,
        "total_products": 2,
        "total_variants": 2,
    }
