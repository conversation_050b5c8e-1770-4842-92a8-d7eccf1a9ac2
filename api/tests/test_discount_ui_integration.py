"""
Tests for discount UI integration functionality.
Tests the integration between the backend discount logic and frontend UI components.
"""

import pytest
from decimal import Decimal
from django.test import TestCase, RequestFactory
from django.utils import timezone
from django.core.cache import cache
from salesman.basket.models import Basket
from shop.models.discount import Discount, AutomaticShippingDiscount
from shop.discount.validators import DiscountValidator
from catalog.models.product import ProductVariant


class DiscountUIIntegrationTest(TestCase):
    """Test discount functionality that affects UI components."""

    def setUp(self):
        self.factory = RequestFactory()
        self.request = self.factory.get("/")
        self.basket = Basket.objects.create()
        self.basket.request = self.request

        # Clear cache before each test
        cache.clear()

    def test_discount_code_validation_flow(self):
        """Test the complete discount code validation flow that the UI depends on."""
        # Create a limited discount
        discount = Discount.objects.create(
            code="TESTCODE",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("10.00"),
            max_uses=1,
            expires_at=timezone.now() + timezone.timedelta(days=1),
        )

        # Test availability check (what UI calls when user types discount code)
        available, message = DiscountValidator.check_discount_availability(
            "TESTCODE", str(self.basket.id)
        )
        self.assertTrue(available)
        # Accept both Redis success and fallback messages
        self.assertIn(message, ["Reserved successfully", "Available (fallback)"])

        # Test that the same basket can check again (UI might call multiple times)
        available, message = DiscountValidator.check_discount_availability(
            "TESTCODE", str(self.basket.id)
        )
        self.assertTrue(available)
        # Accept Redis success, already reserved, or fallback messages
        self.assertIn(
            message,
            ["Already reserved", "Reserved successfully", "Available (fallback)"],
        )

        # Test that another basket cannot use the code
        other_basket = Basket.objects.create()
        available, message = DiscountValidator.check_discount_availability(
            "TESTCODE", str(other_basket.id)
        )
        self.assertFalse(available)
        self.assertIn("no longer available", message)

    def test_discount_timer_expiration_simulation(self):
        """Test discount reservation expiration (3-minute timer functionality)."""
        discount = Discount.objects.create(
            code="TIMERTEST",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("15.00"),
            max_uses=1,
        )

        # Reserve the discount
        available, message = DiscountValidator.check_discount_availability(
            "TIMERTEST", str(self.basket.id)
        )
        self.assertTrue(available)

        # Simulate timer expiration by manually cleaning up reservations
        DiscountValidator.cleanup_expired_reservations("TIMERTEST")

        # Now another basket should be able to use the code
        other_basket = Basket.objects.create()
        available, message = DiscountValidator.check_discount_availability(
            "TIMERTEST", str(other_basket.id)
        )
        self.assertTrue(available)

    def test_automatic_shipping_discount_detection(self):
        """Test automatic shipping discount detection for UI display."""
        # Create an automatic shipping discount
        auto_discount = AutomaticShippingDiscount.objects.create(
            name="Free Shipping Over 50",
            description="Get free shipping on orders over €50",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("100.00"),
            is_active=True,
        )

        # Test discount detection for qualifying basket
        qualifying_discount = AutomaticShippingDiscount.get_best_discount(
            basket_value=Decimal("60.00"), country="ES"
        )
        self.assertIsNotNone(qualifying_discount)
        self.assertEqual(qualifying_discount.name, "Free Shipping Over 50")

        # Test no discount for non-qualifying basket
        no_discount = AutomaticShippingDiscount.get_best_discount(
            basket_value=Decimal("30.00"), country="ES"
        )
        self.assertIsNone(no_discount)

    def test_discount_error_handling_for_ui(self):
        """Test error scenarios that the UI needs to handle."""
        # Test invalid discount code
        available, message = DiscountValidator.check_discount_availability(
            "INVALIDCODE", str(self.basket.id)
        )
        self.assertFalse(available)
        self.assertEqual(message, "Invalid discount code")

        # Test expired discount
        expired_discount = Discount.objects.create(
            code="EXPIRED",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("20.00"),
            expires_at=timezone.now() - timezone.timedelta(days=1),
        )

        # Check if discount is valid (what UI calls for validation)
        is_valid, error_message = expired_discount.is_valid()
        self.assertFalse(is_valid)
        self.assertIn("expired", error_message)

    def test_payment_flow_discount_handling(self):
        """Test discount handling during payment flow (critical for timer expiration)."""
        discount = Discount.objects.create(
            code="PAYMENTTEST",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("25.00"),
            max_uses=1,
        )

        # Reserve discount
        DiscountValidator.check_discount_availability(
            "PAYMENTTEST", str(self.basket.id)
        )

        # Simulate payment completion
        success = DiscountValidator.confirm_discount_usage(
            "PAYMENTTEST", str(self.basket.id), user=None
        )
        self.assertTrue(success)

        # Verify discount usage was recorded
        discount.refresh_from_db()
        self.assertEqual(discount.times_used, 1)

        # Verify reservation was released
        available, message = DiscountValidator.check_discount_availability(
            "PAYMENTTEST", str(self.basket.id)
        )
        self.assertFalse(available)  # Should be unavailable as it's used up

    def test_concurrent_discount_usage_prevention(self):
        """Test that the UI properly handles concurrent discount usage attempts."""
        discount = Discount.objects.create(
            code="CONCURRENT",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("30.00"),
            max_uses=1,
        )

        basket1 = Basket.objects.create()
        basket2 = Basket.objects.create()

        # First basket reserves the discount
        available1, message1 = DiscountValidator.check_discount_availability(
            "CONCURRENT", str(basket1.id)
        )
        self.assertTrue(available1)

        # Second basket should not be able to reserve it
        available2, message2 = DiscountValidator.check_discount_availability(
            "CONCURRENT", str(basket2.id)
        )
        self.assertFalse(available2)
        self.assertIn("no longer available", message2)

    def tearDown(self):
        """Clean up after each test."""
        cache.clear()
