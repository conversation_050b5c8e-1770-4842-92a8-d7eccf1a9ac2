import pytest

from stats.models import ProductStats


@pytest.mark.django_db
def test_record_basket_stats(create_product_with_variant, basket, test_request):
    p1 = create_product_with_variant("P1", quantity=10, base_price=10)[1]
    p2 = create_product_with_variant("P2", quantity=3, base_price=30)[1]
    p3 = create_product_with_variant("P3", quantity=1, base_price=50)[1]

    # Test create multiple new stats
    basket.add(p1, 3)
    basket.add(p2, 1)
    basket.update(test_request)
    ProductStats.record_basket(basket)
    p1.refresh_from_db()
    p2.refresh_from_db()
    assert p1.stats.sold_quantity == 3
    assert p1.stats.sold_total == 30
    assert p2.stats.sold_quantity == 1
    assert p2.stats.sold_total == 30
    basket.clear()

    # Test create single new stat
    basket.add(p3, 1)
    basket.update(test_request)
    ProductStats.record_basket(basket)
    p3.refresh_from_db()
    assert p3.stats.sold_quantity == 1
    assert p3.stats.sold_total == 50
    basket.clear()

    # Test update single stat
    basket.add(p1, 6)
    basket.update(test_request)
    ProductStats.record_basket(basket)
    p1.refresh_from_db()
    assert p1.stats.sold_quantity == 9
    assert p1.stats.sold_total == 90
    basket.clear()

    # Test update multiple stats
    basket.add(p1, 1)
    basket.add(p2, 1)
    basket.update(test_request)
    ProductStats.record_basket(basket)
    p1.refresh_from_db()
    p2.refresh_from_db()
    assert p1.stats.sold_quantity == 10
    assert p1.stats.sold_total == 100
    assert p2.stats.sold_quantity == 2
    assert p2.stats.sold_total == 60
