from django.test import RequestFactory, TestCase
from django.utils import timezone
from salesman.basket.models import Basket
from wagtail.core.models import Page

from catalog.models import BasketStockReservation, SingleProduct
from user.models import User


class TestInventory(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="dino")
        self.request = RequestFactory().get("/")
        self.request.user = self.user
        self.basket = Basket.objects.get_or_create_from_request(self.request)[0]
        self.root = Page.objects.get(depth=1)

    def create_product_with_variant(self, title, quantity=None, **kwargs):
        product = SingleProduct(title=title, origin_country="HR", **kwargs)
        self.root.add_child(instance=product)
        combo = product.get_variant_combinations()[0]
        variant = combo.get_or_create_variant(quantity=quantity)[0]
        return product, variant

    def test_inventory_updates(self):
        p1 = self.create_product_with_variant("P1", quantity=10)[1]
        p2 = self.create_product_with_variant("P2", quantity=3)[1]
        self.basket.add(p1, 5)
        self.basket.add(p2, 3)
        BasketStockReservation.create_and_reserve(self.basket)
        p1.refresh_from_db()
        p2.refresh_from_db()
        self.assertEqual(p1.available_quantity, 5)
        self.assertEqual(p2.available_quantity, 0)
        # Add more items and re-reserve basket
        self.basket.add(p1, 2)
        BasketStockReservation.create_and_reserve(self.basket)
        p1.refresh_from_db()
        p2.refresh_from_db()
        self.assertEqual(p1.available_quantity, 3)
        self.assertEqual(p2.available_quantity, 0)
        # Remove item and re-reserve basket
        item = self.basket.get_items()[1]
        self.basket.remove(item.ref)
        BasketStockReservation.create_and_reserve(self.basket)
        p2.refresh_from_db()
        self.assertEqual(p2.available_quantity, 3)
        # Un-reserve all items
        self.basket.refresh_from_db()
        self.basket.stock_reservation.unreserve_and_delete()
        p1.refresh_from_db()
        p2.refresh_from_db()
        self.assertEqual(p1.available_quantity, 10)
        self.assertEqual(p2.available_quantity, 3)

    def test_expired_reservation_revertes(self):
        p1 = self.create_product_with_variant("Product", quantity=3)[1]
        self.basket.add(p1, 1)
        BasketStockReservation.create_and_reserve(self.basket)

        # Another basket
        new_basket = Basket.objects.create()
        new_basket.add(p1, 2)
        BasketStockReservation.create_and_reserve(new_basket)

        p1.refresh_from_db()
        self.assertEqual(p1.available_quantity, 0)
        self.assertEquals(BasketStockReservation.unreserve_and_delete_expired(), 0)

        # Update reserved times to make it expired
        expired_time = timezone.now() - timezone.timedelta(
            seconds=BasketStockReservation.RESERVED_SECONDS + 1
        )
        self.basket.stock_reservation.reserved_at = expired_time
        self.basket.stock_reservation.save()
        new_basket.stock_reservation.reserved_at = expired_time
        new_basket.stock_reservation.save()
        self.assertEquals(BasketStockReservation.unreserve_and_delete_expired(), 2)

        p1.refresh_from_db()
        self.assertEqual(p1.available_quantity, 3)
        self.basket.refresh_from_db()
        with self.assertRaises(BasketStockReservation.DoesNotExist):
            self.basket.stock_reservation

    def test_reserve_and_commit(self):
        p1 = self.create_product_with_variant("Product", quantity=3)[1]
        self.basket.add(p1, 2)
        BasketStockReservation.reserve_and_commit(self.basket)
        p1.refresh_from_db()
        self.assertEqual(p1.available_quantity, 1)
