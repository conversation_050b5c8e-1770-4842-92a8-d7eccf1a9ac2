import pytest

# from django.conf import settings
# from django.utils import timezone
from salesman.orders.models import Order

# from shop.utils import generate_ref, get_unique_ref


@pytest.mark.django_db
def test_order_internal_reference_creates(test_request):
    for _ in range(5):
        order = Order.objects.create_from_request(test_request)
        assert order.internal_ref is not None


# @pytest.mark.django_db
# def test_reference_generator(test_request, monkeypatch):
#     year = timezone.now().year

#     def build_ref(num, year=year):
#         return f"{year}-{settings.SHOP_ORDER_REFERENCE_TWO_LETTER_PREFIX[:2]}{num:06d}"

#     last_order = Order.objects.filter(ref__isnull=False).first()
#     last_num = int(last_order.ref.split("-")[1][2:] if last_order else "000001")
#     new_last_num = 1

#     # Create 100 orders
#     for i in range(100):
#         ref = generate_ref(test_request)
#         Order.objects.create(ref=ref)
#         new_last_num = last_num + i
#         assert ref == build_ref(new_last_num)

#     # Test recursion when order with ref exists
#     expected_ref = build_ref(new_last_num + 1)
#     assert get_unique_ref(new_last_num, year) == expected_ref
#     assert get_unique_ref(new_last_num - 5, year) == expected_ref
#     assert get_unique_ref(new_last_num - 10, year) == expected_ref

#     # Test new year resets increment
#     new_year = year + 1
#     original_now = timezone.now

#     def new_year_now():
#         return original_now().replace(year=new_year)

#     monkeypatch.setattr(timezone, "now", new_year_now)

#     for i in range(10):
#         ref = generate_ref(test_request)
#         Order.objects.create(ref=ref)
#         assert ref == build_ref(i + 1, new_year)

#     # Test different last order ref format results in a fresh start.
#     # Next order should be 11 since we created 10 previously for new year.
#     Order.objects.create(ref=f"{new_year}-something-different")
#     ref = generate_ref(test_request)
#     assert ref == build_ref(11, new_year)
