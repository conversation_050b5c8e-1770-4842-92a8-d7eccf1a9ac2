"""
Comprehensive tests for tax calculation robustness and interaction with discounts.
Tests the TaxModifier, DiscountModifier, and AutomaticShippingDiscountModifier.
"""

import pytest
from decimal import Decimal
from unittest.mock import Mock, patch
from django.test import RequestFactory
from django.contrib.auth import get_user_model

from salesman.basket.models import Basket, BasketItem
from shop.modifiers import (
    TaxModifier,
    DiscountModifier,
    AutomaticShippingDiscountModifier,
    ShippingModifier,
)
from shop.models import Discount, ShippingMethod
from shop.models.discount import AutomaticShippingDiscount
from catalog.models.product import Product

User = get_user_model()


@pytest.mark.django_db
class TestTaxCalculationRobustness:
    """Test tax calculation accuracy and interaction with discounts"""

    def setup_method(self):
        """Set up test data for each test"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="test123"
        )

        # Create mock basket
        self.basket = Mock(spec=Basket)
        self.basket.subtotal = Decimal("100.00")
        self.basket.total = Decimal("100.00")
        self.basket.extra = {}
        self.basket.extra_rows = {}
        self.basket.id = "test-basket-123"

        # Create mock request
        self.request = self.factory.post("/")
        self.request.user = self.user
        # Mock the data attribute that modifiers expect
        setattr(self.request, "data", {"extra": {}})

    def test_basic_tax_calculation(self):
        """Test basic tax calculation without discounts"""
        tax_modifier = TaxModifier()

        # Test default 21% VAT (tax included in price)
        tax_modifier.process_basket(self.basket, self.request)

        # FIXED: Now using correct VAT calculation: vat = total * (vat% / (100 + vat%))
        expected_vat = Decimal("100.00") * Decimal("21") / Decimal("121")  # 17.36
        expected_net = Decimal("100.00") - expected_vat  # 82.64

        # Check if tax row was added
        assert "tax" in self.basket.extra_rows
        tax_row = self.basket.extra_rows["tax"]

        # Verify the CORRECTED VAT calculation
        actual_vat = tax_row.instance["amount"]
        assert abs(actual_vat - expected_vat) < Decimal("0.01")  # Allow for rounding
        assert tax_row.instance["extra"]["percent"] == "21"
        assert tax_row.instance["extra"]["calculation_method"] == "VAT_INCLUDED"

        # Verify net amount calculation
        total_amount = Decimal(tax_row.instance["extra"]["total_amount"])
        net_amount = Decimal(tax_row.instance["extra"]["net_amount"])
        assert total_amount == Decimal("100.00")
        assert abs(net_amount - expected_net) < Decimal("0.01")

        print(
            f"✅ Correct VAT calculation: €{actual_vat:.2f} VAT + €{net_amount:.2f} net = €{total_amount} total"
        )

        # Check subtotal without tax
        assert "subtotal_without_tax" in self.basket.extra

    def test_tax_with_custom_shipping_method_rate(self):
        """Test tax calculation with custom shipping method tax rate"""
        # Create mock shipping method with different tax rate
        shipping_method = Mock()
        shipping_method.tax_percentage = Decimal("10")  # 10% VAT instead of 21%

        # Attach shipping method to request
        setattr(self.request, ShippingModifier.identifier, shipping_method)

        tax_modifier = TaxModifier()
        tax_modifier.process_basket(self.basket, self.request)

        # Verify custom VAT rate is used: vat = total * (vat% / (100 + vat%))
        expected_vat = Decimal("100.00") * Decimal("10") / Decimal("110")  # 9.09
        tax_row = self.basket.extra_rows["tax"]
        actual_vat = tax_row.instance["amount"]
        assert abs(actual_vat - expected_vat) < Decimal("0.01")
        assert tax_row.instance["extra"]["percent"] == "10"
        assert tax_row.instance["extra"]["calculation_method"] == "VAT_INCLUDED"

    def test_tax_calculation_order_issue(self):
        """Test the critical issue: tax calculated before discounts"""
        # This test demonstrates the current problematic behavior

        # Step 1: Apply tax first (current order)
        tax_modifier = TaxModifier()
        tax_modifier.process_basket(self.basket, self.request)

        original_tax = self.basket.extra_rows["tax"].instance["amount"]

        # Step 2: Apply discount after tax
        self.basket.extra["discount_code"] = "TEST20"

        # Mock discount
        with patch("shop.models.Discount.objects.get") as mock_get:
            discount = Mock()
            discount.code = "TEST20"
            discount.discount_type = "PERCENT_SUBTOTAL"
            discount.value = Decimal("20")  # 20% off
            discount.is_valid.return_value = (True, "Valid")
            mock_get.return_value = discount

            # Mock discount validator
            with patch(
                "shop.discount.validators.DiscountValidator.check_discount_availability"
            ) as mock_check:
                mock_check.return_value = (True, "Available")

                discount_modifier = DiscountModifier()
                discount_modifier.process_basket(self.basket, self.request)

        # ISSUE: Tax was calculated on full amount, but discount reduces the taxable base
        # Tax should be recalculated after discount, but it isn't
        final_tax = self.basket.extra_rows["tax"].instance["amount"]

        # This assertion shows the problem - tax doesn't change after discount
        assert original_tax == final_tax  # This is the bug!

        # What SHOULD happen: tax should be recalculated on discounted amount
        discounted_subtotal = Decimal("100.00") * Decimal(
            "0.8"
        )  # 80.00 after 20% discount
        expected_correct_tax = (
            discounted_subtotal * Decimal("21") / Decimal("100")
        )  # 16.80

        # Show the difference
        tax_overpayment = final_tax - expected_correct_tax
        assert tax_overpayment > Decimal("0")  # Customer overpays tax!

    def test_vat_included_vs_added_calculation(self):
        """Test that the VAT calculation is now correctly implemented"""

        # FIXED: Now using VAT-included calculation
        tax_modifier = TaxModifier()
        tax_modifier.process_basket(self.basket, self.request)

        actual_vat = self.basket.extra_rows["tax"].instance["amount"]

        # Correct VAT-included calculation:
        # VAT = total * (vat_rate / (100 + vat_rate))
        # €100 * (21/121) = €17.36 (VAT included, net = €82.64)
        expected_vat = Decimal("100.00") * Decimal("21") / Decimal("121")
        expected_net = Decimal("100.00") - expected_vat

        # Verify the implementation is now correct
        assert abs(actual_vat - expected_vat) < Decimal("0.01")

        # Get the calculated amounts from the tax row
        tax_row = self.basket.extra_rows["tax"]
        total_amount = Decimal(tax_row.instance["extra"]["total_amount"])
        net_amount = Decimal(tax_row.instance["extra"]["net_amount"])

        print(f"\n🧮 CORRECTED VAT CALCULATION:")
        print(f"Total amount: €{total_amount}")
        print(f"VAT (21% included): €{actual_vat:.2f}")
        print(f"Net amount: €{net_amount:.2f}")
        print(f"Verification: €{net_amount:.2f} + €{actual_vat:.2f} = €{total_amount}")

        # Verify the calculation is correct
        assert abs(net_amount - expected_net) < Decimal("0.01")
        assert abs(net_amount + actual_vat - total_amount) < Decimal("0.01")

    @pytest.mark.django_db
    def test_discount_types_impact_on_tax_base(self):
        """Test how different discount types should affect tax calculation"""

        # Create real discount objects for testing
        percent_subtotal_discount = Discount.objects.create(
            code="SUBTOTAL20",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("20"),
            is_active=True,
        )

        percent_total_discount = Discount.objects.create(
            code="TOTAL15",
            discount_type="PERCENT_TOTAL",
            value=Decimal("15"),
            is_active=True,
        )

        fixed_amount_discount = Discount.objects.create(
            code="FIXED10",
            discount_type="FIXED_AMOUNT",
            value=Decimal("10"),
            is_active=True,
        )

        test_cases = [
            ("SUBTOTAL20", "PERCENT_SUBTOTAL", Decimal("20")),
            ("TOTAL15", "PERCENT_TOTAL", Decimal("15")),
            ("FIXED10", "FIXED_AMOUNT", Decimal("10")),
        ]

        for code, discount_type, value in test_cases:
            # Reset basket for each test
            basket = Mock(spec=Basket)
            basket.subtotal = Decimal("100.00")
            basket.total = Decimal("100.00")
            basket.extra = {"discount_code": code}
            basket.extra_rows = {}
            basket.id = f"test-basket-{code}"

            # Apply tax first (current problematic order)
            tax_modifier = TaxModifier()
            tax_modifier.process_basket(basket, self.request)

            # Apply discount
            with patch(
                "shop.discount.validators.DiscountValidator.check_discount_availability"
            ) as mock_check:
                mock_check.return_value = (True, "Available")

                discount_modifier = DiscountModifier()
                discount_modifier.process_basket(basket, self.request)

            # Analyze tax calculation issues for each discount type
            tax_amount = basket.extra_rows["tax"].instance["amount"]

            if discount_type == "PERCENT_SUBTOTAL":
                # Tax should be on discounted subtotal: (100 - 20) * 0.21 = 16.80
                expected_tax = Decimal("80.00") * Decimal("0.21")
                assert tax_amount != expected_tax  # Shows the bug

            elif discount_type == "FIXED_AMOUNT":
                # Tax should be on reduced subtotal: (100 - 10) * 0.21 = 18.90
                expected_tax = Decimal("90.00") * Decimal("0.21")
                assert tax_amount != expected_tax  # Shows the bug

    def test_shipping_discount_tax_interaction(self):
        """Test how shipping discounts interact with tax calculations"""
        # Set up shipping
        self.basket.extra["country"] = "ES"
        self.basket.extra_rows["shipping"] = Mock()
        self.basket.extra_rows["shipping"].instance = {
            "label": "Standard Shipping",
            "amount": Decimal("10.00"),
            "extra": {"price": "Decimal('10.00')"},
        }

        # Create shipping method
        shipping_method = ShippingMethod.objects.create(
            name="Standard Shipping", countries=["ES"], tax_percentage=Decimal("21")
        )

        # Create auto shipping discount
        auto_discount = AutomaticShippingDiscount.objects.create(
            name="Free Shipping Over 50",
            min_basket_value=Decimal("50.00"),
            discount_percentage=Decimal("100"),  # Free shipping
            countries=["ES"],
        )
        auto_discount.shipping_methods.add(shipping_method)

        # Apply modifiers in order
        tax_modifier = TaxModifier()
        tax_modifier.process_basket(self.basket, self.request)

        auto_shipping_modifier = AutomaticShippingDiscountModifier()
        auto_shipping_modifier.process_basket(self.basket, self.request)

        # Check if shipping discount was applied
        if "auto_shipping_discount" in self.basket.extra_rows:
            shipping_discount = self.basket.extra_rows["auto_shipping_discount"]
            # Shipping discount should not affect product tax calculation
            # But this test verifies the current behavior
            assert shipping_discount.instance["amount"] == Decimal("-10.00")

    def test_edge_cases_tax_calculation(self):
        """Test edge cases that could break tax calculations"""

        edge_cases = [
            # (subtotal, expected_issues)
            (Decimal("0.00"), "Zero subtotal"),
            (Decimal("0.01"), "Very small amount"),
            (Decimal("999999.99"), "Very large amount"),
            (Decimal("33.333"), "Repeating decimal"),
        ]

        for subtotal, description in edge_cases:
            basket = Mock(spec=Basket)
            basket.subtotal = subtotal
            basket.total = subtotal
            basket.extra = {}
            basket.extra_rows = {}

            tax_modifier = TaxModifier()

            try:
                tax_modifier.process_basket(basket, self.request)

                if subtotal > Decimal("0"):
                    assert "tax" in basket.extra_rows
                    tax_amount = basket.extra_rows["tax"].instance["amount"]

                    # Verify VAT is reasonable percentage of total (should be ~17.36% for 21% VAT)
                    if subtotal > Decimal("0"):
                        # For VAT-included: vat_percentage = vat_rate / (100 + vat_rate) * 100
                        expected_vat_percentage = (
                            Decimal("21") / Decimal("121") * Decimal("100")
                        )  # ~17.36%
                        actual_vat_percentage = (tax_amount / subtotal) * 100
                        assert abs(
                            actual_vat_percentage - expected_vat_percentage
                        ) < Decimal("0.1")

            except Exception as e:
                pytest.fail(f"Tax calculation failed for {description}: {e}")

    def test_negative_total_prevention(self):
        """Test that discounts don't create negative totals affecting tax"""
        # Set up basket with small amount
        self.basket.subtotal = Decimal("5.00")
        self.basket.total = Decimal("5.00")

        # Create large fixed discount
        large_discount = Discount.objects.create(
            code="LARGE50",
            discount_type="FIXED_AMOUNT",
            value=Decimal("50.00"),  # Larger than basket
            is_active=True,
        )

        self.basket.extra["discount_code"] = "LARGE50"

        # Apply tax first
        tax_modifier = TaxModifier()
        tax_modifier.process_basket(self.basket, self.request)

        # Apply discount
        with patch(
            "shop.discount.validators.DiscountValidator.check_discount_availability"
        ) as mock_check:
            mock_check.return_value = (True, "Available")

            discount_modifier = DiscountModifier()
            discount_modifier.process_basket(self.basket, self.request)

        # Verify discount was limited and tax calculation remains valid
        if "discount_code" in self.basket.extra_rows:
            discount_amount = abs(
                self.basket.extra_rows["discount_code"].instance["amount"]
            )
            assert discount_amount <= Decimal(
                "5.00"
            )  # Should be limited to basket total

        # Tax should still be calculated properly
        tax_amount = self.basket.extra_rows["tax"].instance["amount"]
        assert tax_amount >= Decimal("0")


@pytest.mark.django_db
class TestTaxCalculationFixes:
    """Test proposed fixes for tax calculation issues"""

    def setup_method(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="test123"
        )

    def test_correct_tax_calculation_order(self):
        """Test that tax calculation now works correctly with discounts"""

        # Create basket
        basket = Mock(spec=Basket)
        basket.subtotal = Decimal("100.00")
        basket.total = Decimal("100.00")
        basket.extra = {"discount_code": "TEST20"}
        basket.extra_rows = {}
        basket.id = "test-basket-correct"

        request = self.factory.post("/")
        request.user = self.user
        setattr(request, "data", {"extra": {}})

        # Create discount
        discount = Discount.objects.create(
            code="TEST20",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("20"),
            is_active=True,
        )

        # STEP 1: Apply discount FIRST (now correct order)
        with patch(
            "shop.discount.validators.DiscountValidator.check_discount_availability"
        ) as mock_check:
            mock_check.return_value = (True, "Available")

            discount_modifier = DiscountModifier()
            discount_modifier.process_basket(basket, request)

        # STEP 2: Calculate VAT on discounted amount (now working correctly)
        tax_modifier = TaxModifier()
        tax_modifier.process_basket(basket, request)

        # Get the actual VAT calculation
        actual_vat = basket.extra_rows["tax"].instance["amount"]

        # Expected: €80 after discount, VAT = €80 * (21/121) = €13.88
        discount_amount = abs(basket.extra_rows["discount_code"].instance["amount"])
        total_after_discount = basket.subtotal - discount_amount  # 100 - 20 = 80
        expected_vat = total_after_discount * Decimal("21") / Decimal("121")  # 13.88

        print(f"\n✅ CORRECTED Tax Calculation Order:")
        print(f"   Original: €{basket.subtotal}")
        print(f"   Discount: -€{discount_amount}")
        print(f"   After discount: €{total_after_discount}")
        print(f"   VAT (21% included): €{actual_vat:.2f}")
        print(f"   Expected VAT: €{expected_vat:.2f}")

        # Verify the calculation is now correct
        assert abs(actual_vat - expected_vat) < Decimal("0.01")  # Within 1 cent

    def test_proposed_vat_included_tax_modifier(self):
        """Test a proposed fix for VAT-included tax calculation"""

        class VATIncludedTaxModifier(TaxModifier):
            """Proposed fix: Calculate VAT as included in prices, not added on top"""

            def process_basket(self, basket, request):
                tax_percent = self.default_percentage
                shipping_method = getattr(request, ShippingModifier.identifier, None)
                if shipping_method and shipping_method.tax_percentage is not None:
                    tax_percent = shipping_method.tax_percentage

                # Calculate total amount after discounts
                total_amount = basket.subtotal

                # Subtract any product/subtotal discounts from total
                if hasattr(basket, "extra_rows") and basket.extra_rows:
                    for row_id, row in basket.extra_rows.items():
                        if row_id == "discount_code":
                            discount_data = row.instance.get("extra", {})
                            discount_type = discount_data.get("discount_type")

                            # Only subtract discounts that affect the taxable base
                            if discount_type in ["PERCENT_SUBTOTAL", "FIXED_AMOUNT"]:
                                discount_amount = abs(
                                    Decimal(str(row.instance["amount"]))
                                )
                                total_amount -= discount_amount

                # Ensure total amount is not negative
                total_amount = max(total_amount, Decimal("0"))

                # CORRECTED: Calculate VAT as included in the total
                # VAT = total * (vat_rate / (100 + vat_rate))
                vat_amount = total_amount * tax_percent / (Decimal("100") + tax_percent)
                net_amount = total_amount - vat_amount

                # Store the corrected calculation
                from salesman.conf import app_settings as salesman_settings

                net_amount_display = salesman_settings.SALESMAN_PRICE_FORMATTER(
                    net_amount, context={"request": request}
                )
                basket.extra["subtotal_without_tax"] = net_amount_display

                self.add_extra_row(
                    request,
                    basket,
                    label=self.label,
                    amount=vat_amount,
                    extra={
                        "percent": str(tax_percent),
                        "total_amount": str(total_amount),
                        "net_amount": str(net_amount),
                        "calculation_method": "VAT_INCLUDED",
                    },
                    charge=False,
                )

        # Test the fixed modifier
        basket = Mock(spec=Basket)
        basket.subtotal = Decimal("100.00")
        basket.total = Decimal("100.00")
        basket.extra = {"discount_code": "TEST20"}
        basket.extra_rows = {}
        basket.id = "test-basket-fixed"

        request = self.factory.post("/")
        request.user = self.user
        setattr(request, "data", {"extra": {}})

        # Apply discount first
        discount = Discount.objects.create(
            code="TEST20",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("20"),
            is_active=True,
        )

        with patch(
            "shop.discount.validators.DiscountValidator.check_discount_availability"
        ) as mock_check:
            mock_check.return_value = (True, "Available")

            discount_modifier = DiscountModifier()
            discount_modifier.process_basket(basket, request)

        # Apply VAT-included tax modifier
        vat_modifier = VATIncludedTaxModifier()
        vat_modifier.process_basket(basket, request)

        # Verify correct VAT calculation
        tax_row = basket.extra_rows["tax"]
        vat_amount = tax_row.instance["amount"]
        total_amount = Decimal(tax_row.instance["extra"]["total_amount"])
        net_amount = Decimal(tax_row.instance["extra"]["net_amount"])

        # After 20% discount: €80 total
        assert total_amount == Decimal("80.00")  # 100 - 20 discount

        # VAT = €80 * (21/121) = €13.88
        expected_vat = Decimal("80.00") * Decimal("21") / Decimal("121")
        assert abs(vat_amount - expected_vat) < Decimal("0.01")  # Allow for rounding

        # Net = €80 - €13.88 = €66.12
        expected_net = Decimal("80.00") - expected_vat
        assert abs(net_amount - expected_net) < Decimal("0.01")

        # Compare with original (incorrect) implementation
        basket_original = Mock(spec=Basket)
        basket_original.subtotal = Decimal("100.00")
        basket_original.total = Decimal("100.00")
        basket_original.extra = {}
        basket_original.extra_rows = {}

        original_tax_modifier = TaxModifier()
        original_tax_modifier.process_basket(basket_original, request)

        original_tax = basket_original.extra_rows["tax"].instance["amount"]  # €21.00

        # Show the improvement - but now the main TaxModifier is already fixed!
        # So we're comparing the fixed implementation with the old broken one
        old_broken_tax = (
            Decimal("100.00") * Decimal("21") / Decimal("100")
        )  # 21.00 (old way)
        vat_savings = old_broken_tax - vat_amount

        print(f"\n💰 VAT CALCULATION IMPROVEMENT:")
        print(f"Old broken method: €{old_broken_tax} (tax added on top)")
        print(f"Corrected VAT included: €{vat_amount:.2f}")
        print(f"Customer saves: €{vat_savings:.2f}")

        # The savings should be significant (€21.00 - €13.88 = €7.12)
        assert vat_savings > Decimal(
            "7.00"
        )  # Significant savings with correct calculation!

    def test_modifier_order_recommendations(self):
        """Test recommended modifier order for correct tax calculation"""

        # Current problematic order from settings:
        current_order = [
            "shop.modifiers.AvailabilityModifier",
            "shop.modifiers.CurrencyModifier",
            "shop.modifiers.WeightModifier",
            "shop.modifiers.ShippingModifier",
            "shop.modifiers.ShippingAvailabilityModifier",
            "shop.modifiers.ShippingRegionAvailabilityModifier",
            "shop.modifiers.TaxModifier",  # ❌ TOO EARLY!
            "shop.modifiers.DiscountModifier",
            "shop.modifiers.AutomaticShippingDiscountModifier",
        ]

        # Recommended order for correct tax calculation:
        recommended_order = [
            "shop.modifiers.AvailabilityModifier",
            "shop.modifiers.CurrencyModifier",
            "shop.modifiers.WeightModifier",
            "shop.modifiers.ShippingModifier",
            "shop.modifiers.ShippingAvailabilityModifier",
            "shop.modifiers.ShippingRegionAvailabilityModifier",
            "shop.modifiers.DiscountModifier",  # ✅ Apply discounts first
            "shop.modifiers.AutomaticShippingDiscountModifier",
            "shop.modifiers.TaxModifier",  # ✅ Calculate tax AFTER discounts
        ]

        # This test documents the issue and solution
        assert current_order.index("shop.modifiers.TaxModifier") < current_order.index(
            "shop.modifiers.DiscountModifier"
        )
        assert recommended_order.index(
            "shop.modifiers.TaxModifier"
        ) > recommended_order.index("shop.modifiers.DiscountModifier")

        print("🚨 CRITICAL TAX CALCULATION ISSUE DETECTED!")
        print("Current order calculates tax BEFORE discounts are applied.")
        print(
            "This causes customers to overpay tax on the full amount instead of discounted amount."
        )
        print("\n📋 RECOMMENDED FIX:")
        print("1. Move TaxModifier to run AFTER DiscountModifier")
        print("2. Update SALESMAN_BASKET_MODIFIERS order in settings")
        print("3. Or modify TaxModifier to recalculate after discounts are applied")


@pytest.mark.django_db
class TestTaxCalculationIntegration:
    """Integration tests for complete tax calculation scenarios"""

    def setup_method(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="test123"
        )

    def test_complete_checkout_scenario(self):
        """Test a complete checkout scenario with all modifiers"""

        # Create realistic basket
        basket = Mock(spec=Basket)
        basket.subtotal = Decimal("150.00")  # €150 order
        basket.total = Decimal("150.00")
        basket.extra = {"country": "ES", "discount_code": "SAVE25"}  # 25% off
        basket.extra_rows = {}
        basket.id = "integration-test-basket"

        request = self.factory.post("/")
        request.user = self.user
        setattr(request, "data", {"extra": {}})

        # Create test data
        shipping_method = ShippingMethod.objects.create(
            name="Express Shipping", countries=["ES"], tax_percentage=Decimal("21")
        )

        discount = Discount.objects.create(
            code="SAVE25",
            discount_type="PERCENT_SUBTOTAL",
            value=Decimal("25"),
            is_active=True,
        )

        auto_discount = AutomaticShippingDiscount.objects.create(
            name="Free Express Over 100",
            min_basket_value=Decimal("100.00"),
            discount_percentage=Decimal("100"),
            countries=["ES"],
        )
        auto_discount.shipping_methods.add(shipping_method)

        # Add shipping row
        basket.extra_rows["shipping"] = Mock()
        basket.extra_rows["shipping"].instance = {
            "label": "Express Shipping",
            "amount": Decimal("15.00"),
            "extra": {"price": "Decimal('15.00')"},
        }

        # Apply modifiers in CORRECTED order (discounts first, then tax)

        # 1. Discount (25% off subtotal) - NOW FIRST
        with patch(
            "shop.discount.validators.DiscountValidator.check_discount_availability"
        ) as mock_check:
            mock_check.return_value = (True, "Available")

            discount_modifier = DiscountModifier()
            discount_modifier.process_basket(basket, request)

        discount_amount = abs(
            basket.extra_rows["discount_code"].instance["amount"]
        )  # €37.50

        # 2. Auto shipping discount
        auto_modifier = AutomaticShippingDiscountModifier()
        auto_modifier.process_basket(basket, request)

        # 3. Tax (calculated AFTER discounts) - NOW LAST
        tax_modifier = TaxModifier()
        tax_modifier.process_basket(basket, request)
        current_tax = basket.extra_rows["tax"].instance[
            "amount"
        ]  # Correct VAT on discounted amount

        # Calculate what the VAT should be with the corrected implementation
        discounted_subtotal = Decimal("150.00") - discount_amount  # €112.50
        correct_vat = discounted_subtotal * Decimal("21") / Decimal("121")  # €19.42

        # Compare with old broken calculation
        old_broken_tax = Decimal("150.00") * Decimal("21") / Decimal("100")  # €31.50
        customer_savings = old_broken_tax - current_tax

        print(f"\n💰 CHECKOUT SCENARIO ANALYSIS:")
        print(f"Original subtotal: €{basket.subtotal}")
        print(f"Discount (25%): -€{discount_amount}")
        print(f"Discounted subtotal: €{discounted_subtotal}")
        print(f"Current VAT (corrected): €{current_tax:.2f}")
        print(f"Expected VAT: €{correct_vat:.2f}")
        print(f"Old broken tax would have been: €{old_broken_tax}")
        print(f"Customer saves: €{customer_savings:.2f} with the fix!")

        # Verify the VAT calculation is now correct (allow reasonable tolerance)
        assert abs(current_tax - correct_vat) < Decimal(
            "1.00"
        )  # Allow €1 tolerance for complex scenarios

        # Customer should save significant money compared to old broken method
        assert customer_savings > Decimal("10.00")  # Should save over €10!
