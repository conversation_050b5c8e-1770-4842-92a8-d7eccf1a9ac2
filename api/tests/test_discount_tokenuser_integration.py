"""
Tests for discount system integration with TokenUser objects.
Ensures that JWT authentication with TokenUser objects works properly
with discount validation and usage tracking.
"""

import pytest
from decimal import Decimal
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from unittest.mock import Mock, patch

from shop.models.discount import Discount, DiscountUsage, track_discount_usage
from shop.discount.validators import DiscountValidator
from user.models import TokenUser

User = get_user_model()


class MockTokenUser:
    """Mock TokenUser for testing"""

    def __init__(self, user_id, email="<EMAIL>", is_authenticated=True):
        self.id = user_id
        self.email = email
        self.is_authenticated = is_authenticated
        self.token = {
            "user_id": user_id,
            "email": email,
            "first_name": "Test",
            "last_name": "User",
        }

    def to_user(self):
        """Convert to actual User instance"""
        try:
            return User.objects.get(id=self.id)
        except User.DoesNotExist:
            return None


class MockTokenUserNoConversion:
    """Mock TokenUser that fails conversion"""

    def __init__(self, user_id, email="<EMAIL>", is_authenticated=True):
        self.id = user_id
        self.email = email
        self.is_authenticated = is_authenticated

    def to_user(self):
        """Always returns None to simulate conversion failure"""
        return None


@pytest.mark.django_db
class DiscountTokenUserIntegrationTest(TestCase):
    """Test discount system with TokenUser objects"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        self.discount = Discount.objects.create(
            code="TEST10",
            description="Test discount",
            discount_type="percentage",
            value=Decimal("10.00"),
            max_uses=5,
            max_uses_per_user=2,
            min_basket_value=Decimal("50.00"),
            is_active=True,
        )

        self.token_user = MockTokenUser(self.user.id, self.user.email)
        self.token_user_no_conversion = MockTokenUserNoConversion(
            self.user.id, self.user.email
        )

    def test_discount_validation_with_token_user(self):
        """Test that discount validation works with TokenUser objects"""
        # Test with valid TokenUser
        is_valid, message = self.discount.is_valid(
            basket_value=Decimal("100.00"), user=self.token_user
        )

        self.assertTrue(is_valid)
        self.assertEqual(message, "Valid discount code")

    def test_discount_validation_with_token_user_max_uses_per_user(self):
        """Test per-user limits work with TokenUser objects"""
        # Create usage records for the user
        DiscountUsage.objects.create(
            discount=self.discount, user=self.user, order_id="order1", amount="10.00"
        )
        DiscountUsage.objects.create(
            discount=self.discount, user=self.user, order_id="order2", amount="10.00"
        )

        # Should fail because user has reached max_uses_per_user (2)
        is_valid, message = self.discount.is_valid(
            basket_value=Decimal("100.00"), user=self.token_user
        )

        self.assertFalse(is_valid)
        self.assertIn("already used this code the maximum number of times", message)

    def test_discount_validation_with_failed_token_conversion(self):
        """Test graceful handling when TokenUser.to_user() fails"""
        is_valid, message = self.discount.is_valid(
            basket_value=Decimal("100.00"), user=self.token_user_no_conversion
        )

        # Should still allow discount but skip per-user validation
        self.assertTrue(is_valid)
        self.assertEqual(message, "Valid discount code")

    def test_discount_validator_with_token_user(self):
        """Test DiscountValidator works with TokenUser objects"""
        basket_id = "basket123"

        # Reserve discount
        available, message = DiscountValidator.check_discount_availability(
            self.discount.code, basket_id
        )
        self.assertTrue(available)

        # Confirm usage with TokenUser
        success = DiscountValidator.confirm_discount_usage(
            self.discount.code, basket_id, user=self.token_user
        )
        self.assertTrue(success)

        # Check that usage was recorded
        usage_count = DiscountUsage.objects.filter(
            discount=self.discount, user=self.user
        ).count()
        self.assertEqual(usage_count, 1)

    def test_discount_validator_with_failed_token_conversion(self):
        """Test DiscountValidator handles failed TokenUser conversion"""
        basket_id = "basket456"

        # Reserve discount
        available, message = DiscountValidator.check_discount_availability(
            self.discount.code, basket_id
        )
        self.assertTrue(available)

        # Confirm usage with TokenUser that fails conversion
        success = DiscountValidator.confirm_discount_usage(
            self.discount.code, basket_id, user=self.token_user_no_conversion
        )
        self.assertTrue(success)

        # Check that no usage record was created (graceful failure)
        usage_count = DiscountUsage.objects.filter(discount=self.discount).count()
        self.assertEqual(usage_count, 0)

        # But discount usage counter should still be incremented
        self.discount.refresh_from_db()
        self.assertEqual(self.discount.times_used, 1)

    def test_discount_tracking_with_token_user(self):
        """Test discount tracking function with TokenUser objects"""
        from shop.models.discount import track_discount_usage

        # Mock order object
        mock_order = Mock()
        mock_order.id = "order123"
        mock_order.user = self.token_user
        mock_order._basket_id = "basket123"
        mock_order.extra_rows = [
            {
                "modifier": "discount",
                "amount": Decimal("10.00"),
                "extra": {"discount_code": self.discount.code},
            }
        ]

        # Track usage (this will use fallback method since Redis may not be available in tests)
        track_discount_usage(sender=None, order=mock_order, new_status="PROCESSING")

        # Check that usage was recorded (either via main method or fallback)
        usage_count = DiscountUsage.objects.filter(
            discount=self.discount, user=self.user, order_id="order123"
        ).count()

        # Refresh discount to check times_used
        self.discount.refresh_from_db()

        # Either a usage record was created OR times_used was incremented (fallback)
        self.assertTrue(
            usage_count == 1 or self.discount.times_used > 0,
            f"Expected usage record or times_used increment. Got usage_count={usage_count}, times_used={self.discount.times_used}",
        )

    def test_discount_tracking_with_failed_token_conversion(self):
        """Test discount tracking handles failed TokenUser conversion"""
        from shop.models.discount import track_discount_usage

        # For this test, we'll test the fallback method directly
        # by not setting up a reservation, which will cause the main method to fail
        # and trigger the fallback

        # Mock order object with TokenUser that fails conversion
        mock_order = Mock()
        mock_order.id = "order456"
        mock_order.user = self.token_user_no_conversion
        mock_order._basket_id = "basket456"  # Add explicit basket_id
        mock_order.extra_rows = [
            {
                "modifier": "discount",
                "amount": Decimal("10.00"),
                "extra": {"discount_code": self.discount.code},
            }
        ]

        # Track usage - should not raise error
        track_discount_usage(sender=None, order=mock_order, new_status="PROCESSING")

        # Check that no usage record was created (because TokenUser conversion failed)
        # But the discount times_used should be incremented
        self.discount.refresh_from_db()
        self.assertEqual(self.discount.times_used, 1)

        # And no DiscountUsage record should exist (because user conversion failed)
        usage_count = DiscountUsage.objects.filter(
            discount=self.discount, order_id="order456"
        ).count()
        self.assertEqual(usage_count, 0)

    def test_real_token_user_integration(self):
        """Test with actual TokenUser class if available"""
        try:
            # Create a real TokenUser instance
            token_data = {
                "user_id": self.user.id,
                "email": self.user.email,
                "first_name": "Test",
                "last_name": "User",
            }

            real_token_user = TokenUser(token_data)

            # Test discount validation
            is_valid, message = self.discount.is_valid(
                basket_value=Decimal("100.00"), user=real_token_user
            )

            self.assertTrue(is_valid)
            self.assertEqual(message, "Valid discount code")

        except Exception as e:
            # Skip if TokenUser can't be instantiated in test environment
            self.skipTest(f"Could not test with real TokenUser: {e}")


@pytest.mark.django_db
class DiscountErrorPreventionTest(TestCase):
    """Tests to prevent regression of TokenUser errors"""

    def test_no_tokenuser_in_database_queries(self):
        """Ensure TokenUser objects are never used directly in DB queries"""
        user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        discount = Discount.objects.create(
            code="TEST20",
            description="Test discount",
            discount_type="percentage",
            value=Decimal("20.00"),
            max_uses_per_user=1,
            is_active=True,
        )

        # Create a mock TokenUser that would cause the original error
        class BadTokenUser:
            def __init__(self):
                self.id = "not_a_number"  # This would cause the original error
                self.is_authenticated = True

            def to_user(self):
                return user

        bad_token_user = BadTokenUser()

        # This should not raise an error anymore
        try:
            is_valid, _ = discount.is_valid(user=bad_token_user)
            # Should succeed because to_user() returns valid user
            self.assertTrue(is_valid)
        except Exception as e:
            self.fail(f"Discount validation failed with TokenUser: {e}")


if __name__ == "__main__":
    pytest.main([__file__])
