steps:
  - id: Build
    name: gcr.io/cloud-builders/docker
    args:
      - 'build'
      - '-t'
      - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'
      - '.'
    dir: api

  - id: Push
    name: gcr.io/cloud-builders/docker
    args:
      - 'push'
      - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'

  - id: 'Apply Migrations'
    name: 'gcr.io/google-appengine/exec-wrapper'
    args:
      - '-i'
      - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'
      - '-e'
      - 'DJANGO_SETTINGS_MODULE=$_DJANGO_SETTINGS_MODULE'
      - '-e'
      - 'DJANGO_SETTINGS_SECRET=$_DJANGO_SETTINGS_SECRET'
      - '-e'
      - 'DJANGO_ADMIN_PASSWORD_SECRET=$_DJANGO_ADMIN_PASSWORD_SECRET'
      - '-e'
      - 'DEBUG_TOOLBAR=$_DEBUG_TOOLBAR'
      - '--'
      - 'bash'
      - '-c'
      - 'python manage.py migrate && python manage.py createcachetable && python manage.py update_index'

  - id: 'Collect Static'
    name: 'gcr.io/google-appengine/exec-wrapper'
    args:
      - '-i'
      - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'
      - '-e'
      - 'DJANGO_SETTINGS_MODULE=$_DJANGO_SETTINGS_MODULE'
      - '-e'
      - 'DJANGO_SETTINGS_SECRET=$_DJANGO_SETTINGS_SECRET'
      - '-e'
      - 'DEBUG_TOOLBAR=$_DEBUG_TOOLBAR'
      - '--'
      - 'python'
      - 'manage.py'
      - 'collectstatic'
      - '--no-input'

  - id: Deploy
    name: gcr.io/google.com/cloudsdktool/cloud-sdk
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '$_SERVICE_NAME'
      - '--platform=managed'
      - '--region=$_REGION'
      - '--image=$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'
      - '--cpu=$_CPU'
      - '--memory=$_MEMORY'
      - '--min-instances=$_MIN_INSTANCES'
      - '--max-instances=$_MAX_INSTANCES'
      - '--timeout=$_TIMEOUT'
      - '--concurrency=$_CONCURRENCY'
      - '--vpc-connector'
      - '$_VPC_CONNECTOR'
      - '--allow-unauthenticated'
      - '--set-env-vars'
      - 'WORKERS=$_WORKERS'
      - '--set-env-vars'
      - 'WORKER_CONNECTIONS=$_WORKER_CONNECTIONS'
      - '--set-env-vars'
      - 'REDIS_MAX_CONNS=$_REDIS_MAX_CONNS'
      - '--set-env-vars'
      - 'DJANGO_SETTINGS_MODULE=$_DJANGO_SETTINGS_MODULE'
      - '--set-env-vars'
      - 'DJANGO_SETTINGS_SECRET=$_DJANGO_SETTINGS_SECRET'
      - '--set-env-vars'
      - 'DEBUG_TOOLBAR=$_DEBUG_TOOLBAR'

  - id: Update Traffic
    name: gcr.io/google.com/cloudsdktool/cloud-sdk
    entrypoint: gcloud
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '$_SERVICE_NAME'
      - '--region=$_REGION'
      - '--to-latest'

images:
  - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'

options:
  substitutionOption: ALLOW_LOOSE
  pool:
    name: projects/$PROJECT_ID/locations/europe-west4/workerPools/leeosmerch
  logging: CLOUD_LOGGING_ONLY

substitutions:
  _SERVICE_NAME: leeos-merch-api
  _IMAGE_NAME: leeos-merch-api
  _DB_INSTANCE: leeos-merch-db
  _VPC_CONNECTOR: leeos-merch-vpc
  _REGION: 'europe-west4'
  _GCR_HOSTNAME: europe-docker.pkg.dev
  _AR_REPOSITORY: eu.gcr.io
  _TIMEOUT: '180'
  _CPU: '1'
  _MEMORY: '512Mi'
  # Max concurent users 1000*80 = 80,000
  _MIN_INSTANCES: '3'
  _MAX_INSTANCES: '1000'
  _CONCURRENCY: '80'
  # Container environment vars
  _WORKERS: '1'
  _WORKER_CONNECTIONS: '80'
  _REDIS_MAX_CONNS: '50' # 50*1000 = 50,000 Redis max_connections = 65,000
  _DJANGO_SETTINGS_MODULE: project.settings.production
  _DJANGO_SETTINGS_SECRET: leeos-merch-django-settings
  _DJANGO_ADMIN_PASSWORD_SECRET: leeos-merch-django-admin-password
  _DEBUG_TOOLBAR: 'False'

tags:
  - leeos-merch

timeout: 12000s
