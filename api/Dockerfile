FROM python:3.9-slim

WORKDIR /usr/src/app

ENV PYTHONUNBUFFERED=1

# Install server requirements.
RUN apt-get update && apt-get install -y libpq-dev gcc wkhtmltopdf

# Install poetry
RUN pip install --no-cache-dir poetry==2.1.3
RUN poetry config virtualenvs.create false

# Install app requirements.
COPY pyproject.toml poetry.lock ./
RUN poetry install --without dev --no-root

COPY . .

ENV HOST=0.0.0.0
ENV PORT=8080

# For Cloud Run 1 worker should be used per-CPU
ENV WORKERS=1

# Set to Cloud Run concurency value
ENV WORKER_CONNECTIONS=80

# Max connections the DB and Redis from container instance, depending on Cloud Run max_instances,
# Cloud SQL max_connections and Redis max_connections values, set appropriately to not overflow the connections.
ENV REDIS_MAX_CONNS=80

# DB max conns setting disabled on a per-container base and moved to external pgbouncer instance for this project.
# ENV DB_MAX_CONNS=8

EXPOSE $PORT

CMD ["gunicorn", "-c", "python:gunicorn_config", "project.wsgi:application"]
