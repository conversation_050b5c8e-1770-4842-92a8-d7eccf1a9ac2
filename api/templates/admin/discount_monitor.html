{% extends "wagtailadmin/base.html" %}
{% load wagtailadmin_tags %}

{% block titletag %}{{ title }}{% endblock %}

{% block content %}
<header class="header">
    <div class="row">
        <div class="left">
            <div class="col">
                <h1 class="header-title">{{ title }}</h1>
            </div>
        </div>
        <div class="right">
            <div class="col">
                <a href="{% url 'discount_modeladmin_index' %}" class="button">
                    Back to Discount Codes
                </a>
            </div>
        </div>
    </div>
</header>

<div class="content-wrapper">
    <div class="content">
        {% if discounts_data %}
            <div class="nice-padding">
                <h2>Active Limited Discount Codes</h2>
                <p>Real-time monitoring of discount code usage and Redis reservations.</p>
                
                <table class="listing">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Type</th>
                            <th>Value</th>
                            <th>Max Uses</th>
                            <th>Times Used</th>
                            <th>Reserved</th>
                            <th>Total Usage</th>
                            <th>Remaining</th>
                            <th>Usage %</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for data in discounts_data %}
                            <tr>
                                <td><strong>{{ data.discount.code }}</strong></td>
                                <td>{{ data.discount.get_discount_type_display }}</td>
                                <td>{{ data.discount.value }}{% if data.discount.discount_type|slice:":7" == "PERCENT" %}%{% else %}€{% endif %}</td>
                                
                                {% if data.error %}
                                    <td colspan="7" style="color: red;">Error: {{ data.error }}</td>
                                {% else %}
                                    <td>{{ data.discount.max_uses }}</td>
                                    <td>{{ data.discount.times_used }}</td>
                                    <td>
                                        {% if data.reserved_count > 0 %}
                                            <span style="color: orange;">{{ data.reserved_count }}</span>
                                        {% else %}
                                            {{ data.reserved_count }}
                                        {% endif %}
                                    </td>
                                    <td>{{ data.total_usage }}</td>
                                    <td>
                                        {% if data.remaining <= 5 %}
                                            <span style="color: red; font-weight: bold;">{{ data.remaining }}</span>
                                        {% elif data.remaining <= 10 %}
                                            <span style="color: orange;">{{ data.remaining }}</span>
                                        {% else %}
                                            {{ data.remaining }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if data.usage_percent >= 90 %}
                                            <span style="color: red; font-weight: bold;">{{ data.usage_percent|floatformat:1 }}%</span>
                                        {% elif data.usage_percent >= 75 %}
                                            <span style="color: orange;">{{ data.usage_percent|floatformat:1 }}%</span>
                                        {% else %}
                                            <span style="color: green;">{{ data.usage_percent|floatformat:1 }}%</span>
                                        {% endif %}
                                    </td>
                                {% endif %}
                                
                                <td>
                                    <a href="{% url 'discount_modeladmin_clear_cache' data.discount.code %}" 
                                       class="button button-small button-secondary"
                                       onclick="return confirm('Clear cache for {{ data.discount.code }}?')">
                                        Clear Cache
                                    </a>
                                </td>
                            </tr>
                            
                            {% if data.reserved_baskets and data.reserved_baskets|length > 0 %}
                                <tr style="background-color: #f9f9f9;">
                                    <td colspan="10">
                                        <small>
                                            <strong>Reserved by baskets:</strong> 
                                            {{ data.reserved_baskets|join:", " }}
                                        </small>
                                    </td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
                
                <div style="margin-top: 20px; padding: 15px; background-color: #f0f8ff; border-left: 4px solid #007cba;">
                    <h3>Legend</h3>
                    <ul>
                        <li><strong>Times Used:</strong> Confirmed usage from completed orders</li>
                        <li><strong>Reserved:</strong> Temporary reservations in Redis (3-minute timeout)</li>
                        <li><strong>Total Usage:</strong> Times Used + Reserved</li>
                        <li><strong>Remaining:</strong> Available uses before limit is reached</li>
                        <li><span style="color: red;">Red</span>: ≥90% used or ≤5 remaining</li>
                        <li><span style="color: orange;">Orange</span>: ≥75% used or ≤10 remaining</li>
                        <li><span style="color: green;">Green</span>: <75% used</li>
                    </ul>
                </div>
                
                <div style="margin-top: 10px;">
                    <a href="{% url 'discount_modeladmin_monitor' %}" class="button button-secondary">
                        Refresh
                    </a>
                </div>
            </div>
        {% else %}
            <div class="nice-padding">
                <h2>No Limited Discount Codes Found</h2>
                <p>There are currently no active discount codes with usage limits.</p>
                <a href="{% url 'discount_modeladmin_index' %}" class="button">
                    Back to Discount Codes
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
