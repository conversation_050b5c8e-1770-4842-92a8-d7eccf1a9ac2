#!/usr/bin/env python
"""
Pytest-compatible test for customer export functionality.
Run with: pytest test_customer_export.py -v
"""

import pytest
from datetime import datetime, timedelta
from django.utils import timezone
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.http import QueryDict
from salesman.orders.models import Order


def test_helper_functions():
    """Test the helper functions work correctly."""
    print("🧪 Testing helper functions...")

    # Import our functions
    from catalog.reports import calculate_order_analytics, extract_name_from_address

    # Test calculate_order_analytics with empty data
    empty_analytics = calculate_order_analytics([], datetime.now())
    expected_empty = ["0", "€0.00", "€0.00", "", "", "", "", "", "", ""]
    assert (
        empty_analytics == expected_empty
    ), f"Empty analytics failed: {empty_analytics}"
    print("✅ Empty analytics calculation works")

    # Test extract_name_from_address with mock order
    class MockOrder:
        def __init__(self, shipping_address):
            self.shipping_address = shipping_address

    # Test with simple name
    order1 = MockOrder("<PERSON>\n123 Main St\nCity, State")
    first, last = extract_name_from_address(order1)
    print(f"✅ Name extraction test 1: '{first}' '{last}'")

    # Test with JSON address
    order2 = MockOrder(
        '{"first_name": "Jane", "last_name": "Smith", "address": "456 Oak Ave"}'
    )
    first, last = extract_name_from_address(order2)
    print(f"✅ Name extraction test 2: '{first}' '{last}'")

    print("✅ Helper functions work correctly!\n")


@pytest.mark.django_db
def test_data_availability():
    """Check what data is available for testing."""
    print("📊 Checking available data...")

    User = get_user_model()

    # Count users
    user_count = User.objects.count()
    print(f"👥 Registered users: {user_count}")

    # Count orders
    total_orders = Order.objects.count()
    completed_orders = Order.objects.exclude(status="NEW").count()
    guest_orders = Order.objects.filter(user__isnull=True).exclude(status="NEW").count()

    print(f"📦 Total orders: {total_orders}")
    print(f"📦 Completed orders: {completed_orders}")
    print(f"👤 Guest orders: {guest_orders}")

    # Show some sample data
    if completed_orders > 0:
        sample_order = Order.objects.exclude(status="NEW").first()
        print(
            f"📋 Sample order: ID={sample_order.id}, Email={sample_order.email}, Total={sample_order.total}"
        )

    print("✅ Data availability check complete!\n")

    # Assert that we can access the database
    assert user_count >= 0
    assert total_orders >= 0


@pytest.mark.django_db
def test_export_function():
    """Test the actual export function."""
    print("🚀 Testing export function...")

    from catalog.reports import export_customers_csv

    # Create a mock request
    class MockRequest:
        def __init__(self, params):
            self.GET = QueryDict("", mutable=True)
            self.GET.update(params)
            self.method = "GET"

    # Test basic export
    request = MockRequest({"include_orders": "true"})

    try:
        response = export_customers_csv(request)

        # Check response
        assert response.status_code == 200, f"Expected 200, got {response.status_code}"
        assert response["Content-Type"] == "text/csv", f"Expected CSV content type"
        assert "attachment" in response["Content-Disposition"], "Should be downloadable"

        # Check CSV content
        csv_content = response.content.decode("utf-8")
        lines = csv_content.strip().split("\n")

        print(f"✅ CSV generated with {len(lines)} lines")
        print(f"📋 Headers: {lines[0] if lines else 'No headers'}")

        # Verify headers
        expected_headers = [
            "Customer Type",
            "Email",
            "First Name",
            "Last Name",
            "Full Name",
            "Registration Date",
            "Is Activated",
            "Address",
            "Total Orders",
            "Total Spent",
            "Average Order Value",
            "First Order Date",
            "Last Order Date",
            "Last Order Amount",
            "Highest Order Amount",
            "Lowest Order Amount",
            "Days Since Last Order",
            "Customer Lifetime (Days)",
        ]

        if lines:
            headers = lines[0].split(",")
            # Remove quotes from headers for comparison
            headers = [h.strip('"') for h in headers]

            for expected in expected_headers:
                if expected not in headers:
                    print(f"⚠️  Missing header: {expected}")
                else:
                    print(f"✅ Found header: {expected}")

        print("✅ Export function works correctly!\n")

        # Verify we got valid CSV content
        assert csv_content is not None, "CSV content should not be None"
        assert len(csv_content) > 0, "CSV content should not be empty"

    except Exception as e:
        print(f"❌ Export function failed: {str(e)}")
        import traceback

        traceback.print_exc()
        pytest.fail(f"Export function failed: {str(e)}")


@pytest.mark.django_db
def test_date_filtering():
    """Test export with date filtering."""
    print("📅 Testing date filtering...")

    from catalog.reports import export_customers_csv

    class MockRequest:
        def __init__(self, params):
            self.GET = QueryDict("", mutable=True)
            self.GET.update(params)
            self.method = "GET"

    # Test with date range using timezone-aware datetimes
    yesterday = (timezone.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    tomorrow = (timezone.now() + timedelta(days=1)).strftime("%Y-%m-%d")

    request = MockRequest(
        {
            "include_orders": "false",  # Basic export only
            "date_from": yesterday,
            "date_to": tomorrow,
        }
    )

    try:
        response = export_customers_csv(request)
        assert response.status_code == 200

        csv_content = response.content.decode("utf-8")
        lines = csv_content.strip().split("\n")

        print(f"✅ Date-filtered CSV generated with {len(lines)} lines")
        print("✅ Date filtering works correctly!\n")

    except Exception as e:
        print(f"❌ Date filtering failed: {str(e)}")


@pytest.mark.django_db
def test_customer_export_integration():
    """Integration test for the complete customer export system."""
    print("🧪 Customer Export System Integration Test")
    print("=" * 50)

    # Test that we can import and run the export function
    from catalog.reports import export_customers_csv

    # Create a mock request
    class MockRequest:
        def __init__(self, params):
            self.GET = QueryDict("", mutable=True)
            self.GET.update(params)
            self.method = "GET"

    # Test basic export
    request = MockRequest({"include_orders": "true"})

    response = export_customers_csv(request)

    # Verify response
    assert response.status_code == 200
    assert response["Content-Type"] == "text/csv"
    assert "attachment" in response["Content-Disposition"]

    # Check CSV content structure
    csv_content = response.content.decode("utf-8")
    lines = csv_content.strip().split("\n")

    # Should have at least headers
    assert len(lines) >= 1

    # Check headers are present
    headers = lines[0]
    assert "Customer Type" in headers
    assert "Email" in headers
    assert "Total Orders" in headers  # Analytics should be included

    print("✅ Integration test passed!")
    print(f"📊 CSV generated with {len(lines)} lines")


def test_manual_testing_info():
    """Display manual testing information."""
    print("\n📋 Manual Testing Steps:")
    print("1. Start Django server: python manage.py runserver")
    print("2. Go to: http://localhost:8000/admin/")
    print("3. Navigate to: Catalog Reports → Export Customers")
    print("4. Test different export options")
    print("5. Download and verify the CSV file")

    print("\n🎯 Expected functionality:")
    print("• Form with checkboxes and date fields")
    print("• CSV download with customer data")
    print("• Order analytics (when enabled)")
    print("• Both registered users and guest customers")
    print("• Date filtering options")
