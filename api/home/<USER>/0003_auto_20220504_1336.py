# Generated by Django 3.2.11 on 2022-05-04 11:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wagtailimages', '0023_add_choose_permissions'),
        ('core', '0002_corepage_show_in_footer_menus'),
        ('home', '0002_homepage_copyright_text'),
    ]

    operations = [
        migrations.AddField(
            model_name='homepage',
            name='banner_countdown_bg_color',
            field=models.CharField(default='#ffffff', max_length=7, null=True, verbose_name='Countdown BG color (hex)'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_countdown_bg_image',
            field=models.ForeignKey(blank=True, help_text='Image for the banner countdown, overrides BG color.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='wagtailimages.image', verbose_name='Countdown BG image'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_countdown_text1',
            field=models.CharField(blank=True, max_length=255, verbose_name='Countdown text #1'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_countdown_text2',
            field=models.CharField(blank=True, max_length=255, verbose_name='Countdown text #2'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_countdown_text_color',
            field=models.CharField(default='#000000', max_length=7, null=True, verbose_name='Countdown text color (hex)'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_countdown_to',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Countdown to'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_hide_on_countdown_done',
            field=models.BooleanField(default=True, help_text='Should the banner be automatically hidden once the timer runs out.', verbose_name='Hide on countdown done'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_image',
            field=models.ForeignKey(blank=True, help_text='Image for the banner, size of image controls the banner size.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='wagtailimages.image', verbose_name='Image'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='banner_link',
            field=models.ForeignKey(blank=True, help_text='Select a page to link to when clicked on a banner', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='core.corepage', verbose_name='Link'),
        ),
    ]
