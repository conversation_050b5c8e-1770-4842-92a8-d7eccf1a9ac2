# Generated by Django 3.2.25 on 2025-07-21 17:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0007_auto_20250721_1858'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='notificationticker',
            name='copy_button_text_en',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='copy_button_text_es',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='link_text_en',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='link_text_es',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='message_en',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='message_es',
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='copy_button_text',
            field=models.Char<PERSON>ield(default='Copy Code', help_text='Text for the copy button', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='link_text',
            field=models.CharField(blank=True, help_text='Text for the link button', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='message',
            field=models.TextField(default='', help_text='The notification message to display'),
        ),
    ]
