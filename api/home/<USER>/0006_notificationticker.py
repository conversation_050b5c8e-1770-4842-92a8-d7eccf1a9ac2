# Generated by Django 3.2.25 on 2025-07-17 18:00

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0005_homepage_banner_external_link'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTicker',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(help_text='Internal title for admin', max_length=200)),
                ('message', models.TextField(help_text='The notification message to display')),
                ('display_type', models.CharField(choices=[('ticker', 'Ticker (scrolling text)'), ('carousel', 'Carousel (one at a time)')], default='ticker', help_text='How this notification should be displayed', max_length=20)),
                ('priority', models.IntegerField(choices=[(1, 'Low'), (2, 'Normal'), (3, 'High'), (4, 'Critical')], default=2, help_text='Higher priority notifications appear first')),
                ('background_color', models.CharField(default='#007bff', help_text='Background color (hex code)', max_length=7)),
                ('text_color', models.CharField(default='#ffffff', help_text='Text color (hex code)', max_length=7)),
                ('copyable_code', models.CharField(blank=True, help_text='Discount code or text that can be copied (optional)', max_length=100)),
                ('copy_button_text', models.CharField(default='Copy Code', help_text='Text for the copy button', max_length=50)),
                ('link_url', models.URLField(blank=True, help_text='Optional link URL')),
                ('link_text', models.CharField(blank=True, help_text='Text for the link button', max_length=50)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now, help_text='When this notification should start showing')),
                ('end_date', models.DateTimeField(blank=True, help_text='When this notification should stop showing (optional)', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this notification is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Notification Ticker',
                'verbose_name_plural': 'Notification Tickers',
                'ordering': ['-priority', '-created_at'],
            },
        ),
    ]
