# Generated by Django 3.2.25 on 2025-07-21 19:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0009_add_translation_keys'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(help_text="Translation key (e.g., 'notifications.welcome')", max_length=100, unique=True)),
                ('text_en', models.TextField(help_text='English text')),
                ('text_es', models.TextField(help_text='Spanish text')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Notification Translation',
                'verbose_name_plural': 'Notification Translations',
                'ordering': ['key'],
            },
        ),
    ]
