from django.conf import settings
from django.utils.translation import get_language_info
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from wagtail.core.models import Site

from catalog.models.catalog import ProductCollection
from catalog.serializers import ProductCollectionSummarySerializer
from core.models import CorePage
from core.serializers import (
    ImageSerializer,
    LabelsField,
    PageMenuSerializer,
    PageSerializer,
)
from country.utils import get_country_choices, get_state_choices

from .models import HomePage, NotificationTicker, NotificationSettings


class HomePageConfigSerializer(serializers.ModelSerializer):
    site_name = serializers.SerializerMethodField()
    root_url = serializers.SerializerMethodField()
    languages = serializers.SerializerMethodField()
    company_address = serializers.SerializerMethodField()
    company_email = serializers.SerializerMethodField()
    currencies = serializers.SerializerMethodField()
    page_links = serializers.SerializerMethodField()
    main_menu = serializers.SerializerMethodField()
    footer = serializers.SerializerMethodField()
    labels = LabelsField(
        ("account", _("Account")),
        ("cart", _("Cart")),
        ("login_register", _("Log in / Register")),
        ("currency", _("Currency")),
    )
    countries = serializers.SerializerMethodField()
    states = serializers.SerializerMethodField()

    class Meta:
        model = HomePage
        fields = [
            "site_name",
            "root_url",
            "languages",
            "company_address",
            "company_email",
            "currencies",
            "page_links",
            "main_menu",
            "footer",
            "labels",
            "countries",
            "states",
        ]

    def get_site(self):
        request = self.context["request"]
        return Site.find_for_request(request)

    def get_site_name(self, obj):
        site = self.get_site()
        return site.site_name if site else None

    def get_root_url(self, obj):
        site = self.get_site()
        return site.root_url if site else None

    def get_languages(self, obj):
        languages = obj.get_translations(inclusive=True).values_list(
            "locale__language_code", flat=True
        )
        data = []
        for lang, _name in settings.WAGTAIL_CONTENT_LANGUAGES:
            if lang in languages:
                data.append(get_language_info(lang))
        return data

    def get_company_address(self, obj):
        return settings.SHOP_COMPANY_ADDRESS

    def get_company_email(self, obj):
        return settings.SHOP_COMPANY_EMAIL

    def get_currencies(self, obj):
        return settings.CURRENCY_SYMBOLS

    def get_main_menu(self, obj):
        return PageMenuSerializer(context=self.context).to_representation(obj)

    def get_page_links(self, obj):
        serializer = PageMenuSerializer(context=self.context, with_children=False)
        return {
            page.reverse_id: serializer.to_representation(page)
            for page in CorePage.objects.filter(
                locale=obj.locale, reverse_id__isnull=False
            )
        }

    def get_footer(self, obj):
        menu_serializer = PageMenuSerializer(
            get_child_pages_method="get_footer_menu_pages",
            context=self.context,
        )
        return {
            "copyright_text": obj.copyright_text,
            "menu": menu_serializer.to_representation(obj),
        }

    def get_countries(self, obj):
        return get_country_choices(shipping=False)

    def get_states(self, obj):
        return {
            "US": get_state_choices("US"),
            "CA": get_state_choices("CA"),
            "IT": get_state_choices("IT"),
        }


class HomePageBannerField(serializers.DictField):
    def get_attribute(self, instance):
        return instance

    def to_representation(self, page):
        image_serializer = ImageSerializer(context=self.context)
        image = (
            image_serializer.to_representation(page.banner_image)
            if page.banner_image
            else None
        )
        countdown_bg_image = (
            image_serializer.to_representation(page.banner_countdown_bg_image)
            if page.banner_countdown_bg_image
            else None
        )
        return {
            "link": page.banner_link.get_url() if page.banner_link else None,
            "external_link": page.banner_external_link or None,
            "image": image,
            "title": page.banner_title,
            "hide_on_countdown_done": page.banner_hide_on_countdown_done,
            "countdown_to": page.banner_countdown_to,
            "countdown_text1": page.banner_countdown_text1,
            "countdown_text2": page.banner_countdown_text2,
            "countdown_text_color": page.banner_countdown_text_color,
            "countdown_bg_color": page.banner_countdown_bg_color,
            "countdown_bg_image": countdown_bg_image,
        }


class HomePageSerializer(PageSerializer):
    banner = HomePageBannerField()
    collections = serializers.SerializerMethodField()

    class Meta:
        model = HomePage
        fields = PageSerializer.Meta.fields + ["banner", "collections"]

    def get_collections(self, obj):
        items = ProductCollection.objects.filter(locale_id=obj.locale_id).live()
        return ProductCollectionSummarySerializer(items, many=True).data


class NotificationSettingsSerializer(serializers.ModelSerializer):
    """Serializer for notification settings"""

    class Meta:
        model = NotificationSettings
        fields = [
            "display_type",
            "carousel_rotation_speed",
            "ticker_scroll_speed",
        ]


class NotificationTickerSerializer(serializers.ModelSerializer):
    """Serializer for notification tickers using Django native translations"""

    message = serializers.SerializerMethodField()
    copy_button_text = serializers.SerializerMethodField()
    link_text = serializers.SerializerMethodField()

    class Meta:
        model = NotificationTicker
        fields = [
            "id",
            "title",
            "message",
            "priority",
            "background_color",
            "text_color",
            "copyable_code",
            "copy_button_text",
            "link_url",
            "link_text",
            "start_date",
            "end_date",
            "is_currently_active",
        ]

    def get_message(self, obj):
        """Get the translated message"""
        language_code = self.context.get("language_code", "en")
        return obj.get_translated_message(language_code)

    def get_copy_button_text(self, obj):
        """Get the translated copy button text"""
        language_code = self.context.get("language_code", "en")
        return obj.get_translated_copy_button_text(language_code)

    def get_link_text(self, obj):
        """Get the translated link text"""
        language_code = self.context.get("language_code", "en")
        return obj.get_translated_link_text(language_code)

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Only include copyable_code if it exists
        if not data["copyable_code"]:
            data.pop("copyable_code", None)
            data.pop("copy_button_text", None)

        # Only include link if it exists
        if not data["link_url"]:
            data.pop("link_url", None)
            data.pop("link_text", None)

        return data
