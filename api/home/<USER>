from rest_framework.response import Response
from rest_framework.decorators import action

from core.views import CachedGenericViewSet
from home.models import HomePage, NotificationTicker, NotificationSettings

from .serializers import (
    HomePageConfigSerializer,
    NotificationTickerSerializer,
    NotificationSettingsSerializer,
)


class ConfigViewSet(CachedGenericViewSet):
    serializer_class = HomePageConfigSerializer
    pagination_class = None

    def get_view_name(self):
        return "Config"

    def list(self, request):
        obj = HomePage.find_for_request(request)
        if obj:
            serializer = self.get_serializer(obj)
            return Response(serializer.data)
        return Response({})

    @action(detail=False, methods=["get"])
    def notifications(self, request):
        """Get active notifications for the ticker/carousel with settings"""
        # Check if cache refresh is requested
        if request.GET.get("refresh_cache"):
            from django.core.cache import cache

            cache.delete("active_notifications")

        # Get language from request
        language_code = request.GET.get("locale", "en")

        # Get notifications and settings
        notifications = NotificationTicker.get_active_notifications()
        settings = NotificationSettings.get_settings()

        # Serialize notifications and settings with language context
        notification_serializer = NotificationTickerSerializer(
            notifications, many=True, context={"language_code": language_code}
        )
        settings_serializer = NotificationSettingsSerializer(settings)

        return Response(
            {
                "settings": settings_serializer.data,
                "notifications": notification_serializer.data,
            }
        )

    @action(detail=False, methods=["post"])
    def refresh_notifications(self, request):
        """Manually refresh notification cache"""
        from django.core.cache import cache

        cache.delete("active_notifications")
        notifications = NotificationTicker.get_active_notifications()
        serializer = NotificationTickerSerializer(notifications, many=True)
        return Response(
            {
                "message": "Cache refreshed successfully",
                "count": len(notifications),
                "data": serializer.data,
            }
        )
