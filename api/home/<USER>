from wagtail.contrib.modeladmin.options import ModelAdmin, modeladmin_register
from wagtail.admin.edit_handlers import FieldPanel, MultiFieldPanel
from wagtail.core import hooks

from .models import NotificationTicker, NotificationSettings, NotificationTranslation


class NotificationTranslationAdmin(ModelAdmin):
    model = NotificationTranslation
    menu_label = "Notification Translations"
    menu_icon = "doc-full"
    menu_order = 198
    add_to_settings_menu = False
    exclude_from_explorer = False
    list_display = ["key", "text_en_preview", "text_es_preview", "updated_at"]
    list_filter = []
    search_fields = ["key", "text_en", "text_es"]

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("key"),
            ],
            heading="Translation Key",
        ),
        MultiFieldPanel(
            [
                FieldPanel("text_en"),
                FieldPanel("text_es"),
            ],
            heading="Translations",
        ),
    ]

    def text_en_preview(self, obj):
        """Show preview of English text"""
        return obj.text_en[:50] + "..." if len(obj.text_en) > 50 else obj.text_en

    text_en_preview.short_description = "English Text"

    def text_es_preview(self, obj):
        """Show preview of Spanish text"""
        return obj.text_es[:50] + "..." if len(obj.text_es) > 50 else obj.text_es

    text_es_preview.short_description = "Spanish Text"


class NotificationSettingsAdmin(ModelAdmin):
    model = NotificationSettings
    menu_label = "Notification Settings"
    menu_icon = "cogs"
    menu_order = 199
    add_to_settings_menu = True
    exclude_from_explorer = False
    list_display = [
        "display_type",
        "carousel_rotation_speed",
        "ticker_scroll_speed",
        "updated_at",
    ]

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("display_type"),
            ],
            heading="Display Mode",
        ),
        MultiFieldPanel(
            [
                FieldPanel("carousel_rotation_speed"),
                FieldPanel("ticker_scroll_speed"),
            ],
            heading="Animation Settings",
        ),
    ]

    def has_add_permission(self, request):
        """Only allow one settings instance"""
        return not NotificationSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        """Don't allow deletion of settings"""
        return False


class NotificationTickerAdmin(ModelAdmin):
    model = NotificationTicker
    menu_label = "Notification Tickers"
    menu_icon = "warning"
    menu_order = 200
    add_to_settings_menu = False
    exclude_from_explorer = False
    list_display = [
        "title",
        "priority",
        "is_active",
        "start_date",
        "end_date",
        "is_currently_active_display",
    ]
    list_filter = ["priority", "is_active", "start_date"]
    search_fields = ["title", "message_key", "message_fallback"]
    ordering = ["-priority", "-created_at"]

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("title"),
                FieldPanel("priority"),
            ],
            heading="Basic Information",
        ),
        MultiFieldPanel(
            [
                FieldPanel("message_key"),
                FieldPanel("message_fallback"),
            ],
            heading="Message (Translation)",
        ),
        MultiFieldPanel(
            [
                FieldPanel("background_color"),
                FieldPanel("text_color"),
            ],
            heading="Styling",
        ),
        MultiFieldPanel(
            [
                FieldPanel("copyable_code"),
                FieldPanel("copy_button_key"),
                FieldPanel("copy_button_fallback"),
            ],
            heading="Copyable Content (Optional)",
        ),
        MultiFieldPanel(
            [
                FieldPanel("link_url"),
                FieldPanel("link_text_key"),
                FieldPanel("link_text_fallback"),
            ],
            heading="Link (Optional)",
        ),
        MultiFieldPanel(
            [
                FieldPanel("start_date"),
                FieldPanel("end_date"),
                FieldPanel("is_active"),
            ],
            heading="Timing & Status",
        ),
    ]

    def is_currently_active_display(self, obj):
        """Display current active status with color coding"""
        if obj.is_currently_active:
            return '<span style="color: green; font-weight: bold;">✓ Active</span>'
        else:
            return '<span style="color: red;">✗ Inactive</span>'

    is_currently_active_display.short_description = "Currently Active"
    is_currently_active_display.allow_tags = True


modeladmin_register(NotificationTranslationAdmin)
modeladmin_register(NotificationSettingsAdmin)
modeladmin_register(NotificationTickerAdmin)


@hooks.register("after_create_page")
@hooks.register("after_edit_page")
@hooks.register("after_delete_page")
def clear_notification_cache(request, page):
    """Clear notification cache when pages are modified"""
    from django.core.cache import cache

    cache.delete("active_notifications")


@hooks.register("after_create_snippet")
@hooks.register("after_edit_snippet")
@hooks.register("after_delete_snippet")
def clear_notification_cache_on_snippet_change(request, snippet):
    """Clear notification cache when NotificationTicker snippets are modified"""
    if isinstance(snippet, NotificationTicker):
        from django.core.cache import cache

        cache.delete("active_notifications")
        print(f"🔄 Cleared notification cache after {snippet.title} was modified")


@hooks.register("construct_main_menu")
def hide_notifications_from_main_menu(request, menu_items):
    """Keep notifications in settings menu only"""
    pass
