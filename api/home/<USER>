from tabnanny import verbose

from django.db import models
from django.core.cache import cache
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from wagtail.admin.edit_handlers import (
    FieldPanel,
    MultiFieldPanel,
    ObjectList,
    PageChooserPanel,
    TabbedInterface,
)
from wagtail.images.edit_handlers import ImageChooserPanel

from core.models import CorePage


class ConfigMixin(models.Model):
    copyright_text = models.CharField("Copyright text", max_length=255, blank=True)

    panels = [
        FieldPanel("copyright_text"),
    ]

    class Meta:
        abstract = True


class HomePage(ConfigMixin, CorePage):
    # banner
    banner_link = models.ForeignKey(
        CorePage,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="+",
        help_text="Select a page to link to when clicked on a banner",
        verbose_name="Link",
    )
    banner_external_link = models.URLField(
        "External link",
        blank=True,
        null=True,
        help_text="If an external link is present, it will override internal link.",
    )
    banner_image = models.ForeignKey(
        "wagtailimages.Image",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="+",
        help_text="Image for the banner, size of image controls the banner size.",
        verbose_name="Image",
    )
    banner_title = models.CharField("Title", max_length=255, blank=True)
    banner_hide_on_countdown_done = models.BooleanField(
        "Hide on countdown done",
        default=True,
        help_text="Should the banner be automatically hidden once the timer runs out.",
    )
    banner_countdown_to = models.DateTimeField("Countdown to", blank=True, null=True)
    banner_countdown_text1 = models.CharField(
        "Countdown text #1",
        max_length=255,
        blank=True,
        help_text="Shown before the timer.",
    )
    banner_countdown_text2 = models.CharField(
        "Countdown text #2",
        max_length=255,
        blank=True,
        help_text="Show after the timer.",
    )
    banner_countdown_text_color = models.CharField(
        "Countdown text color (hex)",
        max_length=7,
        null=True,
        default="#000000",
    )
    banner_countdown_bg_color = models.CharField(
        "Countdown BG color (hex)",
        max_length=7,
        null=True,
        default="#ffffff",
    )
    banner_countdown_bg_image = models.ForeignKey(
        "wagtailimages.Image",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="+",
        help_text="Image for the banner countdown, overrides BG color.",
        verbose_name="Countdown BG image",
    )

    content_panels = CorePage.content_panels + [
        MultiFieldPanel(
            [
                ImageChooserPanel("banner_image"),
                PageChooserPanel("banner_link"),
                FieldPanel("banner_external_link"),
                FieldPanel("banner_title"),
                FieldPanel("banner_hide_on_countdown_done"),
                FieldPanel("banner_countdown_to"),
                FieldPanel("banner_countdown_text1"),
                FieldPanel("banner_countdown_text2"),
                FieldPanel("banner_countdown_text_color"),
                FieldPanel("banner_countdown_bg_color"),
                ImageChooserPanel("banner_countdown_bg_image"),
            ],
            heading="Banner",
        )
    ]

    edit_handler = TabbedInterface(
        [
            ObjectList(content_panels, heading="Content"),
            ObjectList(CorePage.promote_panels, heading="Promote"),
            ObjectList(
                CorePage.settings_panels, heading="Settings", classname="settings"
            ),
            ObjectList(ConfigMixin.panels, heading="Site config"),
        ]
    )

    max_count = 1
    parent_page_types = ["wagtailcore.Page"]
    page_serializer = "home.serializers.HomePageSerializer"

    class Meta:
        pass

    def get_menu_pages(self, request):
        return CorePage.objects.child_of(self).filter(show_in_menus=True).live()

    def get_footer_menu_pages(self, request):
        return CorePage.objects.child_of(self).filter(show_in_footer_menus=True).live()

    @classmethod
    def find_for_request(cls, request):
        return cls.objects.filter(locale__language_code=request.LANGUAGE_CODE).first()


class NotificationTranslation(models.Model):
    """Model for managing notification translations"""

    key = models.CharField(
        max_length=100,
        unique=True,
        help_text="Translation key (e.g., 'notifications.welcome')",
    )
    text_en = models.TextField(help_text="English text")
    text_es = models.TextField(help_text="Spanish text")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Notification Translation"
        verbose_name_plural = "Notification Translations"
        ordering = ["key"]

    def __str__(self):
        return f"{self.key}"

    @classmethod
    def get_translation(cls, key, language_code="en"):
        """Get translation for a key in the specified language"""
        try:
            translation = cls.objects.get(key=key)
            if language_code == "es":
                return translation.text_es
            return translation.text_en
        except cls.DoesNotExist:
            return None


class NotificationSettings(models.Model):
    """Global settings for notification display behavior"""

    DISPLAY_CHOICES = [
        ("ticker", "Ticker (scrolling text)"),
        ("carousel", "Carousel (one at a time)"),
    ]

    display_type = models.CharField(
        max_length=20,
        choices=DISPLAY_CHOICES,
        default="ticker",
        help_text="How ALL notifications should be displayed globally",
    )
    carousel_rotation_speed = models.IntegerField(
        default=5,
        help_text="Seconds between carousel rotations (only for carousel mode)",
    )
    ticker_scroll_speed = models.CharField(
        max_length=20,
        default="30s",
        help_text="CSS animation duration for ticker scroll (e.g., '30s', '45s')",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Notification Settings"
        verbose_name_plural = "Notification Settings"

    def __str__(self):
        return f"Notification Settings ({self.get_display_type_display()})"

    @classmethod
    def get_settings(cls):
        """Get or create notification settings"""
        settings, created = cls.objects.get_or_create(
            pk=1,  # Singleton pattern
            defaults={
                "display_type": "ticker",
                "carousel_rotation_speed": 5,
                "ticker_scroll_speed": "30s",
            },
        )
        return settings


class NotificationTicker(models.Model):
    """Model for managing notification tickers/banners"""

    PRIORITY_CHOICES = [
        (1, "Low"),
        (2, "Normal"),
        (3, "High"),
        (4, "Critical"),
    ]

    title = models.CharField(max_length=200, help_text="Internal title for admin")
    message_key = models.CharField(
        max_length=100,
        default="notifications.default",
        help_text="Translation key for the message (e.g., 'notifications.welcome')",
    )
    message_fallback = models.TextField(
        default="", help_text="Fallback message if translation key is not found"
    )
    priority = models.IntegerField(
        choices=PRIORITY_CHOICES,
        default=2,
        help_text="Higher priority notifications appear first",
    )

    # Styling options
    background_color = models.CharField(
        max_length=7, default="#007bff", help_text="Background color (hex code)"
    )
    text_color = models.CharField(
        max_length=7, default="#ffffff", help_text="Text color (hex code)"
    )

    # Copyable content
    copyable_code = models.CharField(
        max_length=100,
        blank=True,
        help_text="Discount code or text that can be copied (optional)",
    )
    copy_button_key = models.CharField(
        max_length=100,
        default="notifications.copy_code",
        help_text="Translation key for copy button (e.g., 'notifications.copy_code')",
    )
    copy_button_fallback = models.CharField(
        max_length=50, default="Copy Code", help_text="Fallback text for copy button"
    )

    # Link options
    link_url = models.URLField(blank=True, help_text="Optional link URL")
    link_text_key = models.CharField(
        max_length=100,
        blank=True,
        help_text="Translation key for link text (e.g., 'notifications.learn_more')",
    )
    link_text_fallback = models.CharField(
        max_length=50, blank=True, help_text="Fallback text for link"
    )

    # Timing
    start_date = models.DateTimeField(
        default=timezone.now, help_text="When this notification should start showing"
    )
    end_date = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When this notification should stop showing (optional)",
    )

    # Status
    is_active = models.BooleanField(
        default=True, help_text="Whether this notification is currently active"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-priority", "-created_at"]
        verbose_name = "Notification Ticker"
        verbose_name_plural = "Notification Tickers"

    def __str__(self):
        return f"{self.title}"

    def get_translated_message(self, language_code="en"):
        """Get the translated message using the NotificationTranslation system"""
        # First try to get from NotificationTranslation
        translation = NotificationTranslation.get_translation(
            self.message_key, language_code
        )
        if translation:
            return translation

        # Fallback to the fallback field
        return self.message_fallback or self.message_key

    def get_translated_copy_button_text(self, language_code="en"):
        """Get the translated copy button text"""
        # First try to get from NotificationTranslation
        translation = NotificationTranslation.get_translation(
            self.copy_button_key, language_code
        )
        if translation:
            return translation

        # Fallback to the fallback field
        return self.copy_button_fallback

    def get_translated_link_text(self, language_code="en"):
        """Get the translated link text"""
        if not self.link_text_key:
            return self.link_text_fallback

        # First try to get from NotificationTranslation
        translation = NotificationTranslation.get_translation(
            self.link_text_key, language_code
        )
        if translation:
            return translation

        # Fallback to the fallback field
        return self.link_text_fallback

    @property
    def is_currently_active(self):
        """Check if notification should be shown right now"""
        if not self.is_active:
            return False

        now = timezone.now()
        if now < self.start_date:
            return False

        if self.end_date and now > self.end_date:
            return False

        return True

    @classmethod
    def get_active_notifications(cls):
        """Get all currently active notifications, cached for performance"""
        cache_key = "active_notifications"
        notifications = cache.get(cache_key)

        if notifications is None:
            now = timezone.now()
            notifications = list(
                cls.objects.filter(is_active=True, start_date__lte=now)
                .filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=now))
                .order_by("-priority", "-created_at")
            )
            # Cache for 30 seconds to match frontend refresh rate
            cache.set(cache_key, notifications, 30)

        return notifications

    def save(self, *args, **kwargs):
        """Clear cache when notification is saved"""
        super().save(*args, **kwargs)
        self._clear_notification_cache()

    def delete(self, *args, **kwargs):
        """Clear cache when notification is deleted"""
        self._clear_notification_cache()
        super().delete(*args, **kwargs)

    def _clear_notification_cache(self):
        """Clear notification cache"""
        cache.delete("active_notifications")
