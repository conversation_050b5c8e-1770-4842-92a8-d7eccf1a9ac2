# Generated by Django 3.2.25 on 2025-07-21 17:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0008_revert_to_simple_model'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='notificationticker',
            name='copy_button_text',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='link_text',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='message',
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='copy_button_fallback',
            field=models.CharField(default='Copy Code', help_text='Fallback text for copy button', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='copy_button_key',
            field=models.CharField(default='notifications.copy_code', help_text="Translation key for copy button (e.g., 'notifications.copy_code')", max_length=100),
        ),
        migrations.Add<PERSON>ield(
            model_name='notificationticker',
            name='link_text_fallback',
            field=models.Char<PERSON>ield(blank=True, help_text='Fallback text for link', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='link_text_key',
            field=models.CharField(blank=True, help_text="Translation key for link text (e.g., 'notifications.learn_more')", max_length=100),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='message_fallback',
            field=models.TextField(default='', help_text='Fallback message if translation key is not found'),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='message_key',
            field=models.CharField(default='notifications.default', help_text="Translation key for the message (e.g., 'notifications.welcome')", max_length=100),
        ),
    ]
