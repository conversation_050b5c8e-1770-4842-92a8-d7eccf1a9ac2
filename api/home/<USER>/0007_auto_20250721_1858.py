# Generated by Django 3.2.25 on 2025-07-21 16:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0006_notificationticker'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationSettings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('display_type', models.CharField(choices=[('ticker', 'Ticker (scrolling text)'), ('carousel', 'Carousel (one at a time)')], default='ticker', help_text='How ALL notifications should be displayed globally', max_length=20)),
                ('carousel_rotation_speed', models.IntegerField(default=5, help_text='Seconds between carousel rotations (only for carousel mode)')),
                ('ticker_scroll_speed', models.Char<PERSON>ield(default='30s', help_text="CSS animation duration for ticker scroll (e.g., '30s', '45s')", max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Notification Settings',
                'verbose_name_plural': 'Notification Settings',
            },
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='copy_button_text',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='display_type',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='link_text',
        ),
        migrations.RemoveField(
            model_name='notificationticker',
            name='message',
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='copy_button_text_en',
            field=models.CharField(default='Copy Code', help_text='Copy button text in English', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='copy_button_text_es',
            field=models.CharField(default='Copiar Código', help_text='Copy button text in Spanish', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='link_text_en',
            field=models.CharField(blank=True, default='', help_text='Link button text in English', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='link_text_es',
            field=models.CharField(blank=True, default='', help_text='Link button text in Spanish', max_length=50),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='message_en',
            field=models.TextField(default='', help_text='The notification message in English'),
        ),
        migrations.AddField(
            model_name='notificationticker',
            name='message_es',
            field=models.TextField(default='', help_text='The notification message in Spanish'),
        ),
    ]
