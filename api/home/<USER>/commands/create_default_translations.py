from django.core.management.base import BaseCommand
from home.models import NotificationTranslation


class Command(BaseCommand):
    help = 'Create default notification translations'

    def handle(self, *args, **options):
        """Create default notification translations"""
        
        default_translations = [
            # Common notification messages
            {
                'key': 'notifications.welcome',
                'text_en': 'Welcome to our store!',
                'text_es': '¡Bienvenido a nuestra tienda!'
            },
            {
                'key': 'notifications.sale',
                'text_en': 'Special sale - 20% off everything!',
                'text_es': '¡Oferta especial - 20% de descuento en todo!'
            },
            {
                'key': 'notifications.free_shipping',
                'text_en': 'Free shipping on orders over €50',
                'text_es': 'Envío gratis en pedidos superiores a 50€'
            },
            {
                'key': 'notifications.new_collection',
                'text_en': 'New collection now available',
                'text_es': 'Nueva colección ya disponible'
            },
            {
                'key': 'notifications.summer_sale',
                'text_en': '🎉 Summer Sale! Get 20% off all items with code SUMMER20',
                'text_es': '🎉 ¡Oferta de Verano! Obtén 20% de descuento en todos los artículos con el código SUMMER20'
            },
            
            # Copy button texts
            {
                'key': 'notifications.copy_code',
                'text_en': 'Copy Code',
                'text_es': 'Copiar Código'
            },
            {
                'key': 'notifications.copy_discount',
                'text_en': 'Copy Discount',
                'text_es': 'Copiar Descuento'
            },
            {
                'key': 'notifications.get_code',
                'text_en': 'Get Code',
                'text_es': 'Obtener Código'
            },
            
            # Link texts
            {
                'key': 'notifications.shop_now',
                'text_en': 'Shop Now',
                'text_es': 'Comprar Ahora'
            },
            {
                'key': 'notifications.learn_more',
                'text_en': 'Learn More',
                'text_es': 'Saber Más'
            },
            {
                'key': 'notifications.view_collection',
                'text_en': 'View Collection',
                'text_es': 'Ver Colección'
            },
            {
                'key': 'notifications.see_details',
                'text_en': 'See Details',
                'text_es': 'Ver Detalles'
            },
            {
                'key': 'notifications.explore_collection',
                'text_en': 'Explore Collection',
                'text_es': 'Explorar Colección'
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for translation_data in default_translations:
            translation, created = NotificationTranslation.objects.get_or_create(
                key=translation_data['key'],
                defaults={
                    'text_en': translation_data['text_en'],
                    'text_es': translation_data['text_es'],
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created translation: {translation.key}')
                )
            else:
                # Update existing translation if different
                if (translation.text_en != translation_data['text_en'] or 
                    translation.text_es != translation_data['text_es']):
                    translation.text_en = translation_data['text_en']
                    translation.text_es = translation_data['text_es']
                    translation.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'Updated translation: {translation.key}')
                    )
                else:
                    self.stdout.write(f'Translation already exists: {translation.key}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary: {created_count} created, {updated_count} updated'
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                f'Total translations: {NotificationTranslation.objects.count()}'
            )
        )
