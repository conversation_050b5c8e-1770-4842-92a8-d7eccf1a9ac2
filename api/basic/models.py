from django.db import models
from django.utils import timezone
from wagtail.admin.edit_handlers import FieldPanel, StreamFieldPanel
from wagtail.core.fields import RichTextField, StreamField
from wagtail.images.edit_handlers import ImageChooserPanel
from wagtail.search import index

from core.models import CorePage

from . import blocks


class FeaturedImageMixin(models.Model):
    featured_image = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.SET_NULL,
        verbose_name="Featured image",
        help_text="Main featured image for this page.",
    )

    title_as_image = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.SET_NULL,
        verbose_name="Title as image",
        help_text="Image to use as the collection title, this replaces the collection title on specific pages.",
    )

    title_mobile_as_image = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.SET_NULL,
        verbose_name="Title mobile as image",
        help_text="Image to use as the collection title on mobile, this replaces the collection title on specific pages.",
    )

    alt_featured_image = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.SET_NULL,
        verbose_name="Optional Featured image",
        help_text="Optional featured image for this page.",
    )

    panels = [
        ImageChooserPanel("featured_image"),
        ImageChooserPanel("title_as_image"),
        ImageChooserPanel("title_mobile_as_image"),
        ImageChooserPanel("alt_featured_image"),
    ]

    class Meta:
        abstract = True


class PublishedDateMixin(models.Model):
    published_at = models.DateTimeField("Published at", default=timezone.localtime)

    panels = [FieldPanel("published_at")]

    class Meta:
        abstract = True


class IntroMixin(models.Model):
    intro = RichTextField(
        "Intro",
        blank=True,
        features=["bold", "italic", "link"],
        help_text="Page intro text.",
    )

    search_fields = [index.SearchField("intro")]
    panels = [FieldPanel("intro")]

    class Meta:
        abstract = True


class BasicPageMixin(models.Model):
    body = StreamField(
        [
            ("text", blocks.RichTextBlock()),
            ("image", blocks.ImageBlock()),
            ("table", blocks.TableBlock()),
        ],
        blank=True,
    )

    search_fields = [index.SearchField("body")]
    panels = [StreamFieldPanel("body")]

    class Meta:
        abstract = True


class BasicPage(BasicPageMixin, CorePage):
    search_fields = CorePage.search_fields + BasicPageMixin.search_fields
    content_panels = CorePage.content_panels + BasicPageMixin.panels
    parent_page_types = ["home.HomePage", "basic.BasicPage"]
    page_serializer = "basic.serializers.BasicPageSerializer"
