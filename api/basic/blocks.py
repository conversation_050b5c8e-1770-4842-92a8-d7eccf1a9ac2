from django.utils.safestring import mark_safe
from wagtail.contrib.table_block.blocks import TableBlock as TableBlockBase
from wagtail.core import blocks
from wagtail.core.rich_text import expand_db_html
from wagtail.images.blocks import ImageChooser<PERSON>lock as ImageChooserBlockBase

from core.serializers import ImageSerializer


class RichTextBlock(blocks.RichTextBlock):
    def get_api_representation(self, value, context=None):
        return mark_safe(expand_db_html(value.source))

    class Meta:
        label = "Text"


class TableBlock(TableBlockBase):
    class Meta:
        label = "Table"


class ImageChooserBlock(ImageChooserBlockBase):
    def get_api_representation(self, value, context=None):
        if value:
            return ImageSerializer(context=context).to_representation(value)

    class Meta:
        label = "Image"


class ImageBlock(blocks.StructBlock):
    image = ImageChooserBlock(label="Image")
    caption = blocks.CharBlock(label="Caption", required=False)

    class Meta:
        label = "Image"
        icon = "image"
