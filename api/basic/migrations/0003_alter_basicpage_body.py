# Generated by Django 3.2.9 on 2021-11-15 12:10

import basic.blocks
from django.db import migrations
import wagtail.core.blocks
import wagtail.core.fields


class Migration(migrations.Migration):

    dependencies = [
        ('basic', '0002_alter_basicpage_body'),
    ]

    operations = [
        migrations.AlterField(
            model_name='basicpage',
            name='body',
            field=wagtail.core.fields.StreamField([('text', basic.blocks.RichTextBlock()), ('image', wagtail.core.blocks.StructBlock([('image', basic.blocks.ImageChooserBlock(label='Image')), ('caption', wagtail.core.blocks.CharBlock(label='Caption', required=False))])), ('table', basic.blocks.TableBlock())], blank=True),
        ),
    ]
