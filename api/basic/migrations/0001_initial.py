# Generated by Django 3.2.7 on 2021-09-15 14:12

from django.db import migrations, models
import django.db.models.deletion
import wagtail.core.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BasicPage',
            fields=[
                ('corepage_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.corepage')),
                ('body', wagtail.core.fields.RichTextField(blank=True, verbose_name='Body')),
            ],
            options={
                'abstract': False,
            },
            bases=('core.corepage', models.Model),
        ),
    ]
