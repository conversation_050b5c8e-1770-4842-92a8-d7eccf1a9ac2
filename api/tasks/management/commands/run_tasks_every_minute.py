from django.core.management import call_command
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Run task group every minute"

    def handle(self, *args, **options):
        call_command("publish_scheduled_pages")
        call_command("send_queued_mail")
        call_command("delete_expired_basket_reservations")
        call_command("create_queued_parcels")
        call_command("send_scheduled_reports")
