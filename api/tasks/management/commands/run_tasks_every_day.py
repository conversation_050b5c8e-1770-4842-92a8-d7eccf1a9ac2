from django.core.management import call_command
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Run task group every day"

    def handle(self, *args, **options):
        """Run daily tasks with error handling to ensure all tasks attempt to run"""

        tasks = [
            ("commit_all_reserved_product_stock", "Committing reserved product stock"),
            ("update_rates", "Updating currency exchange rates"),
            ("flushexpiredtokens", "Flushing expired tokens"),
            (
                "trigger_abandoned_basket_notifier",
                "Triggering abandoned basket notifications",
            ),
            ("clear_cache", "Clearing site-wide cache"),
        ]

        for command_name, description in tasks:
            try:
                self.stdout.write(f"Running: {description}...")
                call_command(command_name)
                self.stdout.write(
                    self.style.SUCCESS(f"✅ {description} completed successfully")
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ {description} failed: {str(e)}")
                )
                # Continue with other tasks even if one fails
                continue

        self.stdout.write(self.style.SUCCESS("Daily tasks execution completed"))
