#!/usr/bin/env python
"""
Test script to create sample notifications and test the API endpoint
"""
import os
import sys
import django
import pytest
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project.settings.dev")
django.setup()

from django.utils import timezone
from home.models import NotificationTicker
from django.test import RequestFactory
from home.views import ConfigViewSet
from rest_framework.test import APIRequestFactory


@pytest.mark.django_db
def create_sample_notifications():
    """Create sample notifications for testing"""
    print("🔧 Creating sample notifications...")

    # Clear existing notifications
    NotificationTicker.objects.all().delete()

    # Create a discount notification
    discount_notification = NotificationTicker.objects.create(
        title="Summer Sale Discount",
        message_key="notifications.summer_sale",
        message_fallback="🎉 Summer Sale! Get 20% off all items with code SUMMER20",
        priority=3,
        background_color="#ff6b35",
        text_color="#ffffff",
        copyable_code="SUMMER20",
        copy_button_key="notifications.copy_code",
        copy_button_fallback="Copy Code",
        link_url="https://example.com/sale",
        link_text_key="notifications.shop_now",
        link_text_fallback="Shop Now",
        start_date=timezone.now() - timedelta(hours=1),
        end_date=timezone.now() + timedelta(days=7),
        is_active=True,
    )

    # Create a shipping notification
    shipping_notification = NotificationTicker.objects.create(
        title="Free Shipping Announcement",
        message_key="notifications.free_shipping",
        message_fallback="🚚 Free shipping on orders over €50 to Spain and Portugal",
        priority=2,
        background_color="#28a745",
        text_color="#ffffff",
        start_date=timezone.now() - timedelta(hours=2),
        end_date=timezone.now() + timedelta(days=30),
        is_active=True,
    )

    # Create a new collection notification
    collection_notification = NotificationTicker.objects.create(
        title="New Collection Launch",
        message_key="notifications.new_collection",
        message_fallback="✨ New Autumn Collection now available! Limited edition pieces",
        priority=4,
        background_color="#6f42c1",
        text_color="#ffffff",
        link_url="https://example.com/collections/autumn",
        link_text_key="notifications.explore_collection",
        link_text_fallback="Explore Collection",
        start_date=timezone.now() - timedelta(minutes=30),
        end_date=timezone.now() + timedelta(days=14),
        is_active=True,
    )

    # Create an expired notification (should not appear)
    expired_notification = NotificationTicker.objects.create(
        title="Expired Sale",
        message_key="notifications.expired_sale",
        message_fallback="This sale has ended",
        priority=1,
        background_color="#dc3545",
        text_color="#ffffff",
        start_date=timezone.now() - timedelta(days=10),
        end_date=timezone.now() - timedelta(days=1),
        is_active=True,
    )

    # Create an inactive notification (should not appear)
    inactive_notification = NotificationTicker.objects.create(
        title="Inactive Notification",
        message_key="notifications.inactive",
        message_fallback="This notification is inactive",
        priority=2,
        background_color="#6c757d",
        text_color="#ffffff",
        start_date=timezone.now() - timedelta(hours=1),
        end_date=timezone.now() + timedelta(days=1),
        is_active=False,
    )

    print(f"✅ Created {NotificationTicker.objects.count()} notifications")
    return [discount_notification, shipping_notification, collection_notification]


@pytest.mark.django_db
def test_api_endpoint():
    """Test the notifications API endpoint"""
    print("\n🧪 Testing API endpoint...")

    # Create test data
    create_sample_notifications()

    factory = APIRequestFactory()
    request = factory.get("/api/v1/config/notifications/")

    # Create view instance
    view = ConfigViewSet()
    view.action = "notifications"

    # Call the notifications method
    response = view.notifications(request)

    print(f"📡 API Response Status: {response.status_code}")

    # Check if response has the new structure with settings and notifications
    if "settings" in response.data and "notifications" in response.data:
        settings = response.data["settings"]
        notifications = response.data["notifications"]
        print(f"📊 Number of active notifications: {len(notifications)}")
        print(f"🎛️ Global display type: {settings.get('display_type', 'ticker')}")

        for i, notification in enumerate(notifications, 1):
            print(f"\n📢 Notification {i}:")
            print(f"   Title: {notification['title']}")
            print(f"   Message: {notification['message']}")
            print(f"   Priority: {notification['priority']}")
            print(f"   Background: {notification['background_color']}")
            print(f"   Text Color: {notification['text_color']}")

            if notification.get("copyable_code"):
                print(f"   Copyable Code: {notification['copyable_code']}")
                print(f"   Copy Button: {notification['copy_button_text']}")

            if notification.get("link_url"):
                print(f"   Link URL: {notification['link_url']}")
                print(f"   Link Text: {notification['link_text']}")

            print(f"   Currently Active: {notification['is_currently_active']}")
    else:
        # Fallback for old API structure
        notifications = response.data
        print(f"📊 Number of active notifications: {len(notifications)}")

    # Verify we got some data
    assert response.status_code == 200
    assert len(notifications) > 0


@pytest.mark.django_db
def test_cache_behavior():
    """Test cache behavior"""
    print("\n🗄️ Testing cache behavior...")

    # Create test data
    create_sample_notifications()

    # Clear cache
    from django.core.cache import cache

    cache.delete("active_notifications")
    print("   Cache cleared")

    # First call should hit database
    notifications1 = NotificationTicker.get_active_notifications()
    print(f"   First call: {len(notifications1)} notifications")

    # Second call should hit cache
    notifications2 = NotificationTicker.get_active_notifications()
    print(f"   Second call: {len(notifications2)} notifications")

    # Verify cache is working
    cached_value = cache.get("active_notifications")
    if cached_value is not None:
        print("   ✅ Cache is working correctly")
    else:
        print("   ❌ Cache is not working")

    # Add assertions for pytest
    assert len(notifications1) > 0
    assert len(notifications2) > 0
    assert len(notifications1) == len(notifications2)  # Should be same from cache


@pytest.mark.django_db
def test_display_types():
    """Test global display settings"""
    print("\n🎨 Testing global display settings...")

    # Create test data
    create_sample_notifications()

    # Import and create notification settings
    from home.models import NotificationSettings

    # Test ticker mode
    settings = NotificationSettings.get_settings()
    settings.display_type = "ticker"
    settings.save()

    print(f"   Global display type: {settings.display_type}")
    print(f"   Carousel speed: {settings.carousel_rotation_speed}s")
    print(f"   Ticker speed: {settings.ticker_scroll_speed}")

    active_notifications = NotificationTicker.get_active_notifications()
    print(f"   Active notifications: {len(active_notifications)}")

    for notification in active_notifications:
        print(f"   📢 {notification.title}")

    # Test carousel mode
    settings.display_type = "carousel"
    settings.save()
    print(f"   Switched to: {settings.display_type}")

    # Add assertions for pytest
    assert len(active_notifications) > 0
    assert settings.display_type in ["ticker", "carousel"]


@pytest.mark.django_db
def main():
    """Main test function"""
    print("🚀 Testing Notification Ticker System")
    print("=" * 50)

    # Create sample data
    active_notifications = create_sample_notifications()

    # Test API endpoint
    api_data = test_api_endpoint()

    # Test cache behavior
    test_cache_behavior()

    # Test display types
    test_display_types()

    print("\n" + "=" * 50)
    print("🎉 Notification system test completed!")
    print("\n📝 Summary:")
    print(f"   - Created {len(active_notifications)} active notifications")
    print(f"   - API returned {len(api_data)} notifications")
    print("   - Cache system working")
    print("   - Both ticker and carousel types available")
    print("\n🔗 Next steps:")
    print("   1. Start the frontend development server")
    print("   2. Visit the website to see notifications in action")
    print("   3. Access Wagtail admin to manage notifications")
    print("   4. Test copy-to-clipboard functionality")


if __name__ == "__main__":
    main()
