from django.core.cache import cache
import logging
from typing import Tuple, Set
from django.db import transaction
import time

logger = logging.getLogger(__name__)


class DiscountValidator:
    """
    Validator to prevent discount code race conditions in high-traffic scenarios.

    Uses a multi-layered approach:
    1. Redis-based temporary reservations (3 minutes)
    2. Distributed locking for atomic operations
    3. Database-level constraints as final safety net
    4. Exponential backoff for retries
    """

    RESERVATION_TIMEOUT = 180  # 3 minutes for reservations
    LOCK_TIMEOUT = 10  # 10 seconds for locks (increased for high traffic)
    MAX_RETRIES = 5  # Maximum number of retries for lock acquisition
    RETRY_DELAY = 0.1  # Base delay for exponential backoff (100ms)

    @classmethod
    def _get_reserved_baskets(cls, discount_code: str) -> Set[str]:
        """Get set of baskets that have reserved this code, cleaning up expired ones"""
        active_key = f"discount_baskets:{discount_code}"
        try:
            value = cache.get(active_key)
            if value is None:
                return set()

            # Get the basket set
            if isinstance(value, set):
                baskets = value
            elif isinstance(value, str):
                baskets = set(x for x in value.split(",") if x)
            else:
                return set()

            # Clean up expired individual baskets
            current_time = time.time()
            valid_baskets = set()

            for basket_id in baskets:
                basket_key = f"discount_basket_time:{discount_code}:{basket_id}"
                reservation_time = cache.get(basket_key)

                if (
                    reservation_time
                    and (current_time - reservation_time) < cls.RESERVATION_TIMEOUT
                ):
                    valid_baskets.add(basket_id)
                # If reservation_time is None or expired, don't add to valid_baskets

            # Update the cache with cleaned baskets if any were removed
            if len(valid_baskets) != len(baskets):
                cls._set_reserved_baskets(discount_code, valid_baskets)

            return valid_baskets
        except Exception as e:
            logger.error(f"Error getting reserved baskets: {e}")
            return set()

    @classmethod
    def _set_reserved_baskets(
        cls, discount_code: str, baskets: Set[str], timeout: int = None
    ) -> bool:
        """Set reserved baskets with proper serialization"""
        active_key = f"discount_baskets:{discount_code}"
        try:
            if not baskets:
                cache.delete(active_key)
                return True
            value = ",".join(sorted(baskets)) if baskets else ""
            return bool(
                cache.set(active_key, value, timeout=timeout or cls.RESERVATION_TIMEOUT)
            )
        except Exception as e:
            logger.error(f"Error setting reserved baskets: {e}")
            return False

    @classmethod
    def check_discount_availability(
        cls, discount_code: str, basket_id: str
    ) -> Tuple[bool, str]:
        """
        Check if a discount code can be used in a basket with retry logic.

        Uses exponential backoff to handle high-traffic scenarios gracefully.
        """
        from shop.models import Discount

        for attempt in range(cls.MAX_RETRIES):
            try:
                # Verify discount exists first
                discount = Discount.objects.get(code__iexact=discount_code)

                # If no max_uses limit, always available
                if not discount.max_uses:
                    return True, "Available"

                redis_available = True
                current_baskets = set()

                try:
                    current_baskets = cls._get_reserved_baskets(discount_code)
                except Exception as e:
                    logger.error(f"Redis error in availability check: {e}")
                    redis_available = False

                # If basket already has this code, allow it
                if str(basket_id) in current_baskets:
                    return True, "Already reserved"

                # Check total usage (Redis + DB)
                total_usage = len(current_baskets) + discount.times_used

                if total_usage >= discount.max_uses:
                    return False, "This discount code is no longer available"

                if redis_available:
                    # Try to acquire lock with retry
                    lock_key = f"discount_lock:{discount_code}"
                    got_lock = False

                    try:
                        # Use exponential backoff for lock acquisition
                        lock_timeout = min(
                            cls.LOCK_TIMEOUT, cls.LOCK_TIMEOUT * (2**attempt)
                        )
                        got_lock = cache.add(lock_key, 1, timeout=lock_timeout)

                        if got_lock:
                            # Recheck under lock to prevent race conditions
                            current_baskets = cls._get_reserved_baskets(discount_code)
                            if str(basket_id) in current_baskets:
                                return True, "Already reserved"

                            # Final check before reservation
                            total_usage = len(current_baskets) + discount.times_used
                            if total_usage >= discount.max_uses:
                                return (
                                    False,
                                    "This discount code is no longer available",
                                )

                            # Reserve the discount
                            current_baskets.add(str(basket_id))

                            # Store individual basket reservation time
                            basket_time_key = (
                                f"discount_basket_time:{discount_code}:{basket_id}"
                            )
                            cache.set(
                                basket_time_key,
                                time.time(),
                                timeout=cls.RESERVATION_TIMEOUT,
                            )

                            if cls._set_reserved_baskets(
                                discount_code, current_baskets
                            ):
                                logger.info(
                                    f"Successfully reserved discount {discount_code} for basket {basket_id}"
                                )
                                return True, "Reserved successfully"
                            else:
                                logger.error(
                                    f"Failed to set reservation for {discount_code}"
                                )

                        else:
                            # Lock acquisition failed, will retry with backoff
                            if attempt < cls.MAX_RETRIES - 1:
                                delay = cls.RETRY_DELAY * (2**attempt)
                                logger.warning(
                                    f"Lock acquisition failed for {discount_code}, retrying in {delay}s"
                                )
                                time.sleep(delay)
                                continue
                            else:
                                logger.error(
                                    f"Failed to acquire lock for {discount_code} after {cls.MAX_RETRIES} attempts"
                                )

                    except Exception as e:
                        logger.error(
                            f"Error while holding lock (attempt {attempt + 1}): {e}"
                        )
                    finally:
                        if got_lock:
                            try:
                                cache.delete(lock_key)
                            except Exception as e:
                                logger.error(f"Error releasing lock: {e}")

                # Fallback to database-only check
                if discount.times_used >= discount.max_uses:
                    return False, "This discount code is no longer available"
                return True, "Available (fallback)"

            except Discount.DoesNotExist:
                return False, "Invalid discount code"
            except Exception as e:
                logger.error(f"Error checking discount (attempt {attempt + 1}): {e}")
                if attempt < cls.MAX_RETRIES - 1:
                    delay = cls.RETRY_DELAY * (2**attempt)
                    time.sleep(delay)
                    continue
                else:
                    return (
                        False,
                        f"Error validating discount code after {cls.MAX_RETRIES} attempts",
                    )

        return False, "Failed to validate discount code after maximum retries"

    @classmethod
    def release_discount(cls, discount_code: str, basket_id: str) -> bool:
        """Release a discount code reservation"""
        try:
            lock_key = f"discount_lock:{discount_code}"
            got_lock = False

            try:
                got_lock = cache.add(lock_key, 1, timeout=cls.LOCK_TIMEOUT)
                if got_lock:
                    current_baskets = cls._get_reserved_baskets(discount_code)
                    if str(basket_id) in current_baskets:
                        current_baskets.remove(str(basket_id))

                        # Clean up individual basket reservation time
                        basket_time_key = (
                            f"discount_basket_time:{discount_code}:{basket_id}"
                        )
                        cache.delete(basket_time_key)

                        return cls._set_reserved_baskets(discount_code, current_baskets)
                return True  # Consider "already released" a success
            finally:
                if got_lock:
                    try:
                        cache.delete(lock_key)
                    except Exception as e:
                        logger.error(f"Error releasing lock: {e}")

        except Exception as e:
            logger.error(f"Error releasing discount: {e}")
            return False

    @classmethod
    def confirm_discount_usage(
        cls, discount_code: str, basket_id: str, user=None
    ) -> bool:
        """
        Convert a reservation to actual usage when order is placed.
        This should be called within a database transaction.
        """
        from shop.models import Discount, DiscountUsage
        from django.db import transaction

        try:
            with transaction.atomic():
                # Get discount with select_for_update to prevent race conditions
                discount = Discount.objects.select_for_update().get(
                    code__iexact=discount_code
                )

                # Double-check usage limits at the database level
                if discount.max_uses and discount.times_used >= discount.max_uses:
                    logger.error(
                        f"Discount {discount_code} usage limit exceeded at confirmation"
                    )
                    return False

                # Increment usage counter
                discount.times_used += 1
                discount.save(update_fields=["times_used"])

                # Create usage record if user is provided
                if user and user.is_authenticated:
                    # Handle TokenUser objects by converting to actual User
                    actual_user = user
                    if hasattr(user, "to_user") and callable(user.to_user):
                        actual_user = user.to_user()

                    # Only create usage record if we have a valid User instance
                    if actual_user and hasattr(actual_user, "pk"):
                        DiscountUsage.objects.create(
                            discount=discount,
                            user=actual_user,
                            order_id=basket_id,
                            amount="0.00",
                        )

                # Release the reservation
                cls.release_discount(discount_code, basket_id)

                logger.info(
                    f"Confirmed usage of discount {discount_code} for basket {basket_id}"
                )
                return True

        except Discount.DoesNotExist:
            logger.error(f"Discount {discount_code} not found during confirmation")
            return False
        except Exception as e:
            logger.error(f"Error confirming discount usage: {e}")
            return False

    @classmethod
    def cleanup_expired_reservations(cls, discount_code: str = None) -> int:
        """
        Clean up expired reservations. Can be called periodically.
        If discount_code is None, cleans up all expired reservations.
        """
        cleaned_count = 0
        try:
            if discount_code:
                # Clean specific discount
                active_key = f"discount_baskets:{discount_code}"
                if cache.get(active_key) is not None:
                    cache.delete(active_key)
                    cleaned_count = 1
            else:
                # This would require a way to iterate over all discount keys
                # For now, we rely on Redis TTL for automatic cleanup
                logger.info("Relying on Redis TTL for automatic cleanup")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

        return cleaned_count
