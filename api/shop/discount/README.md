# Discount Code Race Condition Prevention

This document explains the multi-layered approach used to prevent race conditions with limited discount codes in high-traffic scenarios.

## The Problem

In high-traffic ecommerce sites, multiple users can simultaneously try to use the same limited discount code, leading to:

1. **Over-allocation**: More users get the discount than the limit allows
2. **Race conditions**: Database updates can be lost due to concurrent access
3. **Poor user experience**: Users get discount codes that fail at checkout

## Our Solution: Multi-Layered Defense

### Layer 1: Redis-Based Reservations

**File**: `shop/discount/validators.py`

- **Temporary Reservations**: When a user applies a discount code, it's reserved for 3 minutes
- **Distributed Locking**: Redis locks prevent concurrent access during reservation
- **Exponential Backoff**: Failed lock acquisitions retry with increasing delays
- **Automatic Cleanup**: Reservations expire automatically via Redis TTL

```python
# Example usage
is_available, message = DiscountValidator.check_discount_availability(
    discount_code="SAVE20", 
    basket_id="basket_123"
)
```

### Layer 2: Database Constraints

**File**: `shop/models/discount.py`

- **Check Constraints**: Prevent negative usage counts and over-allocation
- **Select for Update**: Atomic database operations during confirmation
- **Transaction Safety**: All critical operations wrapped in database transactions

```sql
-- Database constraints added
CONSTRAINT discount_times_used_non_negative CHECK (times_used >= 0)
CONSTRAINT discount_usage_within_limit CHECK (times_used <= max_uses OR max_uses IS NULL)
```

### Layer 3: Confirmation Process

**File**: `shop/discount/validators.py` - `confirm_discount_usage()`

- **Atomic Confirmation**: Converts reservations to actual usage atomically
- **Double-Check**: Validates limits again at the database level
- **Cleanup**: Releases reservations after confirmation

## Configuration

### Redis Settings

```python
# In settings.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

### Timeout Configuration

```python
class DiscountValidator:
    RESERVATION_TIMEOUT = 180  # 3 minutes for reservations
    LOCK_TIMEOUT = 10         # 10 seconds for locks
    MAX_RETRIES = 5           # Maximum retries for lock acquisition
    RETRY_DELAY = 0.1         # Base delay for exponential backoff
```

## Monitoring

### Management Command

```bash
# Monitor current usage
python manage.py monitor_discount_usage --monitor

# Monitor specific discount
python manage.py monitor_discount_usage --monitor --discount-code SAVE20

# Clean up expired reservations
python manage.py monitor_discount_usage --cleanup
```

### Logging

The system logs important events:

- Successful reservations
- Failed lock acquisitions
- Confirmation successes/failures
- Cleanup operations

## Best Practices

### 1. Set Appropriate Timeouts

- **Reservation Timeout**: Long enough for checkout process (3 minutes)
- **Lock Timeout**: Short enough to prevent deadlocks (10 seconds)
- **Retry Delays**: Exponential backoff to handle high traffic

### 2. Monitor Usage

- Use the management command to monitor high-value discount codes
- Set up alerts for codes approaching their limits
- Monitor Redis memory usage

### 3. Handle Failures Gracefully

- Always provide fallback mechanisms
- Show clear error messages to users
- Log failures for debugging

### 4. Database Maintenance

- Regularly clean up old DiscountUsage records
- Monitor database constraint violations
- Consider archiving old discount codes

## Performance Considerations

### Redis Memory Usage

Each active discount code uses minimal Redis memory:
- Key: `discount_baskets:CODE` (~30 bytes)
- Value: Comma-separated basket IDs (~20 bytes per basket)
- Lock: `discount_lock:CODE` (~25 bytes, temporary)

### Database Impact

- Minimal additional queries (1-2 per discount validation)
- Select for update only during confirmation
- Constraints add negligible overhead

## Troubleshooting

### Common Issues

1. **Redis Connection Failures**
   - System falls back to database-only validation
   - Check Redis connectivity and memory

2. **Lock Acquisition Timeouts**
   - Increase `LOCK_TIMEOUT` or `MAX_RETRIES`
   - Check for Redis performance issues

3. **Constraint Violations**
   - Indicates race condition reached database level
   - Review reservation logic and timeouts

### Debug Commands

```bash
# Check Redis keys
redis-cli KEYS "discount_*"

# Monitor Redis operations
redis-cli MONITOR

# Check database constraints
python manage.py dbshell
SELECT * FROM shop_discount WHERE times_used > max_uses;
```

## Testing

The system includes comprehensive tests covering:

- Basic reservation functionality
- Concurrent access scenarios
- Fallback mechanisms
- Edge cases and error conditions

Run tests with:
```bash
python manage.py test shop.tests.test_discount_validators
```
