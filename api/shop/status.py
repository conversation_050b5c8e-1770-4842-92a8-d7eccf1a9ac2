from django.utils.translation import pgettext_lazy
from salesman.orders.status import BaseOrderStatus


class OrderStatus(BaseOrderStatus):
    NEW = "NEW", pgettext_lazy("Status", "New")
    CREATED = "CREATED", pgettext_lazy("Status", "Created")
    HOLD = "HOLD", pgettext_lazy("Status", "Hold")
    FAILED = "FAILED", pgettext_lazy("Status", "Failed")
    CANCELLED = "CANCELLED", pgettext_lazy("Status", "Cancelled")
    PROCESSING = "PROCESSING", pgettext_lazy("Status", "Processing")
    SHIPPED = "SHIPPED", pgettext_lazy("Status", "Shipped")
    COMPLETED = "COMPLETED", pgettext_lazy("Status", "Completed")
    REFUNDED = "REFUNDED", pgettext_lazy("Status", "Refunded")

    @classmethod
    def get_payable(cls) -> list:
        return [
            cls.CREATED,
            cls.HOLD,
            cls.FAILED,
            cls.CANCELLED,
            cls.PROCESSING,
            cls.SHIPPED,
            cls.COMPLETED,
            cls.REFUNDED,
        ]

    @classmethod
    def get_transitions(cls) -> dict:
        return {
            "NEW": [cls.CREATED],
            "CREATED": [cls.HOLD, cls.FAILED, cls.CANCELLED, cls.PROCESSING],
            "HOLD": [cls.FAILED, cls.CANCELLED, cls.PROCESSING],
            "FAILED": [cls.CANCELLED, cls.PROCESSING],
            "CANCELLED": [],
            "PROCESSING": [cls.SHIPPED, cls.COMPLETED, cls.REFUNDED],
            "SHIPPED": [cls.COMPLETED, cls.REFUNDED],
            "COMPLETED": [cls.REFUNDED],
            "REFUNDED": [],
        }
