{% extends "modeladmin/create.html" %}
{% load wagtailadmin_tags modeladmin_tags static %}

{% block extra_js %}
  {{ block.super }}
  <script src="{% static "wagtailadmin/js/date-time-chooser.js" %}"></script>
{% endblock %}

{% block content %}
  {% block header %}
    {% include "wagtailadmin/shared/header.html" with title=view.get_page_title subtitle=view.get_page_subtitle icon=view.header_icon tabbed=1 merged=1 %}
  {% endblock %}

  <form id="report-form" method="POST" novalidate>
    {% csrf_token %}
  
    <li class="object multi-field">
      <div class="title-wrapper"><label>Report settings</label></div>
      <div class="object-layout">
        <div class="object-layout_big-part">
          <fieldset class="">
            <legend>Generate report</legend>
            <ul class="fields">
              <li class="required">
                <div class="field date_time_field admin_date_time_input" data-contentpath="datetime">
                  <label for="id_datetime_from">Date from:</label>
                  <div class="field-content">
                    <div class="input">
                      <input type="text" name="datetime_from" autocomplete="off" required="required"
                        id="id_datetime_from">
                      <script>initDateTimeChooser("id_datetime_from", { "dayOfWeekStart": 0, "format": "Y-m-d H:i", "formatTime": "H:i" });</script>
                      <span></span>
                    </div>
                  </div>
                </div>
              </li>
              <li>
                <div class="field date_time_field admin_date_time_input" data-contentpath="datetime">
                  <label for="id_datetime_to">Date to:</label>
                  <div class="field-content">
                    <div class="input">
                      <input type="text" name="datetime_to" autocomplete="off" required="" id="id_datetime_to">
                      <script>initDateTimeChooser("id_datetime_to", { "dayOfWeekStart": 0, "format": "Y-m-d H:i", "formatTime": "H:i" });</script>
                      <span></span>
                    </div>
                  </div>
                </div>
              </li>
              <li>
                <div class="field choice_field select" data-contentpath="select">
                  <label for="id_status">Has Owner:</label>
                  <div class="field-content">
                    <div class="input" name="status">
                      <select name="has_owner" id="id_status">
                        <option value="1">Yes</option>
                        <option value="0">All</option>
                      </select>
                      <span></span>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </fieldset>
        </div>
      </div>
    </li>
  
    {% block footer %}
    <footer class="footer">
      <ul>
        <li class="actions footer__container">
          {% block form_actions %}
          <div class="dropdown dropup dropdown-button match-width">
            <button type="submit" class="button action-save button-longrunning" data-clicked-text="Generating CSV…">
              {% icon name="spinner" %}<em>Generate report</em>
            </button>
          </div>
          {% endblock %}
        </li>
      </ul>
    </footer>
    {% endblock %}
  </form>
{% endblock %}
