<mjml>
  <mj-head>
    <mj-attributes>
      <mj-breakpoint width="360px" />
      <mj-all font-family="Helvetica, sans-serif" padding="0" line-height="20px" />
      <mj-class name="body" color="#000000" background-color="#eeeeee" />
      <mj-class name="section" padding="0 16px" background-color="#ffffff" />
      <mj-class name="title" font-size="24px" padding="32px 0" />
      <mj-class name="title-gap-top-none" padding="0 0 32px" />
      <mj-class name="button" border-radius="0" background-color="transparent" color="#000000" border="1px solid #000000" font-weight="bold" align="left" />
      <mj-class name="text-regular" font-size="16px" />
      <mj-class name="text-large" font-size="18px" />
      <mj-class name="align-right" align="right" />
      <mj-class name="text-small" font-size="12px" />
      <mj-class name="text-gray" color="#b3b3b3" />
      <mj-class name="border" border-color="#000000" border-width="1px" />
      <mj-class name="border-light" border-color="#b3b3b3" border-width="1px" />
      <mj-class name="gap-y-32" padding="32px 0" />
      <mj-class name="gap-top-32" padding="32px 0 0" />
      <mj-class name="gap-bottom-32" padding="0 0 32px" />
      <mj-class name="gap-bottom-16" padding="0 0 16px" />
      <mj-class name="gap-bottom-8" padding="0 0 8px" />
      <mj-class name="gap-left-8" padding="0 0 0 8px" />
      <mj-class name="vertical-middle" vertical-align="middle" />
      <mj-class name="brand-logo" align="left" padding="10px 0 6px" width="48px" />
      <mj-class name="item-image" align="left" padding="0" width="90px" />
      <mj-class name="totals-row" padding="0 0 16px" />
      <mj-class name="footer" padding="0 0 10px" />
      <mj-class name="footer-text" align="center" />
    </mj-attributes>
    <mj-style>
      a {
        color: black;
        text-decoration: underline;
      }
      .dot {
        background: #b3b3b3;
        display: inline-block;
        vertical-align: middle;
        height: 8px;
        width: 8px;
        border-radius: 8px;
      }
      .dot-red {
        background: #ec6d6d;
      }
      .dot-yellow {
        background: #fbd37c;
      }
      .dot-green {
        background: #b6fb7c;
      }
    </mj-style>
  </mj-head>

  <mj-body mj-class="body">
    <mj-raw>{% load shop_tags static %}</mj-raw>

    <mj-section mj-class="section">
      <mj-column>
        <mj-raw>{% static "shop/email-logo.png" as logo_url %}</mj-raw>
        <mj-image mj-class="brand-logo" src="{{ logo_url }}"></mj-image>
      </mj-column>
    </mj-section>

    <mj-include path="./order_es/index.mjml" />

  </mj-body>
</mjml>