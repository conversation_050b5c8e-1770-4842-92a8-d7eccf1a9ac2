<mj-section mj-class="section">
  <mj-column>
    <mj-divider mj-class="border"></mj-divider>

    <mj-raw>{% if order.status == "PROCESSING" %}</mj-raw>
      <mj-text mj-class="title">
        <strong>¡Tu pedido se ha realizado correctamente!</strong>
      </mj-text>
      <mj-text mj-class="text-regular gap-bottom-32">
        Ya estamos preparando tu pedido y cuando esté listo para ser enviado, recibirás un correo con un código de rastreo para que puedas hacer el seguimiento.
      </mj-text>
    <mj-raw>{% elif order.status == "SHIPPING" %}</mj-raw>
      <mj-text mj-class="title">
        <strong>Tu pedido ya está preparado para ser enviado. </strong>
      </mj-text>
      <mj-text mj-class="text-regular gap-bottom-32">
        Haciendo click en este código de rastreo podrás hacer el seguimiento del envío.
      </mj-text>
    <mj-raw>{% elif order.status == "CANCELLED" %}</mj-raw>
      <mj-text mj-class="title">
        <strong>Tu pedido ha sido cancelado correctamente.</strong>
      </mj-text>
      <mj-text mj-class="text-regular gap-bottom-32">
        Recibirás tu reembolso en un plazo aproximado de unos 10 días laborables.
      </mj-text>
    <mj-raw>{% elif order.status == "REFUNDED" %}</mj-raw>
      <mj-text mj-class="title">
        <strong>Hemos realizado el reembolso de tu pedido.</strong>
      </mj-text>
      <mj-text mj-class="text-regular gap-bottom-32">
        El abono se hará efectivo en un plazo aproximado de 10 días laborables.
      </mj-text>
    <mj-raw>{% endif %}</mj-raw>

  </mj-column>
</mj-section>

<mj-section mj-class="section">
  <mj-column>
    <mj-divider mj-class="border"></mj-divider>
    <mj-text mj-class="title"><strong>Detalles del pedido</strong></mj-text>
    <mj-text mj-class="text-regular gap-bottom-8">
      <strong>Referencia del pedido: {{ order.ref }}</strong>
    </mj-text>
    <mj-text mj-class="text-small">Fecha de asunto: <strong>{{ order.date_created_display }}</strong></mj-text>
  </mj-column>
</mj-section>

<mj-section mj-class="section">
  <mj-column>
    <mj-text mj-class="text-small gap-bottom-32">
      <strong>{{ order.status_display }}</strong>

      <!-- Dot -->
      <mj-raw>{% if order.status == "CANCELLED" or order.status == "FAILED" %}</mj-raw>
      <span class="dot dot-red"></span>
      <mj-raw>{% elif order.status == "PROCESSING" or order.status == "SHIPPED" or order.status == "COMPLETED" %}</mj-raw>
      <span class="dot dot-green"></span>
      <mj-raw>{% elif order.status == "CREATED" %}</mj-raw>
      <span class="dot dot-yellow"></span>
      <mj-raw>{% else %}</mj-raw>
      <span class="dot dot-gray"></span>
      <mj-raw>{% endif %}</mj-raw>
    </mj-text>

    <mj-raw>{% if order.extra.tracking_url and order.status != "COMPLETED" %}</mj-raw>
    <mj-button mj-class="button gap-bottom-32" href="{{ order.extra.tracking_url }}">
      Rastrea tu orden
    </mj-button>
    <mj-raw>{% endif %}</mj-raw>
    <mj-divider mj-class="border"></mj-divider>
  </mj-column>
</mj-section>