<mj-section mj-class="section">
  <mj-group>
    <mj-column width="100%">
      <mj-text mj-class="title title-gap-top-none"><strong>Order Payment</strong></mj-text>
    </mj-column>
  </mj-group>
</mj-section>

<mj-section mj-class="section">
  <mj-group>
    <mj-column>
      <mj-text mj-class="totals-row"><strong>Subtotal</strong></mj-text>
    </mj-column>
    <mj-column>
      <mj-text mj-class="totals-row align-right">
        <strong>
          <mj-raw>
            {% if order.extra.subtotal_without_tax %}{{ order.extra.subtotal_without_tax }}
            {% else %}{{ order.subtotal }}{% endif %}
          </mj-raw>
        </strong>
      </mj-text>
    </mj-column>
  </mj-group>
</mj-section>

<mj-raw>{% for row in order.extra_rows %}</mj-raw>
<mj-section mj-class="section">
  <mj-group>
    <mj-column>
      <mj-text mj-class="totals-row">
        <mj-raw>{% if row.modifier == "shipping" %}{{ row.extra.caption }}
          {% elif row.modifier == "tax" %}{{ row.label }} ({{ row.extra.percent }}%)
          {% else %}{{ row.label }}
          {% endif %}
        </mj-raw>
      </mj-text>
    </mj-column>
    <mj-column>
      <mj-text mj-class="totals-row align-right">{{ row.amount }}</mj-text>
    </mj-column>
  </mj-group>
</mj-section>
<mj-raw>{% endfor %}</mj-raw>

<mj-section mj-class="section">
  <mj-group>
    <mj-column>
      <mj-text mj-class="text-large"><strong>Total</strong></mj-text>
    </mj-column>
    <mj-column>
      <mj-text mj-class="text-large align-right gap-bottom-32"><strong>{{ order.total }}</strong></mj-text>
    </mj-column>
  </mj-group>
</mj-section>
