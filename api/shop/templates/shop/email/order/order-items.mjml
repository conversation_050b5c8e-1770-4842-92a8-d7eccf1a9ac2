
<mj-section mj-class="section">
  <mj-group>
    <mj-column width="100%">
      <mj-text mj-class="title"><strong>Order Items</strong></mj-text>
    </mj-column>
  </mj-group>
</mj-section>

<mj-raw>{% for item in order.items %}</mj-raw>
<mj-section mj-class="section">
  <mj-group>
    <mj-raw>{% if item.product.image %}</mj-raw>
    <mj-column width="25%" mj-class="vertical-middle">
      <mj-image mj-class="item-image" src="{{ item.product.image.url }}"></mj-image>
    </mj-column>
    <mj-column width="10%" mj-class="vertical-middle">
      &nbsp;
    </mj-column>
    <mj-raw>{% endif %}</mj-raw>
    <mj-column width="65%">
      <mj-text>
        <strong>{{ item.product.name }}</strong>
        <mj-raw><br>{% if item.extra.weight %}{{ item.extra.weight }} Kg{% endif %}</mj-raw>
        &nbsp;<br>
        Quantity: {{ item.quantity }}<br>
        &nbsp;<br>
        <strong>{{ item.total }}</strong>
      </mj-text>
    </mj-column>
  </mj-group>
</mj-section>

<mj-section mj-class="section">
  <mj-column>
    <mj-divider mj-class="border gap-y-32"></mj-divider>
  </mj-column>
</mj-section>
<mj-raw>{% endfor %}</mj-raw>