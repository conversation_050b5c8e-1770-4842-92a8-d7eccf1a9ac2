<mj-section mj-class="section">
  <mj-column>
    <mj-divider mj-class="border"></mj-divider>
    <mj-text mj-class="title">
      <strong>Thank you for your order!</strong>
    </mj-text>
    <mj-text mj-class="text-regular gap-bottom-32">
      This is our variable order message
    </mj-text>
  </mj-column>
</mj-section>

<mj-section mj-class="section">
  <mj-column>
    <mj-divider mj-class="border"></mj-divider>
    <mj-text mj-class="title"><strong>Order Details</strong></mj-text>
    <mj-text mj-class="text-regular gap-bottom-8">
      <strong>Order ref: {{ order.ref }}</strong>
    </mj-text>
    <mj-text mj-class="text-small">Issue date: <strong>{{ order.date_created_display }}</strong></mj-text>
  </mj-column>
</mj-section>

<mj-section mj-class="section">
  <mj-column>
    <mj-text mj-class="text-small gap-bottom-32">
      <strong>{{ order.status_display }}</strong>

      <!-- Dot -->
      <mj-raw>{% if order.status == "CANCELLED" or order.status == "FAILED" %}</mj-raw>
      <span class="dot dot-red"></span>
      <mj-raw>{% elif order.status == "PROCESSING" or order.status == "SHIPPED" or order.status == "COMPLETED" %}</mj-raw>
      <span class="dot dot-green"></span>
      <mj-raw>{% elif order.status == "CREATED" %}</mj-raw>
      <span class="dot dot-yellow"></span>
      <mj-raw>{% else %}</mj-raw>
      <span class="dot dot-gray"></span>
      <mj-raw>{% endif %}</mj-raw>
    </mj-text>

    <mj-raw>{% if order.extra.tracking_url and order.status != "COMPLETED" %}</mj-raw>
    <mj-button mj-class="button gap-bottom-32" href="{{ order.extra.tracking_url }}">
      Track your order
    </mj-button>
    <mj-raw>{% endif %}</mj-raw>
    <mj-divider mj-class="border"></mj-divider>
  </mj-column>
</mj-section>