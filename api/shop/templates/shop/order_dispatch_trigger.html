{% extends "wagtailadmin/base.html" %}
{% load i18n modeladmin_tags %}

{% block titletag %}{{ view.get_meta_title }}{% endblock %}

{% block content %}

    {% block header %}
        {% include "wagtailadmin/shared/header.html" with title=view.get_page_title subtitle=view.get_page_subtitle icon=view.header_icon %}
    {% endblock %}

    {% block content_main %}
        <div class="nice-padding">
            <p>Are you sure you want to dispatch order status: <strong>{{ request.GET.status }}</strong>?</p>
            <form action="{{ request.path }}?status={{ request.GET.status }}" method="POST">
                {% csrf_token %}
                <input type="submit" value="Yes, dispatch" class="button serious" />
                <a href="{% url 'salesman_order_modeladmin_edit' instance.id %}#tab-history" class="button button-secondary">No, go back</a>
            </form>
        </div>
    {% endblock %}
{% endblock %}
