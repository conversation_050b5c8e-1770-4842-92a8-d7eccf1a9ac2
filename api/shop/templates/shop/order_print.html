{% load i18n shop_tags %}
<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>{% translate "Invoice" context "Invoice" %} - {{ order_data.ref }}</title>
  <style type="text/css">
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
      font-size: 15px;
      line-height: 1.35;
      color: #111111;
    }

    table {
      border-collapse: collapse;
      table-layout: fixed;
    }

    table.full {
      width: 100%;
    }

    td,
    th {
      vertical-align: top;
      text-align: left;
      border: 0;
      padding: 1.2em 0;
    }

    td.left,
    th.left {
      text-align: left;
    }

    td.right,
    th.right {
      text-align: right;
    }

    td.center,
    th.center {
      text-align: center;
    }

    td.blank,
    th.blank {
      border: 0 !important;
    }

    .intro {
      border-bottom: 1px solid #dfdfdf;
      padding-bottom: 1.5em;
    }

    .intro-info p {
      float: left;
      margin: 0 3em 0 0;
    }

    .contact {
      margin-top: 1.5em;
    }

    .contact td {
      padding: 0;
    }

    .order {
      margin-top: 3em;
    }

    .order td {
      border-bottom: 1px solid #dfdfdf;
    }

    .order th {
      border-bottom: 2px solid #111111;
    }

    .order td:not(.left),
    .order th:not(.left) {
      text-align: right;
    }

    .bubble {
      margin-top: 1.5em;
      padding: 0 1.5em;
      border: 2px solid #111111;
      border-radius: 10px;
      font-size: 1.3em;
      text-transform: uppercase;
    }

    .icon {
      width: 1.3em;
      height: 1.3em;
    }

    .icon--left {
      float: left;
      padding-right: 0.5em;
    }

    p {
      margin: 0 0 0.5em;
    }

    h1,
    h2,
    h3,
    h4 {
      margin-top: 0;
    }

    .no-border {
      border: 0 !important;
    }

    .no-margin {
      margin: 0 !important;
    }

    .no-padding {
      padding: 0 !important;
    }

    .clear {
      clear: both;
    }
  </style>
</head>

<body>
  {% get_order_info order as order_info %}

  <div class="wrapper">
    <div class="intro">
      <h1>{% translate "Invoice" context "Invoice" %}</h1>

      <div class="intro-info">
        <p><strong>{% translate "Order ID" context "Invoice" %}</strong><br>{{ order_data.ref }}</p>
        <p style="width: 300px"><strong>{% translate "Issue date" context "Invoice" %}</strong><br>{{
          order.date_created|date:"DATETIME_FORMAT" }}</p>
      </div>
      <div class="clear"></div>
    </div>

    <table class="contact full">
      <tbody>
        <tr>
          <td>
            <h4>{% translate "Supplier" context "Invoice" %}</h4>
            {% get_company_address as company_address %}
            <p>{{ company_address|linebreaksbr }}</p>
            <br>
            {% get_company_email as company_email %}
            <p class="no-margin">
              <svg class="icon icon--left" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M22 6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6M20 6L12 11L4 6H20M20 18H4V8L12 13L20 8V18Z">
              </svg>
              {{ company_email }}
            </p>
          </td>
          <td>
            <h4>{% translate "Shipping address" context "Invoice" %}</h4>
            {% parseaddress order_data.shipping_address as addr %}
            {% if addr %}
            <p>
              {{ addr.first_name }} {{ addr.last_name }}<br>
              {{ addr.address }}<br>
              {{ addr.address_2 }}<br>
              {{ addr.postal_code }} {{ addr.city }}<br>
              {{ addr.country }}<br>
              {{ addr.state }}<br>
            </p>
            {% else %}
            <p>{{ order_data.shipping_address|linebreaksbr }}</p>
            {% endif %}
            <br>
            <p>
              <svg class="icon icon--left" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M22 6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6M20 6L12 11L4 6H20M20 18H4V8L12 13L20 8V18Z">
              </svg>
              {{ order_data.email|default:"-" }}
            </p>
            {% if addr %}
            <p class="no-margin">
              <svg class="icon icon--left" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z">
              </svg>
              {{ addr.phone }}
            </p>
            {% endif %}
          </td>
          <td>
            <h4>{% translate "Billing address" context "Invoice" %}</h4>
            {% parseaddress order_data.billing_address as addr %}
            {% if addr %}
            <p>
              {{ addr.first_name }} {{ addr.last_name }}<br>
              {{ addr.address }}<br>
              {{ addr.address_2}}<br>
              {{ addr.postal_code }} {{ addr.city }}<br>
              {{ addr.country }}<br>
              {{ addr.state }}<br>
            </p>
            {% else %}
            <p>{{ order_data.billing_address|linebreaksbr }}</p>
            {% endif %}
            <br>
            <p>
              <svg class="icon icon--left" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M22 6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6M20 6L12 11L4 6H20M20 18H4V8L12 13L20 8V18Z">
              </svg>
              {{ order_data.email|default:"-" }}
            </p>
            {% if addr %}
            <p class="no-margin">
              <svg class="icon icon--left" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z">
              </svg>
              {{ addr.phone }}
            </p>
            {% endif %}
          </td>
        </tr>
      </tbody>
    </table>

    <table class="order full">
      <thead>
        <tr>
          <th class="left" colspan="2">{% translate "Article" context "Invoice" %}</th>
          <th>{% translate "Unit price" context "Invoice" %}</th>
          <th>{% translate "Quantity" context "Invoice" %}</th>
          <th>{% translate "Subtotal" context "Invoice" %}</th>
          <th>{% translate "Tax" context "Invoice" %}</th>
          <th>{% translate "Tax" context "Invoice" %}</th>
          <th>{% translate "Total" context "Invoice" %}</th>
        </tr>
        <thead>
        <tbody>
          {% for item in order_info.items %}
          <tr>
            <td class="left" colspan="2">{{ item.name }}</td>
            <td>{% priceformat item.unit_price %}</td>
            <td>{{ item.quantity }}</td>
            <td>{% priceformat item.subtotal_no_tax %}</td>
            <td>{{ order_info.tax_percent }}%</td>
            <td>{% priceformat item.tax_amount %}</td>
            <td>{% priceformat item.total %}</td>
          </tr>
          {% endfor %}
        </tbody>
    </table>

    <table class="totals full">
      <tr>
        <td class="no-border" colspan="4" style="padding-top: 2.5em;">
          {% if order_info.shipping %}
          <p>
            <strong>{% translate "Shipping method" context "Invoice" %}</strong><br>
            {{ order_info.shipping.extra.caption|default:order_info.shipping.label }}
          </p>
          {% endif %}
          <p>
            <strong>{% translate "Country of origin" context "Invoice" %}</strong><br>
            Spain
          </p>
        </td>
        <td class="no-border" colspan="3">
          <table class="order full no-margin">
            <tr>
              <td class="left">{% translate "Subtotal" context "Invoice" %}</td>
              <td>{{ order_data.subtotal }}</td>
            </tr>
            {% for row in order_data.extra_rows %}
            <tr>
              <td class="left">
                {% if row.modifier == "shipping" %}{% translate "Shipping" context "Invoice" %}
                {% elif row.modifier == "tax" %}{% translate "Tax" context "Invoice" %} ({{ row.extra.percent }}%)
                {% else %}{{ row.label }}{% endif %}
              </td>
              <td>{{ row.amount }}</td>
            </tr>
            {% endfor %}
            <tr>
              <td class="left no-border">{% translate "Total" context "Invoice" %}</td>
              <td class="no-border">{{ order_data.total }}</td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    <div class="bubble">
      <table class="full">
        <tbody>
          <tr>
            <td>{% translate "Paid out" context "Invoice" %}</td>
            <td class="right">{{ order_data.amount_paid }}</td>
            <td></td>
            <td>{% translate "To pay" context "Invoice" %}</td>
            <td class="right">{{ order_data.amount_outstanding }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</body>

</html>
