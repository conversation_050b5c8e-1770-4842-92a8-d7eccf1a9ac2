{% extends "wagtailadmin/base.html" %}
{% load i18n %}

{% block titletag %}{% trans "Cancellation Preview" %} - {{ instance.ref }}{% endblock %}

{% block content %}
    <header class="header">
        <div class="row">
            <div class="left">
                <div class="col">
                    <h1 class="icon icon-warning">
                        {% trans "Cancellation Preview" %} - {{ instance.ref }}
                    </h1>
                </div>
            </div>
        </div>
    </header>

    <div class="nice-padding">
        <div class="row">
            <div class="col12">
                
                {% if can_be_cancelled %}
                    <div class="help-block help-info">
                        <strong>{% trans "Order Status:" %}</strong> {{ current_status }}<br>
                        {% trans "This order can be cancelled or refunded. Below is a preview of what would happen:" %}
                    </div>
                {% else %}
                    <div class="help-block help-warning">
                        <strong>{% trans "Order Status:" %}</strong> {{ current_status }}<br>
                        {% trans "This order cannot be cancelled because it has not been processed yet, or it has already been cancelled/refunded." %}
                    </div>
                {% endif %}

                {% if cancellation_summary.total_items > 0 %}
                    <h2>{% trans "Items to be restored" %}</h2>
                    
                    <table class="listing">
                        <thead>
                            <tr>
                                <th>{% trans "Product" %}</th>
                                <th>{% trans "Code" %}</th>
                                <th>{% trans "Quantity to Restore" %}</th>
                                <th>{% trans "Current Stock" %}</th>
                                <th>{% trans "Stock After Restore" %}</th>
                                <th>{% trans "Current Sales" %}</th>
                                <th>{% trans "Sales After Adjustment" %}</th>
                                <th>{% trans "Amount to Adjust" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in cancellation_summary.items %}
                                <tr>
                                    <td>{{ item.variant_name }}</td>
                                    <td><code>{{ item.variant_code }}</code></td>
                                    <td class="text-center">
                                        <strong>+{{ item.quantity_to_restore }}</strong>
                                    </td>
                                    <td class="text-center">
                                        {% if item.is_limited_stock %}
                                            {{ item.current_stock }}
                                        {% else %}
                                            <em>{% trans "Unlimited" %}</em>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if item.is_limited_stock %}
                                            <strong>{{ item.stock_after_restore }}</strong>
                                        {% else %}
                                            <em>{% trans "Unlimited" %}</em>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">{{ item.current_sold_quantity }}</td>
                                    <td class="text-center">
                                        <strong>{{ item.sold_after_adjustment }}</strong>
                                    </td>
                                    <td class="text-right">
                                        <strong>-{{ item.amount_to_adjust }}</strong>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <div class="row">
                        <div class="col6">
                            <h3>{% trans "Summary" %}</h3>
                            <ul>
                                <li><strong>{% trans "Total items:" %}</strong> {{ cancellation_summary.total_items }}</li>
                                <li><strong>{% trans "Items with limited stock:" %}</strong> 
                                    {% if cancellation_summary.has_limited_stock %}
                                        {% trans "Yes" %}
                                    {% else %}
                                        {% trans "No" %}
                                    {% endif %}
                                </li>
                            </ul>
                        </div>
                        <div class="col6">
                            <h3>{% trans "What will happen" %}</h3>
                            <ul>
                                <li>{% trans "Stock will be restored to variants (limited stock only)" %}</li>
                                <li>{% trans "Sales statistics will be adjusted" %}</li>
                                <li>{% trans "All changes will be logged" %}</li>
                                <li>{% trans "Changes cannot be undone automatically" %}</li>
                            </ul>
                        </div>
                    </div>

                    {% if can_be_cancelled %}
                        <div class="help-block help-warning">
                            <strong>{% trans "Important:" %}</strong>
                            {% trans "This is only a preview. To actually cancel or refund this order, change the order status in the main order edit page." %}
                        </div>
                    {% endif %}

                {% else %}
                    <div class="help-block help-info">
                        {% trans "This order has no items to restore." %}
                    </div>
                {% endif %}

                <footer class="footer">
                    <ul class="footer__container">
                        <li class="footer__item">
                            <a href="{{ view.edit_url }}" class="button">
                                {% trans "Back to order" %}
                            </a>
                        </li>
                    </ul>
                </footer>

            </div>
        </div>
    </div>

{% endblock %}

{% block extra_css %}
    <style>
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .help-block { 
            padding: 1em; 
            margin: 1em 0; 
            border-radius: 3px; 
        }
        .help-info { 
            background-color: #e1f5fe; 
            border-left: 4px solid #0288d1; 
        }
        .help-warning { 
            background-color: #fff3e0; 
            border-left: 4px solid #f57c00; 
        }
        .listing th, .listing td {
            padding: 0.5em;
            border-bottom: 1px solid #ddd;
        }
        .listing th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
    </style>
{% endblock %}
