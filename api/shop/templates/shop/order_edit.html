{% extends "modeladmin/edit.html" %}
{% load wagtailadmin_tags %}

{% block footer %}
<footer class="footer">
  <ul>
    <li class="actions footer__container">
      <div class="dropdown dropup dropdown-button match-width">
        <button type="submit" class="button action-save button-longrunning" data-clicked-text="Saving…">
          {% icon name="spinner" %}<em>Save</em>
        </button>
        <div class="dropdown-toggle"><svg class="icon icon-arrow-up icon" aria-hidden="true" focusable="false"><use href="#icon-arrow-up"></use></svg></div>
        <ul>
          <li>
            <a href="{% url 'salesman_order_modeladmin_delete' instance.id %}" class="button">Delete</a>
          </li>
          <li>
            <a href="{% url 'salesman_order_modeladmin_create' %}?from_order={{ instance.ref }}" class="button">Duplicate</a>
          </li>
          <li>
            <a href="{% url 'salesman_order_modeladmin_print' instance.id %}?locale=es" class="button" target="_blank">Print</a>
          </li>
        </ul>
      </div>
    </li>

    {# Add refund button if needed. #}
    {% url 'salesman_order_modeladmin_refund' instance.id as order_refund_url %}
    {% if order_refund_url %}
      <li class="preview footer__container">
        <a href="{{ order_refund_url }}" class="button">Refund order</a>
      </li>
    {% endif %}
  </ul>
</footer>
{% endblock %}
