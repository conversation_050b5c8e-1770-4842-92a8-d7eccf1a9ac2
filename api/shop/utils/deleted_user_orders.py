"""
Utilities for handling orders from deleted users.
Provides functions to identify, display, and manage orders from deleted users.
"""
from typing import Optional, List, Dict, Any
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from salesman.orders.models import Order


def is_deleted_user_order(order: Order) -> bool:
    """
    Check if an order belongs to a deleted user.
    
    Args:
        order: Order instance to check
        
    Returns:
        bool: True if order is from a deleted user, False otherwise
    """
    return (
        order.user is None and 
        order.email and 
        "deleted-user-" in order.email and 
        "@anonymized.local" in order.email
    )


def get_original_user_id(order: Order) -> Optional[int]:
    """
    Extract the original user ID from a deleted user's anonymized email.
    
    Args:
        order: Order instance from a deleted user
        
    Returns:
        int: Original user ID if extractable, None otherwise
    """
    if not is_deleted_user_order(order):
        return None
        
    try:
        # Extract ID from "<EMAIL>"
        email_parts = order.email.split("@")[0]  # "deleted-user-123"
        user_id_str = email_parts.split("-")[-1]  # "123"
        return int(user_id_str)
    except (ValueError, IndexError):
        return None


def format_customer_display(order: Order) -> str:
    """
    Format customer display for admin interface, handling deleted users.
    
    Args:
        order: Order instance
        
    Returns:
        str: HTML formatted customer display
    """
    if order.user:
        # Regular user - show link to user admin
        from django.urls import reverse
        try:
            url = reverse("user_user_modeladmin_edit", args=[order.user.pk])
            return format_html(
                '<a href="{}" title="View customer details">{}</a>',
                url,
                order.user.get_full_name() or order.user.email
            )
        except:
            return str(order.user)
    
    elif is_deleted_user_order(order):
        # Deleted user - show special indicator
        original_id = get_original_user_id(order)
        return format_html(
            '<span class="deleted-user" title="This order was placed by a deleted customer (original ID: {})">'
            '<span class="icon icon-cross" style="color: #cd3238;"></span> '
            'Deleted Customer (ID: {})'
            '</span>',
            original_id or "unknown",
            original_id or "unknown"
        )
    
    else:
        # Guest order or other case
        return format_html(
            '<span class="guest-user" title="Guest order">'
            '<span class="icon icon-user" style="color: #999;"></span> '
            'Guest'
            '</span>'
        )


def get_deleted_user_orders_summary() -> Dict[str, Any]:
    """
    Get summary statistics about orders from deleted users.
    
    Returns:
        dict: Summary with counts, revenue, etc.
    """
    deleted_user_orders = Order.objects.filter(
        user__isnull=True,
        email__contains="deleted-user-",
        email__endswith="@anonymized.local"
    )
    
    total_count = deleted_user_orders.count()
    total_revenue = sum(order.total for order in deleted_user_orders)
    
    # Group by status
    status_breakdown = {}
    for order in deleted_user_orders:
        status = order.status
        if status not in status_breakdown:
            status_breakdown[status] = {"count": 0, "revenue": 0}
        status_breakdown[status]["count"] += 1
        status_breakdown[status]["revenue"] += order.total
    
    return {
        "total_count": total_count,
        "total_revenue": total_revenue,
        "status_breakdown": status_breakdown,
        "orders": deleted_user_orders
    }


def add_deleted_user_indicator_css() -> str:
    """
    Return CSS styles for deleted user indicators in admin.
    
    Returns:
        str: CSS styles
    """
    return """
    <style>
    .deleted-user {
        color: #cd3238;
        font-weight: bold;
        padding: 2px 6px;
        background-color: #ffeaea;
        border-radius: 3px;
        border: 1px solid #ffcccc;
    }
    
    .guest-user {
        color: #666;
        font-style: italic;
    }
    
    .deleted-user .icon {
        margin-right: 4px;
    }
    
    .guest-user .icon {
        margin-right: 4px;
    }
    
    /* Tooltip styles */
    .deleted-user:hover,
    .guest-user:hover {
        cursor: help;
    }
    </style>
    """


def generate_deleted_user_report() -> str:
    """
    Generate a detailed report about orders from deleted users.
    
    Returns:
        str: HTML report
    """
    summary = get_deleted_user_orders_summary()
    
    if summary["total_count"] == 0:
        return "<p>No orders from deleted users found.</p>"
    
    html = f"""
    <div class="deleted-user-report">
        <h3>Orders from Deleted Users Report</h3>
        
        <div class="summary">
            <p><strong>Total Orders:</strong> {summary['total_count']}</p>
            <p><strong>Total Revenue:</strong> €{summary['total_revenue']:.2f}</p>
        </div>
        
        <h4>Breakdown by Status:</h4>
        <table class="listing">
            <thead>
                <tr>
                    <th>Status</th>
                    <th>Count</th>
                    <th>Revenue</th>
                </tr>
            </thead>
            <tbody>
    """
    
    for status, data in summary["status_breakdown"].items():
        html += f"""
                <tr>
                    <td>{status}</td>
                    <td>{data['count']}</td>
                    <td>€{data['revenue']:.2f}</td>
                </tr>
        """
    
    html += """
            </tbody>
        </table>
        
        <h4>Recent Orders:</h4>
        <table class="listing">
            <thead>
                <tr>
                    <th>Order Ref</th>
                    <th>Original User ID</th>
                    <th>Status</th>
                    <th>Total</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
    """
    
    recent_orders = summary["orders"].order_by("-date_created")[:10]
    for order in recent_orders:
        original_id = get_original_user_id(order)
        html += f"""
                <tr>
                    <td><a href="/admin/orders/{order.id}/">{order.ref}</a></td>
                    <td>{original_id or 'Unknown'}</td>
                    <td>{order.status}</td>
                    <td>€{order.total:.2f}</td>
                    <td>{order.date_created.strftime('%Y-%m-%d %H:%M')}</td>
                </tr>
        """
    
    html += """
            </tbody>
        </table>
    </div>
    """
    
    return mark_safe(html)


def validate_order_data_integrity(order: Order) -> Dict[str, Any]:
    """
    Validate that an order from a deleted user has proper data integrity.
    
    Args:
        order: Order instance to validate
        
    Returns:
        dict: Validation results
    """
    issues = []
    warnings = []
    
    if not is_deleted_user_order(order):
        return {"valid": True, "issues": [], "warnings": ["Not a deleted user order"]}
    
    # Check required fields
    if not order.total or order.total <= 0:
        issues.append("Invalid or missing total amount")
    
    if not order.subtotal or order.subtotal <= 0:
        issues.append("Invalid or missing subtotal amount")
    
    if not order.ref:
        issues.append("Missing order reference")
    
    if not order.date_created:
        issues.append("Missing creation date")
    
    # Check order items
    items = order.items.all()
    if not items.exists():
        issues.append("Order has no items")
    else:
        for item in items:
            if not item.total or item.total <= 0:
                issues.append(f"Item '{item.name}' has invalid total")
            if not item.quantity or item.quantity <= 0:
                issues.append(f"Item '{item.name}' has invalid quantity")
    
    # Check anonymization
    original_id = get_original_user_id(order)
    if not original_id:
        warnings.append("Could not extract original user ID from email")
    
    if order.user is not None:
        issues.append("Order still has user reference (should be None)")
    
    return {
        "valid": len(issues) == 0,
        "issues": issues,
        "warnings": warnings,
        "original_user_id": original_id
    }


# Admin integration helpers
def admin_customer_column(order):
    """
    Admin column helper for displaying customer information.
    Use this in admin list_display.
    """
    return format_customer_display(order)

admin_customer_column.short_description = "Customer"
admin_customer_column.allow_tags = True


def admin_deleted_user_filter(queryset, value):
    """
    Admin filter helper for filtering deleted user orders.
    """
    if value == "deleted":
        return queryset.filter(
            user__isnull=True,
            email__contains="deleted-user-",
            email__endswith="@anonymized.local"
        )
    elif value == "active":
        return queryset.filter(user__isnull=False)
    elif value == "guest":
        return queryset.filter(
            user__isnull=True
        ).exclude(
            email__contains="deleted-user-",
            email__endswith="@anonymized.local"
        )
    return queryset
