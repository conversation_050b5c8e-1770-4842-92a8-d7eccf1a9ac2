from typing import Optional

from django.core.exceptions import ValidationError
from rest_framework import serializers

from .serializers.address import AddressSerializer


def parse_address(text: str) -> dict:
    """
    Converts address text to dict, raising ValidationError if error.
    """
    try:
        serializer = AddressSerializer.from_text(text)
        serializer.is_valid(raise_exception=True)
        return serializer.data
    except serializers.ValidationError as exc:
        detail = exc.detail
        if isinstance(detail, dict):
            raise ValidationError(detail)
        if len(detail):
            raise ValidationError(detail[0])
        raise ValidationError(detail)


def parse_address_or_none(text: str) -> Optional[dict]:
    """
    Convert address text to dict, return None if address not valid.
    """
    try:
        return parse_address(text)
    except ValidationError:
        return None
