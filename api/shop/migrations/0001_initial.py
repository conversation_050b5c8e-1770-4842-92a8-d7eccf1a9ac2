# Generated by Django 3.2.8 on 2021-10-12 11:03

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('salesmanorders', '0002_alter_order_ref'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('kind', models.CharField(choices=[('ORDER_CREATED', 'Order Created'), ('STATUS_CHANGED', 'Status Changed'), ('PAYMENT_ADDED', 'Payment Added'), ('PAYMENT_UPDATED', 'Payment Updated'), ('PAYMENT_REMOVED', 'Payment Removed')], max_length=32)),
                ('info', models.TextField()),
                ('data', models.<PERSON><PERSON><PERSON><PERSON>(default=dict, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('order', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='salesmanorders.order')),
            ],
        ),
    ]
