# Generated by Django 3.2.9 on 2021-11-09 16:04

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields


class Migration(migrations.Migration):

    dependencies = [
        ('salesmanorders', '0002_alter_order_ref'),
        ('shop', '0004_auto_20211109_1538'),
    ]

    operations = [
        migrations.AlterField(
            model_name='shippingmethod',
            name='name',
            field=models.CharField(max_length=255, unique=True, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='shippingmethod',
            name='tax_percentage',
            field=models.PositiveIntegerField(blank=True, help_text='Optionally override tax percentage when this shipping method is selected. Defaults to 21%.', null=True, validators=[django.core.validators.MaxValueValidator(100)], verbose_name='Tax (%)'),
        ),
        migrations.CreateModel(
            name='OrderShipping',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('name', models.CharField(blank=True, max_length=255, verbose_name='Name')),
                ('caption', models.CharField(blank=True, max_length=255, verbose_name='Caption')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Price (€)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('order', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='shippings', to='salesmanorders.order')),
            ],
        ),
    ]
