# Generated by Django 3.2.13 on 2023-06-05 10:14

import country.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0016_orderinternalreference'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShippingRegion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='Name')),
                ('countries', country.models.CountriesField(base_field=models.CharField(max_length=10), blank=True, default=list, help_text='Hold down "Control" or "Command" on a Mac, to select more than one.', size=None)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
