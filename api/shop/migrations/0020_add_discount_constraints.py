# Generated by Django 3.2.25 on 2025-07-16 13:54

from django.db import migrations, models
import django.db.models.expressions


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0019_add_automatic_shipping_discount'),
    ]

    operations = [
        migrations.AddConstraint(
            model_name='discount',
            constraint=models.CheckConstraint(check=models.Q(('times_used__gte', 0)), name='discount_times_used_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='discount',
            constraint=models.CheckConstraint(check=models.Q(models.Q(('max_uses__isnull', True), ('times_used__lte', django.db.models.expressions.F('max_uses')), _connector='OR')), name='discount_usage_within_limit'),
        ),
    ]
