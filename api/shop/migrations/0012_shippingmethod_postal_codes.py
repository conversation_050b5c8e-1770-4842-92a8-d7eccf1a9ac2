# Generated by Django 3.2.9 on 2022-01-14 08:36

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0011_orderredsysreference'),
    ]

    operations = [
        migrations.AddField(
            model_name='shippingmethod',
            name='postal_codes',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=32), blank=True, default=list, help_text='Limit shiiping method to postal codes. Codes should be comma separated.', size=None),
        ),
    ]
