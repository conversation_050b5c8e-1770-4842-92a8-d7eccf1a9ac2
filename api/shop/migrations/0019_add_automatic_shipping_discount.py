# Generated by Django 3.2.25 on 2025-07-16 11:49

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0018_discount_discountusage'),
    ]

    operations = [
        migrations.CreateModel(
            name='AutomaticShippingDiscount',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Internal name for this automatic discount', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Optional description of this discount')),
                ('min_basket_value', models.DecimalField(decimal_places=2, help_text='Minimum basket value required to trigger this discount', max_digits=10)),
                ('max_basket_value', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum basket value for this discount (leave empty for no limit)', max_digits=10, null=True)),
                ('discount_percentage', models.DecimalField(decimal_places=2, help_text='Percentage of shipping cost to discount (1-100)', max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.01')), django.core.validators.MaxValueValidator(Decimal('100.00'))])),
                ('countries', models.JSONField(blank=True, default=list, help_text='Limit to specific countries (leave empty for all countries)')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this automatic discount is currently active')),
                ('valid_from', models.DateTimeField(blank=True, help_text='Start date for this discount (leave empty for immediate activation)', null=True)),
                ('valid_until', models.DateTimeField(blank=True, help_text='End date for this discount (leave empty for no expiration)', null=True)),
                ('priority', models.PositiveIntegerField(default=0, help_text='Priority when multiple discounts match (higher number = higher priority)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('shipping_methods', models.ManyToManyField(blank=True, help_text='Limit to specific shipping methods (leave empty for all methods)', to='shop.ShippingMethod')),
            ],
            options={
                'verbose_name': 'Automatic Shipping Discount',
                'verbose_name_plural': 'Automatic Shipping Discounts',
                'ordering': ['-priority', '-created_at'],
            },
        ),
    ]
