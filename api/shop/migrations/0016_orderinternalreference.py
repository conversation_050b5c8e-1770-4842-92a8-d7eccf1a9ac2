# Generated by Django 3.2.11 on 2022-03-04 09:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('salesmanorders', '0002_alter_order_ref'),
        ('shop', '0015_populate_sendcloud_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderInternalReference',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ref', models.CharField(max_length=16)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='internal_ref', to='salesmanorders.order')),
            ],
            options={
                'verbose_name': 'Internal Ref',
            },
        ),
    ]
