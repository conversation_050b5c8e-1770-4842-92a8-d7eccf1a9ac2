# Generated by Django 3.2.11 on 2022-02-23 12:25

from django.db import migrations


def populate_sendcloud_status(apps, schema_editor):
    Order = apps.get_model("salesmanorders", "Order")

    update_order_objs = []

    for order in (
        Order.objects.all().order_by("-date_created").prefetch_related("shippings")
    ):
        shipping = order.shippings.last()
        if shipping:
            data = shipping.sendcloud_response_data
            if data is not None:
                if "error" in data:
                    order._extra["sendcloud_status"] = "FAILED"
                else:
                    order._extra["sendcloud_status"] = "SUCCESS"
                update_order_objs.append(order)

    if update_order_objs:
        Order.objects.bulk_update(update_order_objs, ["_extra"])


class Migration(migrations.Migration):

    dependencies = [
        ("shop", "0014_alter_shippingmethod_countries"),
    ]

    operations = [
        migrations.RunPython(
            populate_sendcloud_status,
            reverse_code=lambda apps, shema_editor: None,
        ),
    ]
