# Generated by Django 3.2.9 on 2021-11-09 14:38

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0003_shippingmethod_shippingmethodprice'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='shippingmethod',
            options={'ordering': ('base_price',)},
        ),
        migrations.AddField(
            model_name='shippingmethod',
            name='caption',
            field=models.CharField(blank=True, help_text='Optional caption displayed after the name.', max_length=255, verbose_name='Caption'),
        ),
        migrations.AddField(
            model_name='shippingmethod',
            name='tax_percentage',
            field=models.PositiveIntegerField(help_text='Optionally override tax percentage when this shipping method is selected. Defaults to 21%.', null=True, validators=[django.core.validators.MaxValueValidator(100)], verbose_name='Tax (%)'),
        ),
    ]
