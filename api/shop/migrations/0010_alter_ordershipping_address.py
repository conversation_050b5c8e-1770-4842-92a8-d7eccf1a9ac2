# Generated by Django 3.2.9 on 2021-11-15 12:10

from django.db import migrations, models
import shop.validators


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0009_alter_ordershipping_address'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ordershipping',
            name='address',
            field=models.TextField(blank=True, help_text='Creating a shipping instance triggers Sendcloud Parcel to be created on order save.', validators=[shop.validators.validate_address], verbose_name='Address'),
        ),
    ]
