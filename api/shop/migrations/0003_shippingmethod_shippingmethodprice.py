# Generated by Django 3.2.9 on 2021-11-09 12:23

import country.models
from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0002_ordercreate_orderitemcreate'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShippingMethod',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('base_price', models.DecimalField(decimal_places=2, default=0, help_text='Base shipping price in EUR.', max_digits=10, verbose_name='Base price (€)')),
                ('countries', country.models.CountriesField(base_field=models.CharField(max_length=10), default=list, help_text='Hold down "Control" or "Command" on a Mac, to select more than one.', size=None)),
                ('prices_data', models.JSO<PERSON>ield(editable=False, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ShippingMethodPrice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('max_weight', models.DecimalField(decimal_places=3, default=0, help_text='Weight in kilograms.', max_digits=10, verbose_name='Max weight (kg)')),
                ('price', models.DecimalField(decimal_places=2, default=0, help_text='Price in EUR.', max_digits=10, verbose_name='Price (€)')),
                ('shipping_method', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='prices', to='shop.shippingmethod')),
            ],
            options={
                'ordering': ('max_weight',),
            },
        ),
    ]
