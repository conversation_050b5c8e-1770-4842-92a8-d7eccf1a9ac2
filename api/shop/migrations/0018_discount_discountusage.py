# Generated by Django 3.2.25 on 2025-07-15 14:10

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('shop', '0017_shippingregion'),
    ]

    operations = [
        migrations.CreateModel(
            name='Discount',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text='Discount code that customers will enter', max_length=20, unique=True)),
                ('description', models.CharField(help_text='Internal description of the discount', max_length=255)),
                ('discount_type', models.CharField(choices=[('PERCENT_SUBTOTAL', 'Percentage off subtotal'), ('PERCENT_SHIPPING', 'Percentage off shipping'), ('PERCENT_TOTAL', 'Percentage off total'), ('FIXED_AMOUNT', 'Fixed amount off subtotal')], help_text='Type of discount to apply', max_length=20)),
                ('value', models.DecimalField(decimal_places=2, help_text='Discount value (percentage or fixed amount)', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('expires_at', models.DateTimeField(blank=True, help_text='Leave empty for no expiration', null=True)),
                ('max_uses', models.PositiveIntegerField(blank=True, help_text='Maximum number of times this code can be used overall (null for unlimited)', null=True)),
                ('max_uses_per_user', models.PositiveIntegerField(blank=True, help_text='Maximum number of times a single user can use this code (null for unlimited)', null=True)),
                ('times_used', models.PositiveIntegerField(default=0, help_text='Number of times this code has been used')),
                ('min_basket_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Minimum basket value required to use this discount', max_digits=10)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this discount is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DiscountUsage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.CharField(help_text='ID of the order where this discount was used', max_length=100)),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('amount', models.CharField(help_text='Amount that was discounted', max_length=100)),
                ('discount', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usages', to='shop.discount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discount_usages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-used_at'],
                'unique_together': {('discount', 'order_id')},
            },
        ),
    ]
