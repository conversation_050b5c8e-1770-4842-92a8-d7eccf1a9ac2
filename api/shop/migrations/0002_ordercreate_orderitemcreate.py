# Generated by Django 3.2.8 on 2021-11-04 10:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderCreate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='OrderItemCreate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='Name')),
                ('code', models.CharField(max_length=128, verbose_name='Code')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=18, verbose_name='Unit price')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='Quantity')),
            ],
            options={
                'managed': False,
            },
        ),
    ]
