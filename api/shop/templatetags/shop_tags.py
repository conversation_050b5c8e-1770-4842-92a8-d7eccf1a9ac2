from decimal import Decimal

from django.conf import settings
from django.template import Library
from salesman.orders.models import Order

from shop.formatters import price_format
from shop.parsers import parse_address_or_none


register = Library()


@register.simple_tag(takes_context=True)
def priceformat(context, value):
    return price_format(value, context)


@register.simple_tag
def parseaddress(value):
    return parse_address_or_none(value)


@register.simple_tag
def get_modifier_from_order(order: Order, modifier: str):
    from shop.views.order_extra import reconstruct_basket_from_order
    from salesman.basket.serializers import ExtraRowsField

    try:
        extra_rows = order.extra_rows
        # there is a bug in prod where sometimes extra rows is missing
        # this is a fix for those situations
        if not extra_rows:
            basket, errors = reconstruct_basket_from_order(order)
            if errors:
                return None
            extra_rows = ExtraRowsField().to_representation(basket.extra_rows)
        return [x for x in extra_rows if x["modifier"] == modifier][0]
    except (KeyError, IndexError):
        return None


@register.simple_tag
def get_order_info(order: Order):
    tax = get_modifier_from_order(order, "tax")
    tax_percent = Decimal(tax["extra"]["percent"]) if tax else Decimal(0)

    # Get both manual discount codes and automatic shipping discounts
    discount = get_modifier_from_order(order, "discount_code")
    auto_shipping_discount = get_modifier_from_order(order, "auto_shipping_discount")

    items = []
    for item in order.items.all():
        if tax_percent:
            item.tax_amount = item.subtotal / tax_percent
        else:
            item.tax_amount = Decimal(0)
        item.subtotal_no_tax = item.subtotal - item.tax_amount
        items.append(item)

    return {
        "tax": tax,
        "tax_percent": tax_percent,
        "shipping": get_modifier_from_order(order, "shipping"),
        "items": items,
        "discount": discount,
        "auto_shipping_discount": auto_shipping_discount,
    }


@register.inclusion_tag("shop/email/order.html")
def render_order_email(order: Order):
    return {
        "order": order,
    }


@register.simple_tag
def get_company_address():
    return settings.SHOP_COMPANY_ADDRESS


@register.simple_tag
def get_company_email():
    return settings.SHOP_COMPANY_EMAIL


@register.simple_tag
def get_site_name():
    return settings.SITE_NAME
