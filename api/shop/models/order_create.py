from __future__ import annotations

from decimal import ROUND_HALF_UP, Decimal

from django import forms
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.safestring import mark_safe
from modelcluster.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from modelcluster.models import ClusterableModel
from salesman.basket.models import Basket, BasketItem
from salesman.core.models import <PERSON><PERSON><PERSON><PERSON>
from salesman.orders.models import Order, OrderItem
from wagtail.admin.edit_handlers import FieldPanel, InlinePanel, MultiFieldPanel

from catalog.models import SingleProductVariant
from extra.edit_handlers import ReadOnlyPanel
from shop.parsers import parse_address_or_none
from shop.validators import validate_address
from user.edit_handlers import UserChooserPanel
from user.models import User


def format_json(value, obj, request):
    from salesman.admin.wagtail_hooks import _format_json

    return _format_json(value, obj, request)


class OrderCreate(ClusterableModel):
    """
    Dummy model without tables to manage order creation.
    """

    user = models.ForeignKey(
        User,
        models.DO_NOTHING,
        blank=True,
        null=True,
        verbose_name="Customer",
    )
    email = models.EmailField(
        "Email",
        blank=True,
        help_text="Either customer or email must be specified.",
    )
    shipping_address = models.TextField(
        "Shipping address", validators=[validate_address]
    )
    billing_address = models.TextField("Billing address", validators=[validate_address])
    _extra = JSONField("Extra", blank=True)

    panels = [
        MultiFieldPanel(
            [
                ReadOnlyPanel("admin_ref", heading="Reference"),
            ],
            heading="Info",
        ),
        MultiFieldPanel(
            [
                UserChooserPanel("user"),
                FieldPanel("email"),
                FieldPanel(
                    "shipping_address", widget=forms.Textarea(attrs={"rows": 4})
                ),
                FieldPanel("billing_address", widget=forms.Textarea(attrs={"rows": 4})),
            ],
            heading="Contact",
        ),
        InlinePanel("items", heading="Items", min_num=1),
        ReadOnlyPanel("_extra", formatter=format_json),
    ]

    class Meta:
        managed = False

    @property
    def admin_ref(self):
        return mark_safe("<i>A unique order reference will be generated on save.</i>")

    def save(self, *args, **kwargs):
        raise NotImplementedError

    def clean(self):
        super().clean()
        if not self.user and not self.email:
            raise ValidationError({"email": "Must specify either Customer or email."})

    def to_basket(self) -> Basket:
        from ..modifiers import ShippingModifier

        addr = parse_address_or_none(self.shipping_address)
        basket = Basket(owner=self.user, extra=self._extra)
        if addr:
            basket.extra[ShippingModifier.country_key] = addr["country"]
        basket.extra["email"] = self.email or self.user.email
        basket.extra["shipping_address"] = self.shipping_address
        basket.extra["billing_address"] = self.billing_address
        basket._cached_items = []

        for item in self.items.all():
            basket_item = item.to_basket_item(basket)
            basket._cached_items.append(basket_item)

        return basket

    def to_order(self) -> Order:
        from django.http import HttpRequest

        request = HttpRequest()
        request.user = self.user
        # Set payment to emulate creating order on checkout
        request._payment = True

        basket = self.to_basket()
        basket.update(request)
        order = Order.objects.create_from_basket(
            basket,
            request,
            status=Order.get_statuses().CREATED,
        )
        return order

    @classmethod
    def from_order(cls, order: Order) -> OrderCreate:
        obj = cls(
            user=order.user,
            email=order.email,
            shipping_address=order.shipping_address,
            billing_address=order.billing_address,
            _extra={**order._extra, "created_from_order": order.ref},
        )
        if "tracking_url" in obj._extra:
            del obj._extra["tracking_url"]
        if "tracking_number" in obj._extra:
            del obj._extra["tracking_number"]
        for item in order.items.all():
            create_item = OrderItemCreate.from_order_item(item)
            obj.items.add(create_item)
        return obj


class OrderItemCreate(models.Model):
    order = ParentalKey(OrderCreate, related_name="items")
    name = models.CharField("Name", max_length=128)
    code = models.CharField("Code", max_length=128)
    unit_price = models.DecimalField("Unit price (€)", max_digits=18, decimal_places=2)
    unit_weight = models.DecimalField(
        "Unit weight (kg)", max_digits=10, decimal_places=3
    )
    quantity = models.PositiveIntegerField("Quantity", default=1)
    total = models.DecimalField(
        "Total",
        max_digits=18,
        decimal_places=2,
        blank=True,
        help_text="Leave empty to auto-calculate.",
    )
    _extra = JSONField("Extra", blank=True)

    panels = [
        FieldPanel("name"),
        FieldPanel("code"),
        FieldPanel("unit_price"),
        FieldPanel("unit_weight"),
        FieldPanel("quantity"),
        FieldPanel("total"),
    ]

    class Meta:
        managed = False

    def save(self, *args, **kwargs):
        raise NotImplementedError

    def to_basket_item(self, basket: Basket) -> BasketItem:
        lang = settings.WAGTAIL_CONTENT_LANGUAGES[0][0]

        # Build new product from item, this product adds minimal data to pass
        # basket product serializer requirements. Make sure to check it again
        # when changing the basket serializer for SingleProductVariant.
        product = SingleProductVariant(
            code=self.code,
            price=self.unit_price,
            weight=self.unit_weight,
            product_data={
                lang: {
                    "live": True,
                    "base_price": repr(self.unit_price),
                }
            },
            combo_data={lang: {}},
            media_data=[],
        )
        product._name = self.name

        return BasketItem(
            basket=basket,
            ref=BasketItem.get_product_ref(product),
            product=product,
            quantity=self.quantity,
        )

    @classmethod
    def from_order_item(cls, item: OrderItem) -> OrderItemCreate:
        from ..modifiers import WeightModifier

        weight = Decimal(item._extra.get(WeightModifier.identifier, "0.000"))
        unit_weight = Decimal(weight / item.quantity)
        unit_weight = unit_weight.quantize(Decimal("1.000"), ROUND_HALF_UP)

        return cls(
            name=item.name,
            code=item.code,
            unit_price=item.unit_price,
            unit_weight=unit_weight,
            total=item.total,
            quantity=item.quantity,
            _extra=item._extra,
        )
