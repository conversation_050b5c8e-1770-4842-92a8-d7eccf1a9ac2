from __future__ import annotations

import logging
from decimal import Decimal
from typing import List, Optional

from django.conf import settings
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from salesman.orders.signals import status_changed
from wagtail.admin.edit_handlers import FieldPanel, MultiFieldPanel

logger = logging.getLogger(__name__)


class Discount(models.Model):
    DISCOUNT_TYPES = [
        ("PERCENT_SUBTOTAL", "Percentage off subtotal"),
        ("PERCENT_SHIPPING", "Percentage off shipping"),
        ("PERCENT_TOTAL", "Percentage off total"),  # New type
        ("FIXED_AMOUNT", "Fixed amount off subtotal"),
    ]

    code = models.CharField(
        max_length=20, unique=True, help_text="Discount code that customers will enter"
    )
    description = models.CharField(
        max_length=255, help_text="Internal description of the discount"
    )
    discount_type = models.CharField(
        max_length=20, choices=DISCOUNT_TYPES, help_text="Type of discount to apply"
    )
    value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text="Discount value (percentage or fixed amount)",
    )
    expires_at = models.DateTimeField(
        null=True, blank=True, help_text="Leave empty for no expiration"
    )
    max_uses = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of times this code can be used overall (null for unlimited)",
    )
    max_uses_per_user = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of times a single user can use this code (null for unlimited)",
    )
    times_used = models.PositiveIntegerField(
        default=0, help_text="Number of times this code has been used"
    )
    min_basket_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Minimum basket value required to use this discount",
    )
    is_active = models.BooleanField(
        default=True, help_text="Whether this discount is currently active"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("code"),
        FieldPanel("description"),
        FieldPanel("discount_type"),
        FieldPanel("value"),
        FieldPanel("expires_at"),
        FieldPanel("max_uses"),
        FieldPanel("max_uses_per_user"),
        FieldPanel("min_basket_value"),
        FieldPanel("is_active"),
    ]

    def __str__(self):
        return f"{self.code} - {self.get_discount_type_display()}"

    def is_valid(self, basket_value=None, user=None):
        """Check if discount is valid for use"""
        if not self.is_active:
            return False, "This discount code is not active"

        if self.expires_at and timezone.now() > self.expires_at:
            return False, "This discount code has expired"

        if self.max_uses and self.times_used >= self.max_uses:
            return False, "This discount code has reached maximum usage"

        if basket_value and basket_value < self.min_basket_value:
            return (
                False,
                f"Basket total must be at least {self.min_basket_value} to use this code",
            )

        if user and user.is_authenticated and self.max_uses_per_user:
            # Handle TokenUser objects by converting to actual User
            actual_user = user
            if hasattr(user, "to_user") and callable(user.to_user):
                actual_user = user.to_user()
                if not actual_user:
                    # If we can't get the actual user, skip per-user validation
                    # This prevents errors while still allowing the discount to work
                    return True, "Valid discount code"

            user_uses = DiscountUsage.objects.filter(
                discount=self, user=actual_user
            ).count()
            if user_uses >= self.max_uses_per_user:
                return (
                    False,
                    f"You have already used this code the maximum number of times ({self.max_uses_per_user})",
                )

        return True, "Valid discount code"

    class Meta:
        ordering = ["-created_at"]
        constraints = [
            models.CheckConstraint(
                check=models.Q(times_used__gte=0),
                name="discount_times_used_non_negative",
            ),
            models.CheckConstraint(
                check=models.Q(
                    models.Q(max_uses__isnull=True)
                    | models.Q(times_used__lte=models.F("max_uses"))
                ),
                name="discount_usage_within_limit",
            ),
        ]


class DiscountUsage(models.Model):
    """Tracks individual usage of discount codes"""

    discount = models.ForeignKey(
        Discount, on_delete=models.CASCADE, related_name="usages"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="discount_usages",
    )
    order_id = models.CharField(
        max_length=100, help_text="ID of the order where this discount was used"
    )
    used_at = models.DateTimeField(auto_now_add=True)
    amount = models.CharField(max_length=100, help_text="Amount that was discounted")

    class Meta:
        unique_together = ["discount", "order_id"]
        ordering = ["-used_at"]

    def __str__(self):
        return f"{self.discount.code} used by {self.user} on {self.used_at}"


def extract_discount_code(items: List) -> Optional[str]:
    if not items:
        return None

    discount_code = None
    for item in items:
        extra = item.get("extra")
        if not extra:
            continue
        discount_code = extra.get("discount_code")

    return discount_code


@receiver(status_changed, dispatch_uid="update_discount_stats")
def track_discount_usage(sender, order, new_status, **kwargs):
    """
    Track discount usage when an order is placed.
    Uses the improved DiscountValidator to prevent race conditions.
    """
    from shop.discount.validators import DiscountValidator

    if new_status != "PROCESSING":
        return

    discount_code = extract_discount_code(order.extra_rows)

    if not discount_code:
        return

    try:
        # Use the improved confirmation method that handles race conditions
        basket_id = getattr(order, "_basket_id", str(order.id))
        success = DiscountValidator.confirm_discount_usage(
            discount_code, basket_id, user=order.user
        )

        if success:
            logger.info(f"Successfully confirmed discount usage for order {order.id}")
        else:
            logger.error(f"Failed to confirm discount usage for order {order.id}")

    except Exception as e:
        logger.error(f"Error tracking discount usage for order {order.id}: {e}")

        # Fallback to old method if new method fails
        try:
            discount = Discount.objects.get(code=discount_code)
            if not discount:
                return

            # Increment overall usage counter (with potential race condition)
            discount.times_used += 1
            discount.save()

            user = order.user
            if user:
                # Get the discount amount from the order
                discount_amount = next(
                    (
                        row["amount"]
                        for row in order.extra_rows
                        if row.get("modifier") == "discount"
                    ),
                    Decimal("0.00"),
                )

                # Handle TokenUser objects by converting to actual User
                actual_user = user
                if hasattr(user, "to_user") and callable(user.to_user):
                    actual_user = user.to_user()

                # Only create usage record if we have a valid User instance
                if actual_user and hasattr(actual_user, "pk"):
                    DiscountUsage.objects.create(
                        discount=discount,
                        user=actual_user,
                        order_id=order.id,
                        amount=str(discount_amount),
                    )
            logger.info("Added stats for discount code (fallback method)")
        except Discount.DoesNotExist:
            pass


class AutomaticShippingDiscount(models.Model):
    """
    Automatic shipping discount based on basket size.
    No discount code needed - automatically applied when conditions are met.
    """

    name = models.CharField(
        max_length=255, help_text="Internal name for this automatic discount"
    )
    description = models.TextField(
        blank=True, help_text="Optional description of this discount"
    )

    # Basket size conditions
    min_basket_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Minimum basket value required to trigger this discount",
    )
    max_basket_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum basket value for this discount (leave empty for no limit)",
    )

    # Discount configuration
    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("0.01")),
            MaxValueValidator(Decimal("100.00")),
        ],
        help_text="Percentage of shipping cost to discount (1-100)",
    )

    # Shipping method restrictions
    shipping_methods = models.ManyToManyField(
        "shop.ShippingMethod",
        blank=True,
        help_text="Limit to specific shipping methods (leave empty for all methods)",
    )

    # Geographic restrictions
    countries = models.JSONField(
        default=list,
        blank=True,
        help_text="Limit to specific countries (leave empty for all countries)",
    )

    # Activation settings
    is_active = models.BooleanField(
        default=True, help_text="Whether this automatic discount is currently active"
    )

    # Date restrictions
    valid_from = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Start date for this discount (leave empty for immediate activation)",
    )
    valid_until = models.DateTimeField(
        null=True,
        blank=True,
        help_text="End date for this discount (leave empty for no expiration)",
    )

    # Priority for multiple matching discounts
    priority = models.PositiveIntegerField(
        default=0,
        help_text="Priority when multiple discounts match (higher number = higher priority)",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("name"),
                FieldPanel("description"),
                FieldPanel("is_active"),
            ],
            heading="Basic Information",
        ),
        MultiFieldPanel(
            [
                FieldPanel("min_basket_value"),
                FieldPanel("max_basket_value"),
            ],
            heading="Basket Value Conditions",
        ),
        MultiFieldPanel(
            [
                FieldPanel("discount_percentage"),
                FieldPanel("priority"),
            ],
            heading="Discount Settings",
        ),
        MultiFieldPanel(
            [
                FieldPanel("shipping_methods"),
                FieldPanel("countries"),
            ],
            heading="Restrictions",
        ),
        MultiFieldPanel(
            [
                FieldPanel("valid_from"),
                FieldPanel("valid_until"),
            ],
            heading="Date Range",
        ),
    ]

    class Meta:
        ordering = ["-priority", "-created_at"]
        verbose_name = "Automatic Shipping Discount"
        verbose_name_plural = "Automatic Shipping Discounts"

    def __str__(self):
        return f"{self.name} ({self.discount_percentage}% off shipping)"

    def is_valid_for_basket(
        self, basket_value: Decimal, country: str = None, shipping_method=None
    ) -> bool:
        """Check if this discount is valid for the given basket conditions"""

        # Check if active
        if not self.is_active:
            return False

        # Check date range
        now = timezone.now()
        if self.valid_from and now < self.valid_from:
            return False
        if self.valid_until and now > self.valid_until:
            return False

        # Check basket value range
        if basket_value < self.min_basket_value:
            return False
        if self.max_basket_value and basket_value > self.max_basket_value:
            return False

        # Check country restriction
        if self.countries and country and country not in self.countries:
            return False

        # Check shipping method restriction
        if self.shipping_methods.exists() and shipping_method:
            if not self.shipping_methods.filter(id=shipping_method.id).exists():
                return False

        return True

    @classmethod
    def get_best_discount(
        cls, basket_value: Decimal, country: str = None, shipping_method=None
    ):
        """Get the best automatic shipping discount for the given conditions"""

        # Get all valid discounts ordered by priority
        valid_discounts = []
        for discount in cls.objects.filter(is_active=True):
            if discount.is_valid_for_basket(basket_value, country, shipping_method):
                valid_discounts.append(discount)

        if not valid_discounts:
            return None

        # Return the highest priority discount (highest percentage if same priority)
        return max(valid_discounts, key=lambda d: (d.priority, d.discount_percentage))
