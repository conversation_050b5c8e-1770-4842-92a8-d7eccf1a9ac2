from __future__ import annotations

from datetime import datetime
from decimal import Dec<PERSON><PERSON>
from typing import List, Optional

from django import forms
from django.conf import settings
from django.contrib.postgres.fields.array import ArrayField
from django.core.cache import cache
from django.core.validators import MaxValueValidator
from django.db import models
from django.db.models import F, Q
from django.urls import reverse
from modelcluster.fields import <PERSON>rental<PERSON>ey
from modelcluster.models import ClusterableModel
from salesman.conf import app_settings as salesman_settings
from salesman.orders.models import Order
from wagtail.admin.edit_handlers import FieldPanel, InlinePanel, MultiFieldPanel

from catalog.models.product import ProductVariant
from country.data import COUNTRIES
from country.models import CountriesField
from extra.edit_handlers import ReadOnlyDatePanel, ReadOnlyPanel
from notification.models import NotificationTrigger
from shop.parsers import parse_address_or_none
from shop.shipping.sendcloud import Client as SendcloudClient
from shop.shipping.sendcloud import <PERSON><PERSON><PERSON>, ParcelItem
from shop.validators import validate_address


def get_barcode_from_cache(code: str) -> Optional[str]:
    """
    Get barcode from cache

    This function retrieves the barcode for a given code from the cache.
    """
    cache_key = f"barcode-{code}"
    return cache.get(cache_key)


def set_barcode_to_cache(code: str, barcode: str) -> None:
    """
    Set barcode to cache

    This function stores the barcode for a given code in the cache.
    """
    cache_key = f"barcode-{code}"
    cache.set(cache_key, barcode, timeout=None)


def invalidate_barcode_cache(code: str) -> None:
    """
    Invalidate barcode cache for a specific product code

    This function removes the cached barcode for a given code.
    """
    cache_key = f"barcode-{code}"
    cache.delete(cache_key)


def get_barcode(code: str) -> Optional[str]:
    """
    Get barcode

    This function retrieves the barcode for a given code.
    First checks the cache, then falls back to database if not found.
    """
    # Try to get from cache first
    barcode = get_barcode_from_cache(code)
    if barcode:
        return barcode

    try:
        variant = ProductVariant.objects.get(code=code)
        barcode = variant.ean13

        # Cache the result for future use
        if barcode:
            set_barcode_to_cache(code, barcode)

        return barcode
    except ProductVariant.DoesNotExist:
        # Handle case when no variant exists with this code
        return None


def format_json(value, obj, request):
    from salesman.admin.wagtail_hooks import _format_json

    return _format_json(value, obj, request)


class ShippingMethod(ClusterableModel):
    name = models.CharField("Name", max_length=255, unique=True)
    caption = models.CharField(
        "Caption",
        max_length=255,
        blank=True,
        help_text="Optional caption displayed after the name.",
    )
    is_default = models.BooleanField(
        "Default",
        default=False,
        help_text="Use as default shipping.",
    )

    countries = CountriesField(shipping=False, blank=True)
    postal_codes = ArrayField(
        models.CharField(max_length=32),
        blank=True,
        default=list,
        help_text=(
            "Limit shiiping method to postal codes. Codes should be comma separated."
        ),
    )
    base_price = models.DecimalField(
        "Base price (€)",
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Base shipping price in EUR.",
    )
    prices_data = models.JSONField(null=True, editable=False)

    tax_percentage = models.PositiveIntegerField(
        "Tax (%)",
        blank=True,
        null=True,
        validators=[MaxValueValidator(100)],
        help_text=(
            "Optionally override tax percentage when this shipping method is selected. "
            "Defaults to 21%."
        ),
    )

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("name"),
                FieldPanel("caption"),
                FieldPanel("is_default"),
            ],
            heading="Info",
        ),
        FieldPanel("countries"),
        FieldPanel("postal_codes", widget=forms.Textarea()),
        FieldPanel("base_price"),
        FieldPanel("tax_percentage"),
        InlinePanel("prices", heading="Prices for weights"),
    ]

    debug_panels = [
        ReadOnlyPanel("prices_data"),
    ]

    if settings.DEBUG:
        panels += debug_panels

    class Meta:
        ordering = ("base_price",)

    def __str__(self) -> str:
        return self.name

    def save(self, **kwargs):
        # Invalidate prices data if not explicitly saved.
        if "prices_data" not in kwargs.get("update_fields", []):
            self.prices_data = None
        super().save(**kwargs)

    def get_prices_data(self):
        if self.prices_data is None:
            data = []
            for price in self.prices.all():
                data.append(price.to_dict())
            self.prices_data = data
            self.save(update_fields=["prices_data"])
        return self.prices_data

    def get_best_price_for_weight(self, weight: Decimal) -> ShippingMethodPrice:
        from decimal import Decimal  # noqa

        obj = ShippingMethodPrice(price=self.base_price)
        data = self.get_prices_data()
        for item_data in data:
            item = ShippingMethodPrice.from_dict(item_data)
            if weight <= item.max_weight and item.price < obj.price:
                obj = item
        return obj

    @classmethod
    def find_for_country(
        cls,
        country: str,
        postal_code: Optional[str] = None,
    ) -> Optional[ShippingMethod]:
        shipping = None
        objs = list(cls.objects.filter(countries__contains=[country]))
        if len(objs):
            if postal_code:
                for obj in objs:
                    if postal_code in obj.postal_codes:
                        shipping = obj
                        break

                if not shipping:
                    for obj in objs:
                        if len(obj.postal_codes) == 0:
                            shipping = obj
                            break
            else:
                shipping = objs[0]

        # Try to get default shipping
        if not shipping:
            shipping = cls.objects.filter(is_default=True).first()
        return shipping


class ShippingMethodPrice(models.Model):
    shipping_method = ParentalKey(ShippingMethod, related_name="prices")

    max_weight = models.DecimalField(
        "Max weight (kg)",
        max_digits=10,
        decimal_places=3,
        default=0,
        help_text="Weight in kilograms.",
    )
    price = models.DecimalField(
        "Price (€)",
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Price in EUR.",
    )

    panels = [
        FieldPanel("max_weight"),
        FieldPanel("price"),
    ]

    class Meta:
        ordering = ("max_weight",)

    def __str__(self):
        price = salesman_settings.SALESMAN_PRICE_FORMATTER(self.price, {})
        return f"{price} up to {self.max_weight}"

    def to_dict(self):
        default = Decimal(0)
        return {
            "max_weight": repr(self.max_weight or default),
            "price": repr(self.price or default),
        }

    @classmethod
    def from_dict(cls, data: dict) -> ShippingMethodPrice:
        from decimal import Decimal  # noqa

        return cls(
            max_weight=eval(data["max_weight"]),
            price=eval(data["price"]),
        )


class OrderSendcloudStatus(models.TextChoices):
    PENDING = "PENDING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class OrderShipping(models.Model):
    order = ParentalKey(Order, related_name="shippings")
    address = models.TextField(
        "Address",
        blank=True,
        validators=[validate_address],
        help_text=(
            "Creating a shipping instance triggers Sendcloud Parcel "
            "to be created on order save."
        ),
    )
    created_at = models.DateTimeField("Created at", auto_now_add=True)

    sendcloud_request_sent = models.BooleanField(default=False, editable=False)
    sendcloud_response_data = models.JSONField(null=True, editable=False)

    panels = [
        FieldPanel("address"),
        ReadOnlyDatePanel("created_at"),
        ReadOnlyPanel(
            "sendcloud_response_data",
            formatter=format_json,
            heading="Sendcloud response",
        ),
    ]

    @staticmethod
    def build_external_reference(id):
        return f"{settings.SHOP_ORDER_REFERENCE_TWO_LETTER_PREFIX}{id}"

    @staticmethod
    def parse_external_reference(ref):
        start = len(settings.SHOP_ORDER_REFERENCE_TWO_LETTER_PREFIX)
        return ref[start:]

    def to_sendcloud_parcel(self) -> Optional[Parcel]:
        address = self.address or self.order.shipping_address
        addr = parse_address_or_none(address)

        if not addr:
            return None

        # For Mexican addresses, use RFC as company name if provided
        company_name = None
        if addr["country"] == "MX" and addr.get("rfc"):
            company_name = addr["rfc"]

        parcel = Parcel(
            name="{first_name} {last_name}".format(**addr),
            address=addr["address"],
            address_2=addr.get("address_2", ""),
            city=addr["city"],
            postal_code=addr["postal_code"],
            country=addr["country"],
            country_state=addr["state"],
            company_name=company_name,
            customs_invoice_nr=self.order.ref,
            customs_shipment_type=2,  # comercial goods
            email=self.order.email,
            telephone=addr.get("phone", None),
            external_reference=self.build_external_reference(self.id),
            order_number=self.order.ref,
            weight=self.order.extra.get("weight", "0.000"),
            total_order_value=float(self.order.total),
            total_order_value_currency=settings.BASE_CURRENCY,
            parcel_items=[
                ParcelItem(
                    description=item.name,
                    quantity=item.quantity,
                    weight=item.product_data.get("weight", 0),
                    value=float(item.unit_price),
                    hs_code=item.product_data.get("hs_code", ""),
                    origin_country=item.product_data.get("origin_country", ""),
                    sku=item.code,
                    product_id=item.product_id,
                    properties={},
                )
                for item in self.order.items.all()
            ],
        )
        return parcel

    def create_sendcloud_parcel(self) -> dict:
        parcel = self.to_sendcloud_parcel()
        if parcel:
            self.sendcloud_response_data = SendcloudClient().create_parcel(parcel)
        self.sendcloud_request_sent = True
        return self.sendcloud_response_data

    @classmethod
    def create_for_order(cls, order: Order) -> Optional[OrderShipping]:
        """
        Create a shipping instance for order.
        """
        return cls.objects.create(order=order, address=order.shipping_address)

    @classmethod
    def dispatch_failed_parcel_notification(cls, orders: list[Order]):
        """
        Trigger notificatons for failed sendcloud response.
        """
        order_urls = [
            reverse("salesman_order_modeladmin_edit", args=[x.id]) for x in orders
        ]
        context = {"admin_order_urls": order_urls}
        NotificationTrigger.dispatch_triggers("SENDCLOUD_FAILED", context)

    @classmethod
    def create_queued_parcels(cls) -> int:
        """
        Bulk create queued parcels.
        """
        objs = list(
            cls.objects.filter(sendcloud_request_sent=False).select_related("order")
        )

        objs = objs[:100]  # Limit to 100 parcels this is required by Sendcloud API
        objs_id_map = {str(x.id): x for x in objs}

        parcels = []
        for obj in objs:
            parcel = obj.to_sendcloud_parcel()
            if parcel:
                parcels.append(parcel)

        client = SendcloudClient()
        count = len(parcels)

        if count == 1:
            parcel = parcels[0]
            obj_id = cls.parse_external_reference(parcel.external_reference)
            obj = objs_id_map.get(obj_id)
            if obj:
                obj.sendcloud_response_data = client.create_parcel(parcel)
                obj.sendcloud_request_sent = True
                obj.save()
                if "error" in obj.sendcloud_response_data:
                    obj.order.extra["sendcloud_status"] = OrderSendcloudStatus.FAILED
                    cls.dispatch_failed_parcel_notification(orders=[obj.order])
                else:
                    obj.order.extra["sendcloud_status"] = OrderSendcloudStatus.SUCCESS
                obj.order.save(update_fields=["extra"])
            return 1

        if count > 1:
            data = client.create_parcels(parcels)
            # Update sendcloud response data on objects
            update_objs = []
            update_order_objs = []
            for parcel_data in data.get("parcels", []):
                ref = parcel_data.get("external_reference", "0")
                obj_id = cls.parse_external_reference(ref)
                obj = objs_id_map.get(obj_id)
                obj.sendcloud_response_data = parcel_data
                obj.sendcloud_request_sent = True
                obj.order._extra["sendcloud_status"] = OrderSendcloudStatus.SUCCESS
                update_order_objs.append(obj.order)
                update_objs.append(obj)
            for failed_data in data.get("failed_parcels", []):
                parcel_data = failed_data["parcel"]
                ref = parcel_data.get("external_reference", "0")
                obj_id = cls.parse_external_reference(ref)
                obj = objs_id_map.get(obj_id)
                obj.sendcloud_response_data = {"errors": failed_data["errors"]}
                obj.sendcloud_request_sent = True
                obj.order._extra["sendcloud_status"] = OrderSendcloudStatus.FAILED
                update_order_objs.append(obj.order)
                update_objs.append(obj)

            count = len(update_objs)
            if count == 1:
                update_objs[0].save()
                update_order_objs[0].save()
            elif count > 1:
                cls.objects.bulk_update(
                    update_objs, ["sendcloud_response_data", "sendcloud_request_sent"]
                )
                Order.objects.bulk_update(update_order_objs, ["_extra"])
                cls.dispatch_failed_parcel_notification(orders=update_order_objs)

        return count


class ShippingRegion(ClusterableModel):
    name = models.CharField("Name", max_length=255, unique=True)
    countries = CountriesField(shipping=False, blank=True)

    panels = [
        FieldPanel("name"),
        FieldPanel("countries"),
    ]

    def __str__(self) -> str:
        return self.name
