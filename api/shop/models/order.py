from __future__ import annotations

from django.conf import settings
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.html import format_html
from salesman.orders.models import Order


class OrderRedsysReference(models.Model):
    order = models.ForeignKey(
        Order,
        on_delete=models.SET_NULL,
        null=True,
        related_name="redsys_refs",
    )

    def __str__(self) -> str:
        return self.ref

    @property
    def ref(self):
        if settings.DEBUG:
            num = self.id
        else:
            num = 1000 + self.id

        # Use zero-padded ID with length of 10 as reference
        return f"{num:010}"

    @classmethod
    def create_for_order(cls, order: Order) -> OrderRedsysReference:
        return cls.objects.create(order=order)


class OrderInternalReference(models.Model):
    """
    Internally used reference with numbered incremented ID.
    """

    order = models.OneToOneField(
        Order,
        on_delete=models.CASCADE,
        related_name="internal_ref",
    )

    ref = models.Char<PERSON>ield(max_length=16)

    class Meta:
        verbose_name = "Internal Ref"

    def __str__(self):
        return self.ref

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)
        if is_new:
            self.ref = "{prefix}{id:08}".format(
                prefix=settings.SHOP_ORDER_REFERENCE_TWO_LETTER_PREFIX[:2],
                id=self.pk,
            )
            self.save(update_fields=["ref"])

    @classmethod
    def get_for_order(cls, order: Order) -> OrderInternalReference:
        return cls.objects.get_or_create(order=order)[0]


# Add admin display methods to Order model
def admin_customer_display(self):
    """Display customer information in admin, handling deleted users"""
    from shop.utils.deleted_user_orders import format_customer_display

    return format_customer_display(self)


admin_customer_display.short_description = "Customer"
admin_customer_display.allow_tags = True

# Monkey patch the method onto the Order model
Order.admin_customer_display = admin_customer_display


@receiver(post_save, sender=Order, dispatch_uid="shop_post_save_order")
def handle_post_save_order(sender, instance, created, **kwargs):
    if created:
        OrderInternalReference.objects.create(order=instance)
