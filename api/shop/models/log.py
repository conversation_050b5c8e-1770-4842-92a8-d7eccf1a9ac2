from __future__ import annotations

from typing import Optional

from django.db import models
from django.db.models.signals import post_delete, post_init, post_save
from django.dispatch import receiver
from modelcluster.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from salesman.conf import app_settings as salesman_settings
from salesman.orders.models import Order, OrderPayment
from salesman.orders.signals import status_changed


class OrderLog(models.Model):
    order = ParentalKey(
        Order,
        on_delete=models.CASCADE,
        related_name="logs",
    )

    class Kind(models.TextChoices):
        ORDER_CREATED = "ORDER_CREATED"
        STATUS_CHANGED = "STATUS_CHANGED"
        PAYMENT_ADDED = "PAYMENT_ADDED"
        PAYMENT_UPDATED = "PAYMENT_UPDATED"
        PAYMENT_REMOVED = "PAYMENT_REMOVED"

    kind = models.CharField(max_length=32, choices=Kind.choices)
    info = models.TextField()
    data = models.JSONField(default=dict, editable=False)
    created_at = models.DateTimeField("Created at", auto_now_add=True)

    def __str__(self):
        return self.info

    @classmethod
    def create(
        cls,
        order: Order,
        kind: str,
        info: str,
        data: Optional[dict] = None,
    ) -> OrderLog:
        if not data:
            data = {}
        return cls.objects.create(order=order, kind=kind, info=info, data=data)

    @classmethod
    def order_created(cls, order: Order) -> OrderLog:
        info = f'Order created with status "{cls.get_status_display(order.status)}"'
        return cls.create(order, cls.Kind.ORDER_CREATED, info)

    @classmethod
    def status_changed(cls, order: Order, old_status: str, new_status: str) -> OrderLog:
        info = f'Status changed to "{cls.get_status_display(new_status)}"'
        data = {"old_status": old_status, "new_status": new_status}
        return cls.create(order, cls.Kind.STATUS_CHANGED, info, data)

    @classmethod
    def payment_added(cls, payment: OrderPayment) -> OrderLog:
        info = 'Payment for "{}" with ID "{}" added using "{}"'.format(
            salesman_settings.SALESMAN_PRICE_FORMATTER(payment.amount, {}),
            payment.transaction_id,
            payment.payment_method_display,
        )
        data = cls.get_payment_data(payment)
        return cls.create(payment.order, cls.Kind.PAYMENT_ADDED, info, data)

    @classmethod
    def payment_updated(cls, payment: OrderPayment) -> OrderLog:
        info = 'Payment updated to "{}" with ID "{}" using "{}"'.format(
            salesman_settings.SALESMAN_PRICE_FORMATTER(payment.amount, {}),
            payment.transaction_id,
            payment.payment_method_display,
        )
        data = cls.get_payment_data(payment)
        return cls.create(payment.order, cls.Kind.PAYMENT_UPDATED, info, data)

    @classmethod
    def payment_removed(cls, payment: OrderPayment) -> OrderLog:
        info = f'Payment with ID "{payment.transaction_id}" removed'
        data = cls.get_payment_data(payment)
        return cls.create(payment.order, cls.Kind.PAYMENT_REMOVED, info, data)

    @staticmethod
    def get_payment_data(payment: OrderPayment):
        return {
            "amount": repr(payment.amount),
            "transaction_id": payment.transaction_id,
            "payment_method": payment.payment_method,
        }

    @staticmethod
    def get_status_display(status: str) -> str:
        return str(dict(Order.get_statuses().choices).get(status, status))


@receiver(post_save, sender=Order, dispatch_uid="log_order_created")
def log_order_created(sender, instance, created, **kwargs):
    if created:
        OrderLog.order_created(instance)


@receiver(post_init, sender=OrderPayment, dispatch_uid="log_init_payment")
def log_init_payment(sender, instance, **kwargs):
    instance._payment_data = OrderLog.get_payment_data(instance)


@receiver(post_save, sender=OrderPayment, dispatch_uid="log_payment_added_updated")
def log_payment_added_updated(sender, instance, created, **kwargs):
    if created:
        OrderLog.payment_added(instance)
    elif instance._payment_data != OrderLog.get_payment_data(instance):
        OrderLog.payment_updated(instance)


@receiver(post_delete, sender=OrderPayment, dispatch_uid="log_payment_removed")
def log_payment_removed(sender, instance, **kwargs):
    OrderLog.payment_removed(instance)


@receiver(status_changed, dispatch_uid="log_status_changed")
def log_status_changed(sender, order, new_status, old_status, **kwargs):
    OrderLog.status_changed(order, old_status, new_status)
