from __future__ import annotations

from dataclasses import dataclass
from typing import Optional, Union
from urllib.parse import urljoin

import requests
from django.conf import settings

SENDCLOUD_BASE_URL = "https://panel.sendcloud.sc/api/v2/"


class Client:
    def __init__(self):
        self.client = requests.Session()
        self.client.auth = (settings.SENDCLOUD_API_KEY, settings.SENDCLOUD_API_SECRET)

    def get_url(self, url: str) -> str:
        return urljoin(SENDCLOUD_BASE_URL, url)

    def post(
        self,
        url: str,
        data: Optional[Union[dict, list]] = None,
    ) -> requests.Response:
        return self.client.post(self.get_url(url), json=data)

    def get(self, url: str, params: Optional[dict] = None) -> requests.Response:
        return self.client.get(self.get_url(url), params=params)

    def put(
        self,
        url: str,
        data: Optional[Union[dict, list]] = None,
    ) -> requests.Response:
        return self.client.put(self.get_url(url), json=data)

    # ----------------------------------------------------------------------------------
    # Parcel
    # ----------------------------------------------------------------------------------

    def get_parcels(self) -> list[Parcel]:
        data = self.get("parcels").json()
        return [Parcel.from_dict(x) for x in data["parcels"]]

    def get_parcel(self, id: str) -> Optional[Parcel]:
        response = self.get(f"parcels/{id}")
        if response.status_code == 200:
            return Parcel.from_dict(response.json()["parcel"])
        return None

    def create_parcel(self, parcel: Parcel) -> dict:
        data = {"parcel": parcel.to_dict()}
        return self.post("parcels", data).json()

    def create_parcels(self, parcels: list[Parcel]) -> dict:
        data = {"parcels": [x.to_dict() for x in parcels]}
        return self.post("parcels?errors=verbose", data).json()

    def get_parcel_statuses(self) -> dict:
        return self.get("parcels/statuses").json()


@dataclass
class Parcel:
    name: str
    address: str
    city: str
    postal_code: str
    country: str
    country_state: str
    customs_invoice_nr: str
    customs_shipment_type: int
    parcel_items: list[ParcelItem]
    house_number: Optional[str] = None
    address_2: Optional[str] = None
    company_name: Optional[str] = None
    to_post_number: Optional[str] = None
    telephone: Optional[int] = None
    email: Optional[str] = None
    sender_address: Optional[int] = None
    external_reference: Optional[str] = None
    quantity: Optional[int] = None
    to_service_point: Optional[int] = None
    insured_value: Optional[int] = None
    total_insured_value: Optional[int] = None
    shipping_method_checkout_name: Optional[str] = None
    order_number: Optional[str] = None
    shipment_uuid: Optional[str] = None
    weight: Optional[str] = None
    is_return: Optional[bool] = None
    total_order_value: Optional[float] = None
    total_order_value_currency: Optional[str] = None
    length: Optional[float] = None
    width: Optional[float] = None
    height: Optional[float] = None

    def to_dict(self, include_none_fields=False):
        data = self.__dict__
        data["parcel_items"] = [
            x.to_dict(include_none_fields) for x in data["parcel_items"]
        ]
        if not include_none_fields:
            data = {k: v for k, v in data.items() if v is not None}
        return data

    @classmethod
    def from_dict(cls, data: dict) -> Parcel:
        return cls(**data)


@dataclass
class ParcelItem:
    description: str
    quantity: int
    weight: str
    value: float
    hs_code: str
    origin_country: str
    sku: Optional[str] = None
    product_id: Optional[str] = None
    properties: Optional[dict] = None

    def to_dict(self, include_none_fields=False):
        data = self.__dict__
        if not include_none_fields:
            data = {k: v for k, v in data.items() if v is not None}
        return data
