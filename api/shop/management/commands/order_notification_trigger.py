import datetime

from django.core.management.base import BaseCommand
from django.utils.timezone import now
from salesman.orders.models import Order

from notification.models import get_context_and_notify_status_changed


class Command(BaseCommand):
    help = "Hourly order notification trigger"

    def handle(self, *args, **options):
        run_datetime = now()
        start_datetime = run_datetime.replace(minute=0, second=0, microsecond=0)

        offset = max(1, (start_datetime.weekday() + 6) % 7 - 3) + 2

        check_start_datetime = start_datetime - datetime.timedelta(offset)
        check_end_datetime = check_start_datetime + datetime.timedelta(hours=1)

        orders = Order.objects.filter(
            date_updated__range=[check_start_datetime, check_end_datetime],
            status="PROCESSING",
        )
        for order in orders:
            order.status = "IN_PRODUCTION"
            get_context_and_notify_status_changed(order, "ORDER_IN_PRODUCTION")
