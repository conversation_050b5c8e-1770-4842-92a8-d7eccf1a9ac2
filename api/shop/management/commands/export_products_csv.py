# catalog/management/commands/export_products_csv.py

import csv
import os
import inspect
from django.core.management.base import BaseCommand
from catalog.models.product import Product, ProductVariant
from django.utils import timezone
from django.apps import apps


class Command(BaseCommand):
    help = "Exports products to a CSV file in the required format with support for multiple options"

    def add_arguments(self, parser):
        parser.add_argument(
            "--output", default="products_export.csv", help="Path to output CSV file"
        )
        parser.add_argument(
            "--locale", default="en", help="Locale code for product data"
        )
        parser.add_argument(
            "--fallback", default="es", help="Fallback locale if primary not found"
        )
        parser.add_argument(
            "--product-model",
            default="",
            help="Specific product model to export (e.g., catalog.ClothingProduct)",
        )
        parser.add_argument(
            "--media-url",
            default="https://leeos-merch.storage.googleapis.com",
            help="Base URL for media. Will be prepended to relative paths that start with /media/",
        )

    def handle(self, *args, **options):
        output_file = options["output"]
        locale = options["locale"]
        fallback_locale = options["fallback"]
        product_model = options["product_model"]
        media_url = options["media_url"].rstrip("/")

        self.stdout.write(
            self.style.SUCCESS(
                f"Exporting products to {output_file} in {locale} locale..."
            )
        )
        self.stdout.write(f"Using media URL: {media_url}")

        # Base CSV fields
        base_fields = [
            "Product Id",
            "Product Handle",
            "Product URL",
            "Product Backend URL",
            "Product Title",
            "Product Subtitle",
            "Product Description",
            "Product Status",
            "Product Thumbnail",
            "Product Weight",
            "Product Length",
            "Product Width",
            "Product Height",
            "Product HS Code",
            "Product Origin Country",
            "Product MID Code",
            "Product Material",
            "Product Collection Title",
            "Product Collection Handle",
            "Product Collection URL",
            "Product Type",
            "Product Tags",
            "Product Discountable",
            "Product External Id",
            "Product Profile Name",
            "Product Profile Type",
            "Variant Id",
            "Variant Title",
            "Variant SKU",
            "Variant Barcode",
            "Variant Inventory Quantity",
            "Variant Allow Backorder",
            "Variant Manage Inventory",
            "Variant Weight",
            "Variant Length",
            "Variant Width",
            "Variant Height",
            "Variant HS Code",
            "Variant Origin Country",
            "Variant MID Code",
            "Variant Material",
            "Price EUR",
            "Price USD",
        ]

        # Support up to 2 options
        option_fields = []
        for i in range(1, 3):
            option_fields.extend([f"Option {i} Name", f"Option {i} Value"])

        # Image fields
        image_fields = ["Image 1 Url", "Image 2 Url"]

        # Combine all fields
        fields = base_fields + option_fields + image_fields

        with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(
                csvfile, delimiter=";", quotechar='"', quoting=csv.QUOTE_MINIMAL
            )

            # Write header row
            writer.writerow(fields)

            # Get all concrete product classes that inherit from Product
            concrete_product_models = self.get_concrete_product_models()

            if product_model:
                # If specific model is requested, filter to only that model
                app_label, model_name = product_model.split(".")
                model_class = apps.get_model(app_label, model_name)
                concrete_product_models = [model_class]
                self.stdout.write(f"Exporting only {product_model} products")

            total_products = 0
            total_exported = 0

            # Process each product model
            for model_class in concrete_product_models:
                try:
                    # Check if this model has implemented get_variant_model
                    if not self.has_implemented_get_variant_model(model_class):
                        self.stdout.write(
                            self.style.WARNING(
                                f"Skipping {model_class.__name__}: get_variant_model not implemented"
                            )
                        )
                        continue

                    # Get all products of this type
                    products = model_class.objects.live().specific()
                    count = products.count()
                    total_products += count

                    self.stdout.write(f"Found {count} {model_class.__name__} products")

                    for index, product in enumerate(products):
                        # Progress indicator
                        if (index + 1) % 10 == 0 or (index + 1) == count:
                            self.stdout.write(
                                f"Processing {model_class.__name__} product {index + 1}/{count}"
                            )

                        try:
                            # Get variants for this product
                            variants = product.get_variants()

                            if not variants.exists():
                                self.stdout.write(
                                    self.style.WARNING(
                                        f"No variants found for product {product.title}"
                                    )
                                )
                                continue

                            # Get product collection
                            collection = product.get_parent().specific
                            collection_title = collection.title if collection else ""
                            collection_handle = collection.slug if collection else ""
                            try:
                                collection_url = (
                                    collection.get_full_url() if collection else ""
                                )
                                if (
                                    collection and not collection_url
                                ):  # get_full_url() returned empty string
                                    raise Exception("Empty URL returned")
                            except:
                                collection_url = (
                                    f"/collections/{collection_handle}/"
                                    if collection_handle
                                    else ""
                                )

                            # Get product thumbnail
                            product_media = product.get_media_data()
                            product_thumbnail = ""
                            if product_media:
                                thumbnail_url = (
                                    product_media[0].get("large", {}).get("url", "")
                                )
                                product_thumbnail = self.normalize_image_url(
                                    thumbnail_url, media_url
                                )

                            # Process each variant
                            for variant in variants:
                                try:
                                    # Set language for variant
                                    variant.set_language(locale)

                                    # Get variant attributes
                                    combo_data = variant.get_localized_data(
                                        "combo_data"
                                    )

                                    # If combo_data is empty for the primary locale, try fallback
                                    if not combo_data or "attrs" not in combo_data:
                                        variant.set_language(fallback_locale)
                                        combo_data = variant.get_localized_data(
                                            "combo_data"
                                        )

                                    # Extract all options from combo_data
                                    options = []
                                    if combo_data and "attrs" in combo_data:
                                        # Convert attrs dictionary to a list of (name, value) pairs
                                        for attr_code, attr_data in combo_data[
                                            "attrs"
                                        ].items():
                                            options.append(
                                                (
                                                    attr_data.get(
                                                        "label", attr_code.capitalize()
                                                    ),
                                                    attr_data.get(
                                                        "value_label",
                                                        attr_data.get("value", ""),
                                                    ),
                                                )
                                            )

                                    # If no options were found, add a default one
                                    if not options:
                                        options = [("Size", "Default")]

                                    # Get variant media
                                    variant_media = variant.get_media_data()
                                    image_urls = []

                                    # First try variant media
                                    if variant_media:
                                        for media in variant_media[
                                            :2
                                        ]:  # Get first two images
                                            image_url = media.get("large", {}).get(
                                                "url", ""
                                            )
                                            if image_url:
                                                image_urls.append(
                                                    self.normalize_image_url(
                                                        image_url, media_url
                                                    )
                                                )

                                    # Use product media as fallback if variant has no images
                                    if not image_urls and product_media:
                                        for media in product_media[
                                            :2
                                        ]:  # Get first two images
                                            image_url = media.get("large", {}).get(
                                                "url", ""
                                            )
                                            if image_url:
                                                image_urls.append(
                                                    self.normalize_image_url(
                                                        image_url, media_url
                                                    )
                                                )

                                    # Ensure we have at least 2 image slots (even if empty)
                                    while len(image_urls) < 2:
                                        image_urls.append("")

                                    # Convert product status
                                    product_status = (
                                        "published" if product.live else "draft"
                                    )

                                    # Get prices - keep as decimal with 2 decimal places
                                    price_eur = "{:.2f}".format(variant.get_price())

                                    # Generate frontend product URL
                                    try:
                                        product_url = product.get_full_url()
                                        if (
                                            not product_url
                                        ):  # get_full_url() returned empty string
                                            raise Exception("Empty URL returned")
                                    except:
                                        # Fallback if get_full_url() fails
                                        product_url = f"/products/{product.slug}/"

                                    # Generate backend API URL
                                    try:
                                        # Get the site's root URL for the backend API
                                        from django.conf import settings
                                        from wagtail.models import Site

                                        # Try to get the default site or first available site
                                        try:
                                            site = Site.objects.get(
                                                is_default_site=True
                                            )
                                        except Site.DoesNotExist:
                                            site = Site.objects.first()

                                        if site:
                                            # Construct backend API URL
                                            protocol = (
                                                "https"
                                                if getattr(
                                                    settings,
                                                    "SECURE_SSL_REDIRECT",
                                                    False,
                                                )
                                                else "http"
                                            )
                                            backend_url = f"{protocol}://{site.hostname}/api/v1/pages/{product.id}/"
                                        else:
                                            # Fallback to a default backend URL pattern
                                            backend_url = f"https://cms.madkat.store/api/v1/pages/{product.id}/"
                                    except Exception as e:
                                        # Fallback backend URL if anything fails
                                        backend_url = f"https://cms.madkat.store/api/v1/pages/{product.id}/"

                                    # Prepare base row data
                                    row = [
                                        "",  # Product Id (leave empty)
                                        product.slug,  # Product Handle
                                        product_url,  # Product URL
                                        backend_url,  # Product Backend URL
                                        product.title,  # Product Title
                                        "",  # Product Subtitle
                                        product.details,  # Product Description
                                        product_status,  # Product Status
                                        product_thumbnail,  # Product Thumbnail
                                        product.base_weight,  # Product Weight
                                        "",  # Product Length
                                        "",  # Product Width
                                        "",  # Product Height
                                        variant.hs_code,  # Product HS Code
                                        product.origin_country,  # Product Origin Country
                                        "",  # Product MID Code
                                        "",  # Product Material
                                        collection_title,  # Product Collection Title
                                        collection_handle,  # Product Collection Handle
                                        collection_url,  # Product Collection URL
                                        model_class.__name__,  # Product Type
                                        "",  # Product Tags
                                        "true",  # Product Discountable
                                        str(product.id),  # Product External Id
                                        "",  # Product Profile Name
                                        "",  # Product Profile Type
                                        "",  # Variant Id (leave empty)
                                        variant.name,  # Variant Title
                                        variant.code,  # Variant SKU
                                        (
                                            variant.ean13
                                            if hasattr(variant, "ean13")
                                            and variant.ean13
                                            else ""
                                        ),  # Variant Barcode
                                        (
                                            variant.quantity
                                            if variant.quantity is not None
                                            else 100
                                        ),  # Variant Inventory Quantity
                                        "false",  # Variant Allow Backorder
                                        "true",  # Variant Manage Inventory
                                        variant.get_weight(),  # Variant Weight
                                        "",  # Variant Length
                                        "",  # Variant Width
                                        "",  # Variant Height
                                        variant.hs_code,  # Variant HS Code
                                        product.origin_country,  # Variant Origin Country
                                        "",  # Variant MID Code
                                        "",  # Variant Material
                                        price_eur,  # Price EUR (in cents)
                                        "",  # Price USD (leave empty)
                                    ]

                                    # Add option fields (up to 2)
                                    for i in range(2):
                                        if i < len(options):
                                            row.extend([options[i][0], options[i][1]])
                                        else:
                                            row.extend(["", ""])

                                    # Add image URLs
                                    row.extend(image_urls)

                                    writer.writerow(row)
                                    total_exported += 1

                                except Exception as e:
                                    self.stdout.write(
                                        self.style.ERROR(
                                            f"Error processing variant {variant.code}: {str(e)}"
                                        )
                                    )
                                    import traceback

                                    self.stdout.write(
                                        self.style.ERROR(traceback.format_exc())
                                    )

                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(
                                    f"Error processing product {product.title}: {str(e)}"
                                )
                            )
                            import traceback

                            self.stdout.write(self.style.ERROR(traceback.format_exc()))

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Error processing model {model_class.__name__}: {str(e)}"
                        )
                    )
                    import traceback

                    self.stdout.write(self.style.ERROR(traceback.format_exc()))

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully exported {total_exported} variants from {total_products} products to {output_file}"
                )
            )

    def normalize_image_url(self, url, media_base_url):
        """
        Normalizes image URLs to ensure they are absolute and use the correct domain.

        Handles cases:
        - Absolute URLs (starting with http:// or https://)
        - Media paths starting with /media/
        - Empty or None values
        """
        if not url:
            return ""

        # If URL is already absolute with https:// or http://
        if url.startswith(("http://", "https://")):
            return url

        # If URL starts with /media/, prepend the media base URL
        if url.startswith("/media/"):
            return f"{media_base_url}{url}"

        # For other relative URLs
        return url

    def get_concrete_product_models(self):
        """Get all concrete product models that inherit from Product"""
        from django.apps import apps

        product_models = []
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                if (
                    issubclass(model, Product)
                    and model is not Product
                    and not model._meta.abstract
                ):
                    product_models.append(model)

        return product_models

    def has_implemented_get_variant_model(self, model_class):
        """Check if the model has implemented its own get_variant_model method"""
        # Get the method from the class
        method = model_class.get_variant_model

        # Check if it's the same as the one in Product
        if method.__qualname__ == "Product.get_variant_model":
            return False

        return True
