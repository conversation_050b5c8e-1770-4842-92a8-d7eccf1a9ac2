from datetime import timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone
from salesman.basket.models import Basket

from notification.models import NotificationTrigger


class Command(BaseCommand):
    help = "Check for abandoned baskets within the last day and notify the user"

    def handle(self, *args, **options):
        twentyFourHours = timezone.now() - timedelta(hours=24)
        fortyEightHours = timezone.now() - timedelta(hours=48)

        baskets = Basket.objects.filter(
            date_created__range=[fortyEightHours, twentyFourHours], owner__isnull=False
        )

        if not baskets.exists():
            self.stdout.write(self.style.SUCCESS("No abandoned baskets found"))
            return

        for basket in baskets:
            context = {"basket": basket, "customer": basket.owner}
            NotificationTrigger.dispatch_triggers("BASKET_ABANDONED", context)

        msg = "Notified '%d' users of their abandoned baskets" % baskets.count()
        self.stdout.write(self.style.SUCCESS(msg))
