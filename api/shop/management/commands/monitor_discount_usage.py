"""
Management command to monitor and clean up discount code reservations.
Useful for high-traffic scenarios to prevent memory leaks and monitor usage.
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta
import logging

from shop.models import Discount
from shop.discount.validators import DiscountValidator

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Monitor and clean up discount code reservations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up expired reservations',
        )
        parser.add_argument(
            '--monitor',
            action='store_true',
            help='Show current reservation status',
        )
        parser.add_argument(
            '--discount-code',
            type=str,
            help='Specific discount code to monitor/cleanup',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Verbose output',
        )

    def handle(self, *args, **options):
        if options['monitor']:
            self.monitor_discounts(options)
        
        if options['cleanup']:
            self.cleanup_reservations(options)
            
        if not options['monitor'] and not options['cleanup']:
            self.stdout.write(
                self.style.WARNING('Please specify --monitor or --cleanup')
            )

    def monitor_discounts(self, options):
        """Monitor current discount usage and reservations"""
        self.stdout.write(
            self.style.SUCCESS('=== Discount Usage Monitor ===')
        )
        
        discount_code = options.get('discount_code')
        verbose = options.get('verbose', False)
        
        if discount_code:
            discounts = Discount.objects.filter(code__iexact=discount_code)
        else:
            discounts = Discount.objects.filter(
                is_active=True,
                max_uses__isnull=False
            )
        
        for discount in discounts:
            self.monitor_single_discount(discount, verbose)

    def monitor_single_discount(self, discount, verbose=False):
        """Monitor a single discount code"""
        try:
            # Get current reservations from Redis
            reserved_baskets = DiscountValidator._get_reserved_baskets(discount.code)
            reserved_count = len(reserved_baskets)
            
            # Calculate usage statistics
            total_usage = discount.times_used + reserved_count
            remaining = max(0, (discount.max_uses or 0) - total_usage)
            usage_percent = (total_usage / discount.max_uses * 100) if discount.max_uses else 0
            
            # Color coding based on usage
            if usage_percent >= 90:
                style = self.style.ERROR
            elif usage_percent >= 75:
                style = self.style.WARNING
            else:
                style = self.style.SUCCESS
                
            self.stdout.write(
                style(f'\nDiscount: {discount.code}')
            )
            self.stdout.write(f'  Max Uses: {discount.max_uses}')
            self.stdout.write(f'  Times Used: {discount.times_used}')
            self.stdout.write(f'  Reserved: {reserved_count}')
            self.stdout.write(f'  Total Usage: {total_usage}')
            self.stdout.write(f'  Remaining: {remaining}')
            self.stdout.write(f'  Usage: {usage_percent:.1f}%')
            
            if verbose and reserved_baskets:
                self.stdout.write(f'  Reserved Baskets: {", ".join(reserved_baskets)}')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error monitoring {discount.code}: {e}')
            )

    def cleanup_reservations(self, options):
        """Clean up expired reservations"""
        self.stdout.write(
            self.style.SUCCESS('=== Cleaning Up Reservations ===')
        )
        
        discount_code = options.get('discount_code')
        
        if discount_code:
            cleaned = DiscountValidator.cleanup_expired_reservations(discount_code)
            self.stdout.write(
                self.style.SUCCESS(f'Cleaned up reservations for {discount_code}')
            )
        else:
            # Clean up all expired reservations
            # Note: This is a simplified version. In production, you might want
            # to iterate through all active discount codes
            self.stdout.write(
                self.style.WARNING(
                    'Redis TTL handles automatic cleanup. '
                    'Manual cleanup only needed for specific codes.'
                )
            )
