from typing import Optional

from django.core.exceptions import ValidationError
from django.utils.translation import pgettext_lazy

from country.utils import get_country_choices
from shop.parsers import parse_address


def validate_extra(value: dict, context: dict) -> dict:
    from .modifiers import ShippingModifier
    from .modifiers import DiscountModifier

    value = value or {}
    data = {}

    country_key = ShippingModifier.country_key
    if country_key in value:
        country = value[country_key].upper()
        if country not in [x[0] for x in get_country_choices(shipping=False)]:
            raise ValidationError(pgettext_lazy("Country", "Invalid country provided."))
        data[country_key] = country

    postal_code_key = ShippingModifier.postal_code_key
    if postal_code_key in value:
        postal_code = str(value[postal_code_key])
        if len(postal_code) > 32:
            raise ValidationError(pgettext_lazy("Postal code", "Postal code to long."))
        data[postal_code_key] = postal_code

    discount_key = DiscountModifier.discount_key
    if discount_key in value:
        data[discount_key] = value[discount_key]
    

    return data


def validate_basket_item(attrs: dict, context: dict) -> dict:
    from .utils import clean_product_for_purchase

    if not context.get("basket_item", None):
        # Check if new item can be added to basket
        attrs["product"] = clean_product_for_purchase(
            context["request"],
            attrs["product"],
            attrs["quantity"],
        )
    return attrs


def validate_address(value: str, context: Optional[dict] = None) -> str:
    """
    Validate address with serializer context.
    """
    parse_address(value)
    return value
