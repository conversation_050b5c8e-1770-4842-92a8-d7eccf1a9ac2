from __future__ import annotations

import secrets
from typing import TYPE_CHECKING, Optional

from django.conf import settings
from django.core.exceptions import ValidationError
from django.http import HttpRequest
from django.utils import timezone
from django.utils.translation import get_language_from_request, pgettext_lazy
from salesman.orders.models import Order

if TYPE_CHECKING:
    from catalog.models.product import ProductVariant


def clean_currency(currency: Optional[str] = None) -> str:
    """
    Cleans currency and returns default base currency if invalid or None.
    """
    if not currency or currency not in settings.CURRENCIES:
        return settings.BASE_CURRENCY
    return currency


def get_currency_from_request(request: HttpRequest) -> Optional[str]:
    """
    Returns currency code from request or None.
    """
    currency = request.GET.get("currency", None)

    if not currency:
        currency = request.headers.get("Accept-Currency", None)

    return currency.upper() if currency else None


def get_cleaned_currency_from_request(request: HttpRequest) -> str:
    """
    Returns cleaned currency from request.
    """
    return clean_currency(get_currency_from_request(request))


def clean_product_for_purchase(
    request: HttpRequest,
    product: ProductVariant,
    quantity: int,
) -> ProductVariant:
    """
    Checks that given product is available and valid for purchase.

    Raises:
        ValidationError: If product not available or invalid

    Returns:
        ProductVariant: Cleaned product variant
    """
    product.set_language(get_language_from_request(request))

    if not product.is_available_quantity(quantity):
        raise ValidationError(
            pgettext_lazy("Product", "Product is not available."),
            code="unavailable",
            params={"quantity": product.available_quantity},
        )

    product_data = product.get_localized_data("product_data")
    if not product_data.get("live", None):
        msg = pgettext_lazy("Product", "Product is not published.")
        raise ValidationError(msg, code="invalid")

    return product


def generate_ref(request) -> str:
    """
    Generate reference for new order.
    """
    ts = timezone.now()
    token = settings.SHOP_ORDER_REFERENCE_TWO_LETTER_PREFIX[:2] + secrets.token_hex(3)
    ref = f"{str(ts.year)[2:]}{ts.month:02}{ts.day:02}-{token}"
    if Order.objects.filter(ref=ref).exists():
        return generate_ref(request)
    return ref
