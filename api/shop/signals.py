"""
Signal handlers for order status changes, specifically handling cancellations and refunds.
"""
import logging
from decimal import Decimal
from typing import List

from django.db import transaction
from django.dispatch import receiver
from salesman.orders.models import Order, OrderItem
from salesman.orders.signals import status_changed

from catalog.models.product import ProductVariant
from stats.models import ProductStats

logger = logging.getLogger(__name__)


@receiver(status_changed, dispatch_uid="handle_cancellation_refund")
def handle_order_cancellation_or_refund(sender, order: Order, new_status: str, old_status: str, **kwargs):
    """
    Handle order cancellation or refund by:
    1. Restoring stock to variants
    2. Adjusting sales statistics (subtracting from units sold)
    3. Handling unlimited stock variants properly
    """
    # Only process cancellations and refunds
    if new_status not in ["CANCELLED", "REFUNDED"]:
        return
    
    # Skip if order was already cancelled/refunded (avoid double processing)
    if old_status in ["CANCELLED", "REFUNDED"]:
        logger.info(f"Order {order.ref} already processed for {old_status}, skipping {new_status}")
        return
    
    # Only process orders that had sales recorded (i.e., were PROCESSING or later)
    if old_status not in ["PROCESSING", "SHIPPED", "COMPLETED"]:
        logger.info(f"Order {order.ref} was {old_status}, no stock/sales to restore")
        return
    
    logger.info(f"Processing {new_status} for order {order.ref} (was {old_status})")
    
    try:
        with transaction.atomic():
            _restore_stock_and_adjust_sales(order, new_status)
    except Exception as e:
        logger.error(f"Error processing {new_status} for order {order.ref}: {e}")
        raise


def _restore_stock_and_adjust_sales(order: Order, status: str):
    """
    Restore stock and adjust sales statistics for cancelled/refunded order.
    """
    order_items = order.items.all()
    if not order_items:
        logger.warning(f"No items found for order {order.ref}")
        return
    
    # Get all product variants involved
    product_ids = [item.product_id for item in order_items]
    variants = ProductVariant.objects.filter(id__in=product_ids)
    variants_map = {variant.id: variant for variant in variants}
    
    # Track changes for logging
    stock_changes = []
    sales_adjustments = []
    
    for item in order_items:
        variant = variants_map.get(item.product_id)
        if not variant:
            logger.warning(f"Variant {item.product_id} not found for item {item.id}")
            continue
        
        # 1. Restore stock (only for variants with limited stock)
        if variant.quantity is not None:  # Limited stock variant
            old_quantity = variant.quantity
            variant.increase_stock(item.quantity, commit=False)
            stock_changes.append({
                'variant_id': variant.id,
                'variant_code': variant.code,
                'quantity_restored': item.quantity,
                'old_stock': old_quantity,
                'new_stock': variant.quantity
            })
        else:
            stock_changes.append({
                'variant_id': variant.id,
                'variant_code': variant.code,
                'quantity_restored': item.quantity,
                'old_stock': 'unlimited',
                'new_stock': 'unlimited'
            })
        
        # 2. Adjust sales statistics
        try:
            stats = ProductStats.objects.get(variant_id=item.product_id)
            old_sold_quantity = stats.sold_quantity
            old_sold_total = stats.sold_total
            
            # Subtract the cancelled/refunded quantities and amounts
            stats.sold_quantity = max(0, stats.sold_quantity - item.quantity)
            stats.sold_total = max(Decimal('0'), stats.sold_total - item.total)
            
            sales_adjustments.append({
                'variant_id': variant.id,
                'variant_code': variant.code,
                'quantity_adjusted': item.quantity,
                'amount_adjusted': item.total,
                'old_sold_quantity': old_sold_quantity,
                'new_sold_quantity': stats.sold_quantity,
                'old_sold_total': old_sold_total,
                'new_sold_total': stats.sold_total
            })
            
        except ProductStats.DoesNotExist:
            logger.warning(f"No sales stats found for variant {item.product_id}")
            sales_adjustments.append({
                'variant_id': variant.id,
                'variant_code': variant.code,
                'quantity_adjusted': item.quantity,
                'amount_adjusted': item.total,
                'old_sold_quantity': 0,
                'new_sold_quantity': 0,
                'old_sold_total': Decimal('0'),
                'new_sold_total': Decimal('0'),
                'note': 'No existing stats found'
            })
    
    # Bulk save all changes
    if variants:
        # Save stock changes (only for variants with limited stock)
        limited_stock_variants = [v for v in variants if v.quantity is not None]
        if limited_stock_variants:
            ProductVariant.objects.bulk_update(limited_stock_variants, ['quantity'])
        
        # Save sales statistics changes
        stats_to_update = []
        for item in order_items:
            try:
                stats = ProductStats.objects.get(variant_id=item.product_id)
                stats.sold_quantity = max(0, stats.sold_quantity - item.quantity)
                stats.sold_total = max(Decimal('0'), stats.sold_total - item.total)
                stats_to_update.append(stats)
            except ProductStats.DoesNotExist:
                pass
        
        if stats_to_update:
            ProductStats.objects.bulk_update(stats_to_update, ['sold_quantity', 'sold_total'])
    
    # Log the changes
    logger.info(f"Order {order.ref} {status.lower()} - Stock restored for {len(stock_changes)} variants")
    logger.info(f"Order {order.ref} {status.lower()} - Sales adjusted for {len(sales_adjustments)} variants")
    
    # Detailed logging for debugging
    for change in stock_changes:
        if change['old_stock'] == 'unlimited':
            logger.debug(f"Variant {change['variant_code']}: Unlimited stock (no restoration needed)")
        else:
            logger.debug(f"Variant {change['variant_code']}: Stock {change['old_stock']} → {change['new_stock']} (+{change['quantity_restored']})")
    
    for adjustment in sales_adjustments:
        logger.debug(f"Variant {adjustment['variant_code']}: Sales {adjustment['old_sold_quantity']} → {adjustment['new_sold_quantity']} (-{adjustment['quantity_adjusted']})")


def get_order_cancellation_summary(order: Order) -> dict:
    """
    Get a summary of what would be restored/adjusted if this order were cancelled.
    Useful for admin interfaces or confirmation dialogs.
    """
    order_items = order.items.all()
    if not order_items:
        return {'items': [], 'total_items': 0, 'has_limited_stock': False}
    
    product_ids = [item.product_id for item in order_items]
    variants = ProductVariant.objects.filter(id__in=product_ids)
    variants_map = {variant.id: variant for variant in variants}
    
    summary_items = []
    has_limited_stock = False
    
    for item in order_items:
        variant = variants_map.get(item.product_id)
        if not variant:
            continue
        
        is_limited_stock = variant.quantity is not None
        if is_limited_stock:
            has_limited_stock = True
        
        # Get current sales stats
        try:
            stats = ProductStats.objects.get(variant_id=item.product_id)
            current_sold = stats.sold_quantity
            current_total = stats.sold_total
        except ProductStats.DoesNotExist:
            current_sold = 0
            current_total = Decimal('0')
        
        summary_items.append({
            'variant_code': variant.code,
            'variant_name': getattr(variant, 'name', variant.code),
            'quantity_to_restore': item.quantity,
            'amount_to_adjust': item.total,
            'current_stock': variant.quantity if is_limited_stock else 'unlimited',
            'stock_after_restore': variant.quantity + item.quantity if is_limited_stock else 'unlimited',
            'current_sold_quantity': current_sold,
            'sold_after_adjustment': max(0, current_sold - item.quantity),
            'is_limited_stock': is_limited_stock
        })
    
    return {
        'items': summary_items,
        'total_items': len(summary_items),
        'has_limited_stock': has_limited_stock
    }
