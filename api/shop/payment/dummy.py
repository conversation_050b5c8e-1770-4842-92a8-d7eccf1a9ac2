import os

from django.core.exceptions import ValidationError
from django.http import HttpRequest
from django.urls import reverse
from salesman.basket.models import Basket
from salesman.orders.models import Order

from catalog.models import BasketStockReservation

from .base import PaymentMethod


class DummyPayment(PaymentMethod):
    identifier = "dummy"
    label = "Dummy payment for testing"

    def validate_debug(self):
        if os.getenv("DJANGO_SETTINGS_MODULE") not in [
            "project.settings.dev",
            "project.settings.loadtest",
        ]:
            raise ValidationError("Dummy payment only available in testing environment")

    def validate_basket(self, basket: Basket, request: HttpRequest):
        self.validate_debug()
        super().validate_basket(basket, request)

    def validate_order(self, order: Order, request: HttpRequest) -> None:
        self.validate_debug()
        return super().validate_order(order, request)

    def basket_payment(self, basket: Basket, request: HttpRequest) -> str:
        BasketStockReservation.create_and_reserve(basket)
        order = Order.objects.create_from_basket(basket, request)
        basket.delete()
        return reverse("salesman-order-last") + "?token=" + order.token
