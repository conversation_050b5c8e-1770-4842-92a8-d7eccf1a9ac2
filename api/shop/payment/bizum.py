from typing import Optional

from django.urls import path
from salesman.basket.models import Basket
from salesman.orders.models import Order

from .redsys import RedSysPayment


class BizumPayment(RedSysPayment):
    identifier = "bizum"
    label = "Bizum"

    def get_urls(self) -> list:
        return [
            path("response/", self.response_view, name="bizum-response"),
        ]

    def get_redsys_data(
        self,
        request,
        order: Order,
        basket: Optional[Basket] = None,
    ) -> dict:
        params, extra_params = super().get_redsys_data(request, order, basket)
        extra_params = {"payment_method": "z"}
        return params, extra_params

    def basket_payment(self, basket: Basket, request) -> str:
        return super().basket_payment(basket, request)

    def order_payment(self, order: Order, request) -> str:
        return super().order_payment(order, request)
