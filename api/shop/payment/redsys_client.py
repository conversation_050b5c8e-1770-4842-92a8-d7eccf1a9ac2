import re
from typing import Any, Optional

from redsys.client import (
    DEFAULT_SIGNATURE_VERSION,
    MERCHANT_PARAMETERS,
    SIG<PERSON>TURE,
    SIGNATURE_VERSION,
)
from redsys.client import Redirect<PERSON>lient as BaseRedirectClient
from redsys.request import Request as BaseRequest

EXTRA_PARAMETERS_MAP: dict[str, str] = {"payment_method": "Ds_Merchant_PayMethods"}


class _Request(BaseRequest):
    @staticmethod
    def check_order(value):
        if not re.match(r"[0-9]{4}[a-zA-Z0-9]{5,8}$", value):
            raise ValueError("order format is not valid.")


class Request:
    def __init__(
        self,
        parameters: dict[str, Any],
        extra_parameters: Optional[dict[str, Any]] = None,
    ):
        self._request = _Request(parameters)
        self._extra_parameters: dict[str, Any] = {}
        if extra_parameters:
            for key, value in extra_parameters.items():
                if key in EXTRA_PARAMETERS_MAP:
                    self._extra_parameters[EXTRA_PARAMETERS_MAP[key]] = value
                else:
                    raise ValueError(f"Unknown extra parameter {key}")

    def __getattr__(self, item: str) -> Any:
        return self._request.__getattr__(item)

    def prepare_parameters(self) -> dict[str, str]:
        parameters = self._request.prepare_parameters()
        parameters.update(self._extra_parameters)
        return parameters


class RedirectClient(BaseRedirectClient):
    def prepare_request(
        self,
        parameters: dict[str, Any],
        extra_parameters: Optional[dict[str, Any]] = None,
    ) -> dict[str, Any]:
        request = Request(parameters, extra_parameters)
        merchant_parameters = self.encode_parameters(request.prepare_parameters())
        signature = self.generate_signature(request.order, merchant_parameters)
        return {
            SIGNATURE_VERSION: DEFAULT_SIGNATURE_VERSION,
            MERCHANT_PARAMETERS: merchant_parameters,
            SIGNATURE: signature,
        }
