import html
import json
import logging
from decimal import ROUND_HALF_UP, Decimal
from typing import Optional
from urllib.parse import urljoin

from django.conf import settings
from django.db import IntegrityError, transaction
from django.http import HttpResponseBadRequest
from django.http.response import HttpResponse, HttpResponseNotAllowed
from django.urls import path, reverse
from django.utils.encoding import force_str
from django.utils.translation import get_language_from_request
from django.views.decorators.csrf import csrf_exempt
from redsys import constants
from salesman.basket.models import Basket
from salesman.checkout.payment import PaymentError
from salesman.orders.models import Order

from catalog.models import BasketStockReservation
from stats.models import ProductStats

from ..models import OrderRedsysReference, OrderShipping
from .base import PaymentMethod
from .redsys_client import MERCHANT_PARAMETERS, SIGNATURE, RedirectClient

logger = logging.getLogger(__name__)


class RedSysPayment(PaymentMethod):
    identifier = "redsys"
    label = "RedSys"
    payment_url = urljoin(settings.SITE_URL, f"payment/{identifier}/")
    ok_url = payment_url + "?status=ok&token={token}"
    ko_url = payment_url + "?status=ko"

    def __init__(self):
        self.client = RedirectClient(settings.REDSYS_SECRET_KEY)

    def get_urls(self) -> list:
        return [
            path("response/", self.response_view, name="redsys-response"),
        ]

    def basket_payment(self, basket: Basket, request) -> str:
        BasketStockReservation.create_and_reserve(basket)
        order = Order.objects.create_from_request(request)
        redsys_request = self.get_redsys_request(request, order, basket)
        return self.build_redsys_form_js(redsys_request)

    def order_payment(self, order: Order, request) -> str:
        redsys_request = self.get_redsys_request(request, order)
        return self.build_redsys_form_js(redsys_request)

    def get_redsys_data(
        self,
        request,
        order: Order,
        basket: Optional[Basket] = None,
    ) -> dict:
        """
        Returns parameters for RedSys request.
        """
        if basket:
            total = basket.total.quantize(Decimal(".01"), ROUND_HALF_UP)
            data = {
                "order_id": str(order.id),
                "basket_id": str(basket.id),
            }
        else:
            total = order.amount_outstanding.quantize(Decimal(".01"), ROUND_HALF_UP)
            data = {"order_id": str(order.id)}

        if not total > 0:
            raise PaymentError("Nothing to pay for")

        # A unique redsys reference is created for every order purchase attempt.
        redsys_ref = OrderRedsysReference.create_for_order(order)

        parameters = {
            "merchant_code": settings.REDSYS_MERCHANT_CODE,
            "terminal": settings.REDSYS_MERCHANT_TERMINAL,
            "transaction_type": constants.STANDARD_PAYMENT,
            "currency": constants.EUR,
            "order": str(redsys_ref),
            "amount": total,
            "merchant_data": json.dumps(data),
            "merchant_name": settings.REDSYS_MERCHANT_NAME,
            "titular": settings.SITE_NAME,
            "product_description": f"Pay for {order.ref}",
            "merchant_url": request.build_absolute_uri(
                reverse(f"{self.identifier}-response")
            ),
            "url_ok": self.ok_url.format(token=order.token),
            "url_ko": self.ko_url,
            "consumer_language": (
                constants.ENGLISH
                if get_language_from_request(request).lower() == "en"
                else constants.SPANISH
            ),
        }
        extra_parameters: dict[str, str] = {}
        return parameters, extra_parameters

    def get_redsys_request(
        self,
        request,
        order: Order,
        basket: Optional[Basket] = None,
    ) -> dict:
        """
        Return prepared request for RedSys
        """
        parameters, extra_parameters = self.get_redsys_data(request, order, basket)
        return self.client.prepare_request(parameters, extra_parameters)

    def build_redsys_form_js(self, redsys_request: dict) -> str:
        """
        Build JS data to be parsed and evaluated on frontend site.
        """
        js = (
            f'var form=document.createElement("form");'
            f'form.action="{settings.REDSYS_URL}";'
            f'form.method="POST";'
        )
        for k, v in redsys_request.items():
            js += (
                f'var field=document.createElement("input");'
                f'field.type="hidden";'
                f'field.name="{force_str(k)}";'
                f'field.value="{force_str(v)}";'
                f"form.appendChild(field);"
            )
        js += "document.body.appendChild(form);form.submit();"
        return json.dumps(js)

    @csrf_exempt
    def response_view(self, request):
        if request.method != "POST":
            return HttpResponseNotAllowed(["POST"])

        try:
            redsys_response = self.client.create_response(
                request.POST[SIGNATURE],
                request.POST[MERCHANT_PARAMETERS],
            )
            data = json.loads(html.unescape(redsys_response.merchant_data))
            logger.info(
                f"Received RedSys response "
                f"({redsys_response.code}, {redsys_response.message})"
                f"[{redsys_response.order}]"
            )
        except Exception as exc:
            logger.error(f"Invalid RedSys response ({exc})")
            return HttpResponseBadRequest("Invalid response")

        try:
            order_id = data["order_id"]
            order = Order.objects.get(id=order_id)
        except (KeyError, Order.DoesNotExist):
            logger.error(f"Missing order in RedSys response [{redsys_response.order}]")
            return HttpResponseBadRequest("Missing order")

        try:
            basket_id = data["basket_id"]
            basket = Basket.objects.get(id=basket_id)
        except KeyError:
            basket_id = None
            basket = None
        except Basket.DoesNotExist:
            logger.error(f"Missing basket in RedSys response [{redsys_response.order}]")
            return HttpResponseBadRequest("Missing basket")

        if redsys_response.is_paid:
            response = self.fulfill_order(order, request, redsys_response, basket)
        elif redsys_response.is_canceled:
            response = self.cancel_order(order, request, redsys_response, basket)
        elif redsys_response.is_refunded:
            response = self.refund_order(order, request, redsys_response, basket)
        else:
            response = self.fail_order(order, request, redsys_response, basket)

        return response

    @transaction.atomic
    def fulfill_order(self, order: Order, request, redsys_response, basket=None):
        Status = Order.get_statuses()

        if basket:
            request._payment = True
            basket.update(request)
            order.populate_from_basket(basket, request)
            try:
                logger.info(f"[{order.ref}] populated from basket: {basket.__dict__}")
            except Exception:
                pass
            try:
                basket.stock_reservation.commit()
            except BasketStockReservation.DoesNotExist:
                # In a case that reservation has previously expired, re-reserve items
                BasketStockReservation.reserve_and_commit(basket)
            ProductStats.record_basket(basket)
            basket.delete()
            # Create shipping only for basket checkout
            OrderShipping.create_for_order(order)
        else:
            logger.warning(f"Order not populated from basket [{order.ref}]")
            logger.warning(f"Request data: {request.POST}")

        try:
            order.pay(
                amount=redsys_response.amount,
                transaction_id=redsys_response.authorization_code,
                payment_method=self.identifier,
            )
            logger.info(f"Order fulfilled [{redsys_response.order}]")
        except IntegrityError:
            logger.warning(
                f"Order not fulfilled, authorization code already exists "
                f"[{redsys_response.order}, {redsys_response.authorization_code}]"
            )
            self.fail_order(order, request, redsys_response, basket)
            return HttpResponseBadRequest("Authorization code already exists")

        # Set status to Processing.
        if order.status != Status.PROCESSING:
            order.status = Status.PROCESSING
            order.save()

        return HttpResponse("OK")

    def cancel_order(self, order, request, redsys_response, basket=None):
        Status = Order.get_statuses()
        order.status = Status.CANCELLED
        order.save()
        if basket:
            try:
                basket.stock_reservation.unreserve_and_delete()
            except BasketStockReservation.DoesNotExist:
                pass
        logger.info(f"Order cancelled [{redsys_response.order}]")
        return HttpResponse("OK")

    def refund_order(self, order, request, redsys_response, basket=None):
        Status = Order.get_statuses()
        order.status = Status.REFUNDED
        order.save()
        logger.info(f"Order refunded [{redsys_response.order}]")
        return HttpResponse("OK")

    def fail_order(self, order, request, redsys_response, basket=None):
        Status = Order.get_statuses()
        order.status = Status.FAILED
        order.save()
        if basket:
            try:
                basket.stock_reservation.unreserve_and_delete()
            except BasketStockReservation.DoesNotExist:
                pass
        logger.info(f"Order failed [{redsys_response.order}]")
        return HttpResponse("OK")
