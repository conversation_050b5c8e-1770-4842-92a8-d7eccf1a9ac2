from django.core.exceptions import ValidationError
from django.http import HttpRequest
from django.utils.translation import gettext_lazy as _
from django.utils.translation import pgettext_lazy
from salesman.basket.models import Basket
from salesman.checkout.payment import PaymentMethod as _PaymentMethod
from ..modifiers import (
    AvailabilityModifier,
    ShippingAvailabilityModifier,
    ShippingRegionAvailabilityModifier,
    DiscountModifier,
    AutomaticShippingDiscountModifier,
)
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


class PaymentMethod(_PaymentMethod):
    def validate_basket(self, basket: Basket, request: HttpRequest):
        super().validate_basket(basket, request)

        # Original availability checks
        availability_key = AvailabilityModifier.identifier
        shipping_availability_key = ShippingAvailabilityModifier.identifier
        shipping_region_availability = ShippingRegionAvailabilityModifier.identifier

        for item in basket.get_items():
            if availability_key in item.extra:
                is_available = item.extra[availability_key][0]
                if not is_available:
                    msg = pgettext_lazy("Checkout", "Some products are not available.")
                    raise ValidationError(msg)
                del item.extra[availability_key]

            if shipping_availability_key in item.extra:
                is_available = item.extra[shipping_availability_key]
                if not is_available:
                    msg = pgettext_lazy(
                        "Checkout",
                        "Some products are not available for shipping outside Spain.",
                    )
                    raise ValidationError(msg)
                del item.extra[shipping_availability_key]

            if shipping_region_availability in item.extra:
                is_available = item.extra[shipping_region_availability]
                if not is_available:
                    msg = pgettext_lazy(
                        "Checkout",
                        "Some products are not available for shipping to your Region",
                    )
                    raise ValidationError(msg)
                del item.extra[shipping_region_availability]

        # Discount validation
        discount_key = DiscountModifier.identifier
        if discount_key in basket.extra:
            discount_code = basket.extra[discount_key]

            if not discount_code:
                return

            try:
                from ..models import Discount

                discount = Discount.objects.get(code__iexact=discount_code)

                # Check if code is being used in other active baskets
                active_baskets_key = f"active_discount_baskets:{discount_code}"
                active_baskets = cache.get(active_baskets_key, set())

                # Remove current basket from count
                active_baskets.discard(str(basket.id))

                # If max_uses would be exceeded by current active baskets
                if (
                    discount.max_uses
                    and discount.times_used + len(active_baskets) >= discount.max_uses
                ):
                    msg = pgettext_lazy(
                        "Checkout", "This discount code is no longer available."
                    )
                    raise ValidationError(msg)

                # Add this basket to active baskets
                active_baskets.add(str(basket.id))
                cache.set(
                    active_baskets_key, active_baskets, timeout=1800  # 30 minutes
                )

            except Discount.DoesNotExist:
                msg = pgettext_lazy("Checkout", "Invalid discount code.")
                raise ValidationError(msg)
            except Exception as e:
                logger.error(f"Error validating discount: {e}")
                msg = pgettext_lazy("Checkout", "Error validating discount code.")
                raise ValidationError(msg)

    def basket_payment(self, basket: Basket, request: HttpRequest) -> str:
        """
        Override to cleanup discount usage tracking after successful payment
        """
        try:
            # Clear discount usage for this basket
            discount_key = DiscountModifier.identifier
            if discount_key in basket.extra:
                discount_code = basket.extra[discount_key]
                active_baskets_key = f"active_discount_baskets:{discount_code}"
                active_baskets = cache.get(active_baskets_key, set())
                active_baskets.discard(str(basket.id))
                if active_baskets:
                    cache.set(active_baskets_key, active_baskets, timeout=1800)
                else:
                    cache.delete(active_baskets_key)
        except Exception as e:
            logger.error(f"Error cleaning up discount usage: {e}")
            return

        return super().basket_payment(basket, request)
