from decimal import Decimal
from typing import Optional

from django.conf import settings
from django.urls import NoReverseMatch, reverse
from django.utils.safestring import mark_safe
from djmoney.contrib.exchange.models import MissingRate, get_rate

from user.models import User

from .utils import clean_currency, get_currency_from_request


def price_format(value: Decimal, context: dict) -> str:
    request = context.get("request", None)
    order = context.get("order", None)
    currency: Optional[str] = None
    value = Decimal(value)

    if request:
        currency = get_currency_from_request(request)

    if context.get("admin", False):
        currency = settings.BASE_CURRENCY

    if not currency and order and "currency" in order.extra:
        currency = order.extra["currency"].upper()

    currency = clean_currency(currency)

    # Calculate rate for non-base currency
    if currency != settings.BASE_CURRENCY:
        try:
            if request and hasattr(request, "currency_rate"):
                rate = request.currency_rate
            else:
                rate = get_rate(settings.BASE_CURRENCY, currency)
                if request:
                    request.currency_rate = rate
            value = Decimal(value * Decimal(rate))
        except MissingRate:
            currency = settings.BASE_CURRENCY

    symbol = settings.CURRENCY_SYMBOLS[currency]
    return f"{value:,.2f} {symbol}"


def admin_customer_format(user: User, context: dict) -> str:
    if context.get("wagtail", False):
        url_name = "user_user_modeladmin_edit"
    else:
        url_name = "admin:auth_user_change"
    try:
        url = reverse(url_name, args=[user.pk])
        return mark_safe(f'<a href="{url}">{user}</a>')
    except NoReverseMatch:
        return str(user)
