from django.conf import settings
from django.utils.formats import date_format
from rest_framework import serializers
from salesman.orders.serializers import OrderSerializer


class OrderSummarySerializer(OrderSerializer):
    class Meta:
        model = OrderSerializer.Meta.model
        fields = [
            "id",
            "url",
            "ref",
            "token",
            "status",
            "status_display",
            "date_created",
            "date_updated",
            "subtotal",
            "total",
        ]


class OrderEmailSerializer(OrderSerializer):
    """
    Serializer for order accessible in email notifications.
    """

    date_created_display = serializers.SerializerMethodField()
    tracking_url = serializers.SerializerMethodField()

    class Meta(OrderSerializer.Meta):
        fields = OrderSerializer.Meta.fields + [
            "date_created_display",
            "tracking_url",
        ]

    def get_date_created_display(self, obj):
        return date_format(obj.date_created, "DATETIME_FORMAT")

    def get_tracking_url(self, obj):
        return settings.SITE_URL
