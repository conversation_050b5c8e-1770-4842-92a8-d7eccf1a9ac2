from __future__ import annotations

from django.utils.translation import pgettext_lazy
from rest_framework import serializers

from country.data import CANADA_PROVINCES, ITALY_PROVINCES, US_STATES
from country.utils import get_country_choices


class AddressSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    address = serializers.CharField()
    address_2 = serializers.CharField(required=False, allow_blank=True)
    postal_code = serializers.CharField()
    city = serializers.CharField()
    country = serializers.CharField()
    state = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    phone = serializers.CharField(required=False, allow_blank=True)
    rfc = serializers.CharField(
        required=False, allow_blank=True, help_text="RFC for Mexican addresses"
    )

    def validate(self, attrs):
        country = attrs.get("country", "")
        state = attrs.get("state", "")
        rfc = attrs.get("rfc", "")

        if country not in [x[0] for x in get_country_choices(shipping=False)]:
            raise serializers.ValidationError(
                {"country": pgettext_lazy("Country", "Invalid country provided.")}
            )

        # Validate RFC for Mexico
        if country == "MX" and rfc:
            # Basic RFC validation - should be 12-13 characters
            if len(rfc) < 12 or len(rfc) > 13:
                raise serializers.ValidationError(
                    {
                        "rfc": pgettext_lazy(
                            "Country", "RFC must be 12-13 characters long."
                        )
                    }
                )
            # RFC should be alphanumeric
            if not rfc.replace(" ", "").isalnum():
                raise serializers.ValidationError(
                    {
                        "rfc": pgettext_lazy(
                            "Country", "RFC must contain only letters and numbers."
                        )
                    }
                )

        # validate USA
        if country == "US":
            if state not in US_STATES:
                raise serializers.ValidationError(
                    {"state": pgettext_lazy("Country", "Invalid US state provided.")}
                )
            return attrs

        # Validate state/province for CANADA and ITALY
        if country == "CA" and state not in CANADA_PROVINCES:
            raise serializers.ValidationError(
                {"state": pgettext_lazy("Country", "Invalid Canada province provided.")}
            )
        elif country == "IT" and state not in ITALY_PROVINCES:
            raise serializers.ValidationError(
                {"state": pgettext_lazy("Country", "Invalid Italy province provided.")}
            )
        elif country == "US" and state not in US_STATES:
            raise serializers.ValidationError(
                {"state": pgettext_lazy("Country", "Invalid US state provided.")}
            )
        else:
            attrs["state"] = None

        return attrs

    def to_text(self):
        return "\n".join([x or "-" for x in self.validated_data.values()])

    @classmethod
    def from_text(cls, text: str) -> AddressSerializer:
        lines = tuple(x.strip() for x in text.split("\n"))
        count = len(lines)
        if count > 10 or count < 8:
            msg = (
                "Address must contain a min of 8 and a max of 10 lines: first_name, last_name, "
                "address, address_2, postal_code, city, country, state, phone, rfc."
            )
            raise serializers.ValidationError(msg)

        # Handle different line counts by adding missing fields
        if count == 8:
            # No address_2, no rfc
            lines = lines[:3] + ("",) + lines[3:] + ("",)  # add empty address_2 and rfc
        elif count == 9:
            # Could be missing address_2 or rfc, need to determine which
            # If the last line looks like an RFC (12-13 chars, alphanumeric), treat it as RFC
            last_line = lines[8].replace(" ", "")
            if len(last_line) >= 12 and len(last_line) <= 13 and last_line.isalnum():
                # Last line is RFC, missing address_2
                lines = lines[:3] + ("",) + lines[3:]  # add empty address_2
            else:
                # Last line is phone, missing RFC
                lines = lines + ("",)  # add empty rfc

        data = {
            "first_name": lines[0],
            "last_name": lines[1],
            "address": lines[2],
            "address_2": lines[3],
            "postal_code": lines[4],
            "city": lines[5],
            "country": lines[6],
            "state": lines[7],
            "phone": lines[8],
            "rfc": lines[9],
        }

        return cls(data=data)
