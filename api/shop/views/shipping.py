import enum
import hmac
import json
import logging
from sentry_sdk import capture_message

from django.conf import settings
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from salesman.orders.models import Order

from shop.shipping.sendcloud import Client

logger = logging.getLogger(__name__)


class SendcloudParcelStatus(enum.Enum):
    READY_TO_SEND = 1000
    DELIVERED = 11


@api_view(["GET"])
def parcel_statuses_view(request):
    """
    Debug view that displays available parcel statuses.
    """
    data = Client().get_parcel_statuses()
    return Response(data)


@api_view(["POST"])
def sendcloud_webhook_view(request):
    """
    View accessed from Sendcloud on different events.
    """
    sendcloud_signature = request.headers.get("Sendcloud-Signature", None)
    if not sendcloud_signature:
        return Response("Invalid request", status=status.HTTP_400_BAD_REQUEST)

    message = json.dumps(request.data)
    signature = hmac.new(
        key=settings.SENDCLOUD_API_SECRET.encode("utf-8"),
        msg=message.encode("utf-8"),
        digestmod="sha256",
    ).hexdigest()
    
    if signature != sendcloud_signature:
        error_message = f"Invalid Sendcloud signature ({sendcloud_signature})"
        logger.error(error_message)
        capture_message(error_message, level="error")


    logger.info(f"Signature calculated: {signature}, Signature received: {sendcloud_signature}")

    try:
        logger.info(f"Received Sendcloud webhook ({request.data['action']})")
    except KeyError:
        logger.error("Invalid Sendcloud request data received")
        return Response("Invalid request", status=status.HTTP_412_PRECONDITION_FAILED)

    try:
        assert request.data["action"] == "parcel_status_changed"
        parcel_status = SendcloudParcelStatus(request.data["parcel"]["status"]["id"])
        ref = request.data["parcel"]["order_number"]
        order = Order.objects.get(ref=ref)
    except AssertionError:
        return Response("Request action ignored")
    except ValueError:
        return Response("Status change ignored")
    except Order.DoesNotExist:
        logger.warning(
            f"Sendcloud webhook status change skipped, can't find order ({ref})"
        )
        return Response("Order not found, status change skipped")

    if parcel_status == SendcloudParcelStatus.READY_TO_SEND:
        order.status = order.statuses.SHIPPED
        msg = "Order status changed to SHIPPED."
    elif parcel_status == SendcloudParcelStatus.DELIVERED:
        order.status = order.statuses.COMPLETED
        msg = "Order status changed to COMPLETED."

    tracking_url = request.data["parcel"]["tracking_url"]
    if tracking_url:
        order.extra["tracking_url"] = tracking_url
    tracking_number = request.data["parcel"]["tracking_number"]
    if tracking_number:
        order.extra["tracking_number"] = tracking_number

    order.save()
    logger.info(
        f"Sendcloud webhook triggered order status change"
        f"({order.ref}, {order.status_display})"
    )
    return Response(msg)
