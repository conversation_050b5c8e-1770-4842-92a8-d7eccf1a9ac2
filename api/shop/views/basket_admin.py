import csv
import io
from datetime import datetime

from django.http import HttpResponse
from django.utils import translation
from django.utils.translation import pgettext_lazy
from salesman.basket.models import Basket
from wagtail.admin import messages
from wagtail.contrib.modeladmin.views import (
    C<PERSON>View,
    InstanceSpecificView,
    WMABaseView,
)

from shop.formatters import price_format


class BasketReportView(WMABaseView):
    template_name = "shop/basket_report.html"
    meta_title = "Generate report"
    page_title = "Generate report"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

    def post(self, request, *args, **kwargs):
        translation.activate(request.LANGUAGE_CODE)
        date_from_raw = request.POST.get("datetime_from")
        date_to_raw = request.POST.get("datetime_to")

        try:
            date_from = datetime.strptime(date_from_raw, "%Y-%m-%d %H:%M")
        except ValueError:
            messages.error(request, "Missing Date from.")
            return self.get(request, *args, **kwargs)
        try:
            date_to = datetime.strptime(date_to_raw, "%Y-%m-%d %H:%M")
        except ValueError:
            date_to = None

        qs = Basket.objects.prefetch_related("owner")
        qs = qs.filter(date_created__gte=date_from)
        if date_to:
            qs = qs.filter(date_created__lte=date_to)

        # Add filters
        has_owner = request.POST.get("has_owner")
        if has_owner and has_owner == "1":
            qs = qs.filter(owner__isnull=False)

        baskets = list(qs)
        output = render_basket_csv(request, baskets)
        response = HttpResponse(output, content_type="application/csv")

        # Generate CSV name from dates.
        csv_name = f"report_{date_from.strftime('%Y-%m-%dT%H-%M')}"
        if date_to:
            csv_name += f"_{date_to.strftime('%Y-%m-%dT%H-%M')}"
        csv_name += ".csv"
        response["Content-Disposition"] = f'attachment; filename="{csv_name}"'
        return response


def render_basket_csv(request, baskets: list[Basket]) -> str:
    """
    Render a list of baskets as a CSV file.
    """
    stream = io.StringIO()
    writer = csv.writer(stream)
    headers = [
        "Basket ID",
        "Owner",
        "Date created",
        "Quantity",
        "Name",
        "SKU",
        "Unit price",
        "Subtotal",
        "Total",
    ]
    writer.writerow(headers)

    context = {"request": request}

    for basket in baskets:
        basket.update(request)
        item = basket.get_items()
        for i in item:
            i.update(request)
            writer.writerow(
                [
                    basket.id,
                    basket.owner.email if basket.owner else "",
                    basket.date_created,
                    i.quantity,
                    i.name,
                    i.code,
                    price_format(i.unit_price, context),
                    basket.subtotal,
                    basket.total,
                ]
            )
    return stream.getvalue()
