import logging
from datetime import datetime, timedelta

from django.conf import settings
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.cache import never_cache
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from catalog.models.product import Product

logger = logging.getLogger(__name__)

@never_cache
@api_view(["POST"])
@permission_classes([AllowAny])
def validate_product_access_code(request):
    """
        Validate Product Access Code
    """
    data = request.data
    code = data.get('code', None)
    product_id = data.get('product_id', None)

    if not code: 
        
        return Response("A data code Needed", 400)
    
    if not product_id:
        return Response("Product Required", 400)
    
    product = Product.objects.filter(
        id=product_id,
        access_code__iexact=code
    ).first()

    if not product:
        return Response("Invalid Access Code", 400)
    
    return Response("ok", 200)

