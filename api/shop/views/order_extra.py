import logging
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Optional, Tuple

from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.paginator import Paginator
from django.db import transaction
from django.http import HttpRequest, JsonResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.cache import never_cache
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from salesman.basket.models import Basket, BasketItem
from salesman.basket.serializers import ExtraRowsField
from salesman.orders.models import Order
from sentry_sdk import capture_exception, capture_message, set_context, set_tag

from catalog.models.product import ProductVariant
from shop.models.shipping import OrderShipping
from shop.templatetags.shop_tags import get_order_info

logger = logging.getLogger(__name__)

from decimal import Decimal
from typing import Optional, Tuple

from django.apps import apps
from django.contrib.auth.models import AnonymousUser
from django.db import transaction
from django.http import HttpRequest


class DummyRequest:
    """A minimal request object for basket operations when no request is provided."""
    def __init__(self):
        self.user = AnonymousUser()
        self.session = {}
        self.META = {'REMOTE_ADDR': '0.0.0.0'}
        self.COOKIES = {}
        self.GET = {}
        self.POST = {}
        self.method = 'GET'
        self._messages = []
        # Simple dictionary for headers
        self.headers = {
            'host': 'localhost',
            'user-agent': 'DummyRequest/1.0',
            'accept': '*/*'
        }
        
    def get_host(self):
        return 'localhost'
        
    def is_secure(self):
        return False
    

def reconstruct_basket_from_order(
    order: Order, request: Optional[HttpRequest] = None
) -> Tuple[Basket, list]:
    """
    Reconstructs a basket from an order, attempting to recreate all items
    and their states at the time of order creation.

    Args:
        order (Order): The order to reconstruct from
        request (Optional[HttpRequest]): The current request. If None, a dummy request will be used.

    Returns:
        Tuple[Basket, list]: A tuple containing:
            - The reconstructed basket
            - List of any errors encountered during reconstruction
    """
    # Use provided request or create a dummy one
    effective_request = request if request is not None else DummyRequest()

    with transaction.atomic():
        # Create a temporary basket instance
        basket = Basket.objects.create()
        basket.request = effective_request
        errors = []

        try:
            # Copy order-level extra data
            basket.extra = order.extra.copy() if order.extra else {}
            basket.extra.update(
                {
                    "email": order.email,
                    "shipping_address": order.shipping_address,
                    "billing_address": order.billing_address,
                }
            )

            # Reconstruct each order item
            for order_item in order.items.all():
                try:
                    # Parse the product type and try to get the actual product
                    app_label, model_name = order_item.product_type.split(".")
                    model = apps.get_model(app_label, model_name)
                    product = None

                    # Try to get the original product if it still exists
                    if order_item.product_id:
                        try:
                            product = model.objects.get(id=order_item.product_id)
                        except model.DoesNotExist:
                            errors.append(
                                f"Original product {order_item.product_type} (id={order_item.product_id}) no longer exists"
                            )

                    # If we can't get the original product, create a dummy product for calculation
                    if not product:
                        product = create_dummy_product_from_data(
                            order_item.product_data, model, order_item.product_id
                        )

                    # Use basket.add() instead of direct BasketItem creation
                    basket.add(
                        product=product,
                        quantity=order_item.quantity,
                        ref=order_item.ref if hasattr(order_item, "ref") else None,
                        extra=order_item.extra.copy() if order_item.extra else {},
                    )

                    # If using a dummy product, update the price to match the original order
                    if hasattr(product, "_dummy_price"):
                        basket_item = basket.items.get(product_id=product.id)
                        basket_item.unit_price = order_item.unit_price
                        basket_item.save()

                except Exception as e:
                    errors.append(
                        f"Error reconstructing item {order_item.product_type} (id={order_item.product_id}): {str(e)}"
                    )

            # Update basket to recalculate all modifiers
            basket.update(effective_request)

            # Get the reconstructed basket data
            result = basket, errors

            # Roll back the transaction to prevent persistence
            transaction.set_rollback(True)

            return result

        except Exception as e:
            transaction.set_rollback(True)
            raise e


def create_dummy_product_from_data(
    product_data: dict, model_class: type, product_id: int
) -> object:
    """
    Creates a dummy product instance from stored order data when original product
    no longer exists or is inaccessible.

    Args:
        product_data (dict): The stored product data from the order
        model_class (type): The original product model class
        product_id (int): The original product ID

    Returns:
        object: A dummy product instance with minimal required attributes
    """

    class DummyProduct:
        def __init__(self, data, model_class, product_id):
            self._meta = model_class._meta
            self.id = product_id
            self.name = data.get("name", "(Unavailable Product)")
            self.code = data.get("code", f"UNAVAILABLE-{product_id}")
            self._dummy_price = Decimal(str(data.get("price", "0")))

        def get_price(self, request):
            return self._dummy_price

    return DummyProduct(product_data, model_class, product_id)


@api_view(["POST"])
@permission_classes([AllowAny])
def recreate_order(request):
    data = request.data
    order_id = data.get("order_id")

    if not order_id:
        return Response("Invalid", status=status.HTTP_400_BAD_REQUEST)

    order = Order.objects.get(ref=order_id)

    if not order:
        return Response("Invalid", status=status.HTTP_404_NOT_FOUND)

    try:
        order_info = get_order_info(order)
        print(order_info)
        basket, errors = reconstruct_basket_from_order(order, request)
        basket.update(request)

        extra_rows = ExtraRowsField().to_representation(basket.extra_rows)
        # print(extra_rows)
        # print(errors)
        return Response("OK", status=status.HTTP_200_OK)

    except Exception as e:
        print(e)
        return Response(
            "Internal server error", status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
