import csv
import io
from datetime import datetime
from django.utils import timezone

import pdfkit
from django import forms
from django.db import models
from django.http import Http404, HttpResponse
from django.shortcuts import redirect
from django.template.loader import render_to_string
from django.utils import translation
from django.utils.formats import date_format
from django.utils.translation import pgettext_lazy
from salesman.orders.models import Order
from salesman.orders.serializers import OrderSerializer
from wagtail.admin import messages
from wagtail.admin.edit_handlers import ObjectList
from wagtail.admin.forms.models import WagtailAdminModelForm
from wagtail.contrib.modeladmin.views import (
    CreateView,
    InstanceSpecificView,
    WMABaseView,
)

from notification.models import get_context_and_notify_status_changed
from shop.formatters import price_format
from shop.models.order import OrderInternalReference
from shop.models.order_create import OrderCreate
from shop.parsers import parse_address_or_none
from shop.signals import get_order_cancellation_summary
from shop.templatetags.shop_tags import get_company_address, get_order_info


class OrderCreateForm(WagtailAdminModelForm):
    _extra = forms.JSONField(required=False, widget=forms.HiddenInput())

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["_extra"].initial = self.instance._extra

    def save(self, commit=True):
        obj = super().save(commit=False)
        obj._extra = self.cleaned_data["_extra"]
        order = obj.to_order()
        order.save()
        return order


class OrderCreateView(CreateView):
    def __init__(self, model_admin):
        super().__init__(model_admin)
        self.model = OrderCreate

    def get_edit_handler(self):
        edit_handler = ObjectList(self.model.panels, base_form_class=OrderCreateForm)
        return edit_handler.bind_to(
            model=self.model,
            request=self.request,
            instance=self.get_instance(),
        )

    def get_instance(self):
        return self.get_from_order() or super().get_instance()

    def get_from_order(self):
        try:
            from_order = self.request.GET["from_order"]
            order = Order.objects.get(ref=from_order)
            return OrderCreate.from_order(order)
        except (KeyError, Order.DoesNotExist):
            return None

    def get_success_url(self):
        return self.url_helper.get_action_url("edit", instance_pk=self.instance.id)


def render_order_pdf(request, order: Order):
    """
    Renders order to PDF and returns output.
    """
    serializer = OrderSerializer(order, context={"request": request})
    context = {"order": order, "order_data": serializer.data}

    with translation.override(request.LANGUAGE_CODE):
        html = render_to_string("shop/order_print.html", context, request)

    options = {
        "page-size": "Letter",
        "dpi": 400,
        "margin-top": "0.45in",
        "margin-right": "0.45in",
        "margin-bottom": "0.45in",
        "margin-left": "0.45in",
        "encoding": "UTF-8",
    }
    return pdfkit.from_string(html, False, options=options)


def render_orders_csv(request, orders: list[Order], write_headers: bool = True) -> str:
    """
    Renders orders into CSV for export with memory efficiency and proper variant handling.
    """
    from catalog.models import ProductVariant
    from django.db import connection

    stream = io.StringIO()
    writer = csv.writer(stream)

    if write_headers:
        headers = [
            pgettext_lazy("Invoice", "Order ID"),
            pgettext_lazy("Invoice", "Internal reference"),
            pgettext_lazy("Invoice", "Issue date"),
            pgettext_lazy("Invoice", "Supplier"),
            pgettext_lazy("Invoice", "Full name"),
            pgettext_lazy("Invoice", "Address"),
            pgettext_lazy("Invoice", "Postal code"),
            pgettext_lazy("Invoice", "City"),
            pgettext_lazy("Invoice", "State"),
            pgettext_lazy("Invoice", "Country Code"),
            pgettext_lazy("Invoice", "Phone number"),
            pgettext_lazy("Invoice", "Email address"),
            pgettext_lazy("Invoice", "Quantity"),
            pgettext_lazy("Invoice", "Article"),
            pgettext_lazy("Invoice", "SKU"),
            pgettext_lazy("Invoice", "EAN-13"),
            pgettext_lazy("Invoice", "Unit price"),
            pgettext_lazy("Invoice", "Subtotal"),
            pgettext_lazy("Invoice", "Tax"),
            pgettext_lazy("Invoice", "Shipping"),
            pgettext_lazy("Invoice", "Discount Applied"),
            pgettext_lazy("Invoice", "Auto Shipping Discount"),
            pgettext_lazy("Invoice", "Total"),
            pgettext_lazy("Invoice", "Transaction ID"),
            pgettext_lazy("Invoice", "Payment Method"),
            pgettext_lazy("Invoice", "Discount Code Used"),
            pgettext_lazy("Invoice", "Auto Discount Type"),
        ]
        writer.writerow(headers)

    company_address = get_company_address()

    # Get all unique SKUs first
    all_skus = set()
    for order in orders:
        for item in order.items.all():
            if hasattr(item, "code"):
                all_skus.add(item.code)

    # Get variants without using select_related
    variants = ProductVariant.objects.filter(code__in=all_skus)
    sku_to_variant = {variant.code: variant for variant in variants}

    # Clear query log after fetching variants
    connection.queries_log.clear()

    # Process each order
    for order in orders:
        try:
            order_info = get_order_info(order)
            context = {"order": order, "request": request}
            payment = order.payments.last()  # Using last() instead of all().last()

            shipping = parse_address_or_none(order.shipping_address) or {}
            try:
                name = "{first_name} {last_name}".format(**shipping)
            except KeyError:
                name = ""

            try:
                internal_ref = order.internal_ref.ref
            except OrderInternalReference.DoesNotExist:
                internal_ref = "-"

            # Process items
            for count, item in enumerate(order_info["items"], 1):
                shipping_amount = (
                    order_info["shipping"]["amount"] if order_info["shipping"] else "-"
                )
                tax_amount = order_info["tax"]["amount"] if order_info["tax"] else "-"

                # Handle manual discount codes
                try:
                    discount_amount = (
                        order_info["discount"]["amount"]
                        if order_info["discount"]
                        else "-"
                    )
                    discount_code_used = order_info["discount"]["extra"][
                        "discount_code"
                    ]
                except (KeyError, TypeError):
                    discount_code_used = "-"
                    discount_amount = "-"

                # Handle automatic shipping discounts
                try:
                    auto_shipping_discount_amount = (
                        order_info["auto_shipping_discount"]["amount"]
                        if order_info["auto_shipping_discount"]
                        else "-"
                    )
                    auto_discount_type = (
                        order_info["auto_shipping_discount"]["extra"].get(
                            "auto_discount_id", "-"
                        )
                        if order_info["auto_shipping_discount"]
                        else "-"
                    )
                except (KeyError, TypeError):
                    auto_shipping_discount_amount = "-"
                    auto_discount_type = "-"

                # Get variant info safely
                variant = sku_to_variant.get(item.code)
                ean13 = (
                    variant.barcode if variant and hasattr(variant, "barcode") else "-"
                )

                if count == 1:
                    cols = [
                        order.ref,
                        internal_ref,
                        date_format(order.date_created, format="DATETIME_FORMAT"),
                        company_address,
                        name,
                        shipping.get("address", ""),
                        shipping.get("postal_code", ""),
                        shipping.get("city", ""),
                        shipping.get("state", ""),
                        shipping.get("country", ""),
                        shipping.get("phone", ""),
                        order.email,
                        item.quantity,
                        item.name,
                        item.code,
                        ean13,
                        price_format(item.unit_price, context),
                        price_format(order.subtotal, context),
                        tax_amount,
                        shipping_amount,
                        discount_amount,
                        auto_shipping_discount_amount,
                        price_format(order.total, context),
                        payment.transaction_id if payment else "",
                        payment.payment_method if payment else "",
                        discount_code_used,
                        auto_discount_type,
                    ]
                else:
                    cols = [
                        order.ref,
                        internal_ref,
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        item.quantity,
                        item.name,
                        item.code,
                        ean13,
                        price_format(item.unit_price, context),
                        "",
                        "",
                        "",
                        "",
                        "",  # auto_shipping_discount_amount
                        "",
                        "",
                        "",
                        "",
                        "",  # auto_discount_type
                    ]
                writer.writerow(cols)

            # Clear per-order cache
            del order_info
            connection.queries_log.clear()

        except Exception as e:
            print(f"Error processing order {order.ref}: {str(e)}")
            continue

    return stream.getvalue()


def get_order_filename(order: Order, extension: str = "pdf") -> str:
    timestamp = order.date_created.strftime("%Y-%m-%dT%H-%M-%S")
    return f"order__{timestamp}__{order.ref}.{extension}"


class OrderPrintView(InstanceSpecificView):
    template_name = "shop/order_print.html"

    def get(self, request, *args, **kwargs):
        pdf = render_order_pdf(request, self.instance)
        response = HttpResponse(pdf, content_type="application/pdf")
        filename = get_order_filename(self.instance, extension="pdf")
        response["Content-Disposition"] = f'filename="{filename}"'
        return response


class OrderDispatchTriggerView(InstanceSpecificView):
    template_name = "shop/order_dispatch_trigger.html"

    def get_orders_status_from_request_or_404(self, request):
        status = request.GET.get("status", "")
        try:
            return Order.get_statuses()[status]
        except KeyError:
            raise Http404

    def get(self, request, *args, **kwargs):
        self.get_orders_status_from_request_or_404(request)
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self.get_orders_status_from_request_or_404(request)
        status = request.GET.get("status", "")
        msg = f"Order status change to '{status}' was successfully dispatched."
        get_context_and_notify_status_changed(self.instance, status)
        messages.success(request, msg)
        return redirect(self.edit_url)


class OrderCancellationPreviewView(InstanceSpecificView):
    """View to preview what would happen if an order is cancelled/refunded"""

    template_name = "shop/order_cancellation_preview.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get cancellation summary
        summary = get_order_cancellation_summary(self.instance)
        context["cancellation_summary"] = summary

        # Add order status info
        context["can_be_cancelled"] = self.instance.status in [
            "PROCESSING",
            "SHIPPED",
            "COMPLETED",
        ]
        context["current_status"] = self.instance.status

        return context


class OrderReportView(WMABaseView):
    template_name = "shop/order_report.html"
    meta_title = "Generate report"
    page_title = "Generate report"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["order_status_choices"] = Order.get_statuses().choices
        return context

    def post(self, request, *args, **kwargs):
        translation.activate(request.LANGUAGE_CODE)

        date_from_raw = request.POST.get("datetime_from")
        date_to_raw = request.POST.get("datetime_to")

        try:
            # Make datetime timezone-aware
            date_from = timezone.make_aware(
                datetime.strptime(date_from_raw, "%Y-%m-%d %H:%M")
            )
        except ValueError:
            messages.error(request, "Missing Date from.")
            return self.get(request, *args, **kwargs)

        try:
            date_to = (
                timezone.make_aware(datetime.strptime(date_to_raw, "%Y-%m-%d %H:%M"))
                if date_to_raw
                else None
            )
        except ValueError:
            date_to = None

        # Get orders
        qs = Order.objects.exclude(status=Order.get_statuses().NEW).prefetch_related(
            "items", "payments"
        )

        # Apply date filters
        qs = qs.filter(date_created__gte=date_from)
        if date_to:
            qs = qs.filter(date_created__lte=date_to)

        # Apply status filter
        status = request.POST.get("status")
        if status:
            qs = qs.filter(status=status)

        # Apply payment status filter
        is_paid = request.POST.get("is_paid")
        if is_paid == "1":
            # Get IDs of paid orders
            paid_ids = [order.id for order in qs if order.is_paid]
            qs = qs.filter(id__in=paid_ids)
        elif is_paid == "0":
            # Get IDs of unpaid orders
            unpaid_ids = [order.id for order in qs if not order.is_paid]
            qs = qs.filter(id__in=unpaid_ids)

        # Apply shipping destination filter
        shipping_destination = request.POST.get("shipping_destination")
        if shipping_destination == "ES":
            # Get IDs of Spanish orders
            spain_ids = [
                order.id for order in qs if order.extra.get("country", None) == "ES"
            ]
            qs = qs.filter(id__in=spain_ids)
        elif shipping_destination == "INTL":
            # Get IDs of international orders
            intl_ids = [
                order.id for order in qs if order.extra.get("country", None) != "ES"
            ]
            qs = qs.filter(id__in=intl_ids)

        # Process in memory-efficient chunks
        chunk_size = 100
        output = io.StringIO()

        # Process orders in chunks
        total_orders = qs.count()
        for i in range(0, total_orders, chunk_size):
            chunk = list(qs[i : i + chunk_size])
            if i == 0:  # First chunk, include headers
                chunk_output = render_orders_csv(request, chunk)
            else:  # Subsequent chunks, skip headers
                chunk_output = render_orders_csv(request, chunk, write_headers=False)
            output.write(chunk_output)

        # Create response
        response = HttpResponse(output.getvalue(), content_type="application/csv")

        # Generate descriptive filename
        filename_parts = [f"report_{date_from.strftime('%Y-%m-%dT%H-%M')}"]
        if date_to:
            filename_parts.append(date_to.strftime("%Y-%m-%dT%H-%M"))
        if status:
            filename_parts.append(f"status_{status}")
        if is_paid:
            filename_parts.append("paid" if is_paid == "1" else "unpaid")
        if shipping_destination:
            filename_parts.append(shipping_destination)

        csv_name = "_".join(filename_parts) + ".csv"

        response["Content-Disposition"] = f'attachment; filename="{csv_name}"'
        return response
