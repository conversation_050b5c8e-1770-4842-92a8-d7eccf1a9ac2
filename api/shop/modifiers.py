from decimal import Decimal

import bleach
from django.core.exceptions import ValidationError
from django.http import HttpRequest
from django.utils.translation import gettext_lazy as _
from django.utils.translation import pgettext
from salesman.basket.models import Basket, BasketItem
from salesman.basket.modifiers import BasketModifier
from salesman.basket.serializers import ExtraRowSerializer
from salesman.conf import app_settings as salesman_settings

from shop.discount.validators import DiscountValidator

from .models import Discount, ShippingMethod
from .models.discount import AutomaticShippingDiscount
from .utils import clean_product_for_purchase, get_cleaned_currency_from_request


class BasketModifierWithRequest(BasketModifier):
    def add_extra_row(self, request, obj, label, amount, extra=None, charge=True):
        """
        Override add_extra_row for modifier that takes `request` into account.
        """
        extra = extra or {}
        instance = dict(label=label, amount=amount, extra=extra)
        context = {"request": request}
        obj.extra_rows[self.identifier] = ExtraRowSerializer(instance, context=context)
        if charge:
            obj.total += Decimal(amount)


class AvailabilityModifier(BasketModifierWithRequest):
    identifier = "availability"

    def process_item(self, item, request) -> None:
        if getattr(request, "_payment", None):
            # Skip modifier if basket update called on return from payment gateway.
            return

        try:
            clean_product_for_purchase(request, item.product, item.quantity)
            is_available = True
            msg = pgettext("Basket", "This product is available.")
        except ValidationError as exc:
            is_available = False
            if exc.code == "unavailable":
                available_quantity = exc.params["quantity"]
                if available_quantity == 0:
                    msg = pgettext("Basket", "This product is no longer available.")
                else:
                    msg = (
                        pgettext("Basket", "Only %d of this item is available.")
                        % available_quantity
                    )
            else:
                msg = exc.messages[0]

        item.extra[self.identifier] = is_available, msg


class CurrencyModifier(BasketModifierWithRequest):
    """
    Add currency to basket extra.
    """

    identifier = "currency"

    def process_basket(self, basket: Basket, request):
        basket.extra[self.identifier] = get_cleaned_currency_from_request(request)


class WeightModifier(BasketModifierWithRequest):
    identifier = "weight"

    def process_basket(self, basket: Basket, request):
        weight = self.get_weight_for_basket(basket)
        basket.extra[self.identifier] = f"{weight:.3f}"

    def process_item(self, item: BasketItem, request):
        weight = Decimal(item.product.get_weight() * item.quantity)
        item.extra[self.identifier] = f"{weight:.3f}"

    @classmethod
    def get_weight_for_basket(cls, basket: Basket) -> Decimal:
        if not hasattr(basket, "_weight"):
            basket._weight = Decimal(
                sum([x.product.get_weight() * x.quantity for x in basket.get_items()])
            )
        return basket._weight


class ShippingModifier(BasketModifierWithRequest):
    identifier = "shipping"
    country_key = "country"
    postal_code_key = "postal_code"

    def process_basket(self, basket: Basket, request):
        if self.country_key not in basket.extra:
            return

        country = basket.extra[self.country_key]
        postal_code = basket.extra.get(self.postal_code_key, None)
        shipping_method = ShippingMethod.find_for_country(country, postal_code)
        if not shipping_method:
            return

        # Cache shipping method on request to be accessed from other modifiers
        setattr(request, self.identifier, shipping_method)

        weight = WeightModifier.get_weight_for_basket(basket)
        shipping_method_price = shipping_method.get_best_price_for_weight(weight)

        self.add_extra_row(
            request,
            basket,
            label=shipping_method.name,
            amount=shipping_method_price.price,
            extra={
                "caption": shipping_method.caption,
                "price": repr(shipping_method_price.price),
                "country": country,
            },
        )


class ShippingAvailabilityModifier(BasketModifierWithRequest):
    """
    Checks if products are available for give shipping country.
    """

    identifier = "shipping_availability"
    country_key = "country"
    local_country = "ES"

    def process_basket(self, basket: Basket, request: HttpRequest) -> None:
        shipping_method = getattr(request, ShippingModifier.identifier, None)
        if shipping_method:
            is_local = "ES" in shipping_method.countries
        else:
            is_local = False

        for item in basket.get_items():
            if is_local:
                item.extra[self.identifier] = True
            else:
                product_data = item.product.get_localized_data("product_data")
                local_shipping_only = bool(
                    product_data.get("local_shipping_only", False)
                )
                item.extra[self.identifier] = not local_shipping_only


class ShippingRegionAvailabilityModifier(BasketModifierWithRequest):
    """
    Checks if products are available for given shipping region/method.
    """

    identifier = "shipping_region_availability"
    country_key = "country"

    def process_basket(self, basket: Basket, request: HttpRequest) -> None:
        country = basket.extra.get(self.country_key, None)

        for item in basket.get_items():
            if country:
                product_data = item.product.get_localized_data("product_data")
                limited_countries = product_data.get("limited_countries", [])
                item.extra[self.identifier] = (
                    not limited_countries or country in limited_countries
                )
                item.extra["log"] = limited_countries
            else:
                item.extra[self.identifier] = True


class TaxModifier(BasketModifierWithRequest):
    identifier = "tax"
    label = _("Tax")
    default_percentage = 21

    def process_basket(self, basket: Basket, request):
        tax_percent = self.default_percentage
        shipping_method = getattr(request, ShippingModifier.identifier, None)
        if shipping_method and shipping_method.tax_percentage is not None:
            tax_percent = shipping_method.tax_percentage

        # Calculate total amount after discounts
        total_amount = basket.subtotal

        # Subtract any product/subtotal discounts from total amount
        if hasattr(basket, "extra_rows") and basket.extra_rows:
            for row_id, row in basket.extra_rows.items():
                if row_id == "discount_code":
                    discount_data = row.instance.get("extra", {})
                    discount_type = discount_data.get("discount_type")

                    # Only subtract discounts that affect the taxable base
                    if discount_type in ["PERCENT_SUBTOTAL", "FIXED_AMOUNT"]:
                        discount_amount = abs(Decimal(str(row.instance["amount"])))
                        total_amount -= discount_amount

        # Ensure total amount is not negative
        total_amount = max(total_amount, Decimal("0"))

        # CORRECTED: Calculate VAT as included in the total (EU style)
        # VAT = total * (vat_rate / (100 + vat_rate))
        # This gives the VAT portion of a VAT-inclusive price
        vat_amount = total_amount * tax_percent / (Decimal("100") + tax_percent)
        net_amount = total_amount - vat_amount

        net_amount_display = salesman_settings.SALESMAN_PRICE_FORMATTER(
            net_amount, context={"request": request}
        )
        basket.extra["subtotal_without_tax"] = net_amount_display

        self.add_extra_row(
            request,
            basket,
            label=self.label,
            amount=vat_amount,
            extra={
                "percent": str(tax_percent),
                "calculation_method": "VAT_INCLUDED",
                "total_amount": str(total_amount),
                "net_amount": str(net_amount),
            },
            charge=False,
        )


class DiscountModifier(BasketModifierWithRequest):
    identifier = "discount_code"
    label = _("Discount")
    discount_key = identifier
    country_key = "country"
    local_country = "ES"
    postal_code_key = "postal_code"

    def process_basket(self, basket, request):
        # Check if request has discount
        try:
            data = request.data
            if "extra" in data:
                extra = data["extra"]
                _discount_code = extra.get(self.identifier)
                if _discount_code:
                    basket.extra[self.identifier] = bleach.clean(_discount_code)
        except Exception:
            pass

        discount_code = basket.extra.get(self.identifier)

        if not discount_code:
            # If code was removed, release any previous reservation AND clear the error.
            DiscountValidator.release_discount(discount_code, str(basket.id))
            if "discount_error" in basket.extra:
                del basket.extra["discount_error"]
            return

        try:
            # Check if discount exists
            discount = Discount.objects.get(code__iexact=discount_code)
        except Discount.DoesNotExist:
            basket.extra["discount_error"] = "Invalid discount code"
            return

        # Check availability with validator
        is_available, message = DiscountValidator.check_discount_availability(
            discount_code, str(basket.id)
        )
        if not is_available:
            basket.extra["discount_error"] = message
            return

        # Get the current total before applying discount
        current_total = basket.total

        # Validate discount including user-specific checks
        is_valid, message = discount.is_valid(
            basket_value=basket.subtotal,
            user=request.user if hasattr(request, "user") else None,
        )

        if not is_valid:
            basket.extra["discount_error"] = message
            # Release reservation if validation fails
            DiscountValidator.release_discount(discount_code, str(basket.id))
            return

        # Calculate discount amount based on type
        if discount.discount_type == "PERCENT_TOTAL":
            # Calculate discount based on total (including shipping)
            amount = current_total * (discount.value / 100)
            label = f"{round(discount.value)}% Off total order"

            if discount.value == 100:
                label = "Free order (100% off)"

        elif discount.discount_type == "PERCENT_SUBTOTAL":
            amount = basket.subtotal * (discount.value / 100)
            label = f"{round(discount.value)}% Off subtotal"

        elif discount.discount_type == "PERCENT_SHIPPING":
            # validate the user has gone through the shipping modifier
            if self.country_key not in basket.extra:
                return

            country = basket.extra[self.country_key]
            postal_code = basket.extra.get(self.postal_code_key, None)
            shipping_method = ShippingMethod.find_for_country(country, postal_code)
            if not shipping_method:
                return

            # Find shipping in extra rows
            shipping_row = next(
                (
                    row
                    for row in basket.extra_rows.values()
                    if row.instance["label"] == shipping_method.name
                ),
                None,
            )
            if not shipping_row:
                return

            # Extract shipping amount from the serializer
            shipping_amount = Decimal(
                shipping_row.instance["extra"]["price"].strip("Decimal('").strip("')")
            )
            amount = shipping_amount * (discount.value / 100)
            label = f"{round(discount.value)}% Off Shipping"

        elif discount.discount_type == "FIXED_AMOUNT":
            amount = min(discount.value, basket.subtotal)  # Don't exceed basket total
            label = f"{amount} Off"

        # Ensure the discount won't make the total negative
        if current_total - amount < Decimal("0.00"):
            amount = current_total  # Limit discount to current total
            label += " (Limited to prevent negative total)"

        self.add_extra_row(
            request,
            basket,
            label=label,
            amount=-amount,  # Negative amount for discount
            extra={
                "discount_code": discount_code,
                "discount_type": discount.discount_type,
                "discount_value": str(discount.value),
                "discount_amount": str(amount),  # Store the actual amount discounted
            },
        )


class AutomaticShippingDiscountModifier(BasketModifierWithRequest):
    """
    Automatically applies shipping discounts based on basket size.
    No discount code required - runs after shipping calculation.
    """

    identifier = "auto_shipping_discount"
    label = _("Shipping Discount")
    country_key = "country"
    postal_code_key = "postal_code"

    def process_basket(self, basket, request):
        # Only apply if we have country information (shipping has been calculated)
        if self.country_key not in basket.extra:
            return

        country = basket.extra[self.country_key]
        postal_code = basket.extra.get(self.postal_code_key, None)

        # Check if shipping has been calculated (look for shipping row)
        if not hasattr(basket, "extra_rows") or not basket.extra_rows:
            return

        # Look for shipping row first
        shipping_row = None

        # Look for any shipping row (identifier = "shipping")
        if "shipping" in basket.extra_rows:
            shipping_row = basket.extra_rows["shipping"]

        if not shipping_row:
            return

        # Try to find the shipping method by the label in the shipping row
        shipping_method_name = shipping_row.instance["label"]
        shipping_method = None

        # Try to find the shipping method by name
        try:
            shipping_method = ShippingMethod.objects.filter(
                name=shipping_method_name, countries__contains=[country]
            ).first()
        except:
            pass

        # Fallback: find default shipping method for country
        if not shipping_method:
            shipping_method = ShippingMethod.find_for_country(country, postal_code)

        if not shipping_method:
            return

        # Get the best automatic shipping discount
        best_discount = AutomaticShippingDiscount.get_best_discount(
            basket_value=Decimal(str(basket.subtotal)),
            country=country,
            shipping_method=shipping_method,
        )

        if not best_discount:
            return

        # Extract shipping amount from the serializer
        try:
            # First try to get from extra.price field (formatted as Decimal string)
            price_str = shipping_row.instance["extra"]["price"]
            if price_str.startswith("Decimal('") and price_str.endswith("')"):
                shipping_amount = Decimal(price_str.strip("Decimal('").strip("')"))
            else:
                shipping_amount = Decimal(price_str)
        except (KeyError, ValueError, AttributeError):
            # Fallback: try to get amount directly
            try:
                amount = shipping_row.instance["amount"]
                if isinstance(amount, str):
                    # Handle formatted price strings like "10.00 €"
                    amount = amount.replace("€", "").replace(",", "").strip()
                shipping_amount = Decimal(amount)
            except (ValueError, KeyError):
                return

        # Calculate discount amount
        discount_amount = shipping_amount * (best_discount.discount_percentage / 100)

        # Ensure we don't discount more than the shipping cost
        discount_amount = min(discount_amount, shipping_amount)

        if discount_amount <= Decimal("0.00"):
            return

        # Create label for the discount
        percentage_int = (
            int(best_discount.discount_percentage)
            if best_discount.discount_percentage % 1 == 0
            else best_discount.discount_percentage
        )
        label = f"{percentage_int}% Off Shipping"
        if best_discount.discount_percentage == 100:
            label = "Free Shipping"

        # Add the discount row
        self.add_extra_row(
            request,
            basket,
            label=label,
            amount=-discount_amount,  # Negative amount for discount
            extra={
                "auto_discount_id": best_discount.id,
                "auto_discount_name": best_discount.name,
                "discount_type": "AUTO_SHIPPING",
                "discount_percentage": str(best_discount.discount_percentage),
                "discount_amount": str(discount_amount),
                "shipping_method": shipping_method.name,
                "basket_value": str(basket.subtotal),
            },
        )
