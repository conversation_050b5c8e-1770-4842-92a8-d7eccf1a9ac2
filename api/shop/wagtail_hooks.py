from datetime import timedelta
import logging

from django import forms
from django.contrib.admin import SimpleList<PERSON>ilter
from django.contrib.admin.utils import quote
from django.urls import reverse
from django.urls.conf import re_path
from django.utils import timezone
from django.utils.formats import date_format
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from salesman.admin.wagtail_hooks import BaseOrderAdmin
from salesman.admin.wagtail_hooks import OrderButtonHelper as _OrderButtonHelper
from salesman.admin.wagtail_hooks import OrderModelForm
from salesman.basket.models import Basket
from salesman.conf import app_settings as salesman_settings
from salesman.orders.models import Order
from wagtail.admin.edit_handlers import (
    FieldPanel,
    InlinePanel,
    MultiFieldPanel,
    ObjectList,
    TabbedInterface,
)
from wagtail.admin.site_summary import SummaryItem
from wagtail.contrib.modeladmin.helpers import AdminURLHelper, PermissionHelper
from wagtail.contrib.modeladmin.options import (
    ModelAdmin,
    ModelAdminGroup,
    modeladmin_register,
)
from wagtail.contrib.modeladmin.views import EditView
from wagtail.core import hooks

from extra.edit_handlers import ReadOnlyPanel
from shop.models.discount import Discount, AutomaticShippingDiscount
from shop.models.log import OrderLog
from shop.models.order import OrderInternalReference
from shop.views.basket_admin import BasketReportView
from salesman.basket.serializers import ExtraRowsField

from .models import OrderSendcloudStatus, ShippingMethod, ShippingRegion
from .views import (
    OrderCreateView,
    OrderDispatchTriggerView,
    OrderPrintView,
    OrderReportView,
    OrderCancellationPreviewView,
)


class OrdersSummaryItem(SummaryItem):
    template = "shop/site_summary_orders.html"

    def get_context(self):
        return {"total_orders": Order.objects.all().count()}


@hooks.register("construct_homepage_summary_items")
def construct_homepage_summary_items(request, items):
    items.append(OrdersSummaryItem(request))


def _render_status_transition(value, obj, request):
    head = """<tr>
        <td>Date</td>
        <td>Info</td>
        <td>Type</td>
        <td>Action</td>
        </tr>"""

    body = ""
    dispatch_url = reverse("salesman_order_modeladmin_dispatch_trigger", args=[obj.id])
    for item in obj.logs.all():
        action = "-"
        if item.kind == OrderLog.Kind.STATUS_CHANGED:
            new_status = item.data["new_status"]
            action = f'<a href="{dispatch_url}?status={new_status}" class="button button-small button-secondary">Dispatch</a>'

        body += f"""<tr>
            <td>{date_format(item.created_at, 'DATETIME_FORMAT')}</td>
            <td>{item.info}</td>
            <td>{item.get_kind_display()}</td>
            <td>{action}</td>
            </tr>"""

    if not body:
        return mark_safe("<p>No entries.</p>")

    return format_html(
        '<table class="listing full-width">'
        "<thead>{}</thead>"
        "<tbody>{}</tbody>"
        "</table>",
        mark_safe(head),
        mark_safe(body),
    )


class OrderPermissionHelper(PermissionHelper):
    def user_can_create(self, user):
        return True

    def user_can_delete_obj(self, user, obj):
        return True


class OrderUrlHelper(AdminURLHelper):
    pass


class OrderButtonHelper(_OrderButtonHelper):
    def duplicate_button(self, pk, ref, **kwargs):
        url = self.url_helper.get_action_url("create", quote(pk)) + f"?from_order={ref}"
        return {
            "url": url,
            "label": "Duplicate",
            "classname": "button button-small button-secondary",
            "title": "Duplicate this %s" % self.verbose_name,
        }

    def print_button(self, pk, ref, **kwargs):
        return {
            "url": self.url_helper.get_action_url("print", quote(pk)) + "?locale=es",
            "label": "Print",
            "classname": "button button-small button-secondary",
            "title": "Print this %s" % self.verbose_name,
        }

    def cancellation_preview_button(self, pk, ref, **kwargs):
        return {
            "url": self.url_helper.get_action_url("cancellation_preview", quote(pk)),
            "label": "Preview Cancellation",
            "classname": "button button-small button-secondary",
            "title": "Preview what would happen if this order is cancelled",
        }

    def get_buttons_for_obj(self, obj, **kwargs):
        btns = super().get_buttons_for_obj(obj, **kwargs)
        btns.insert(-1, self.duplicate_button(obj.pk, obj.ref))
        btns.insert(-1, self.print_button(obj.pk, obj.ref))

        # Add cancellation preview button for orders that can be cancelled
        if obj.status in ["PROCESSING", "SHIPPED", "COMPLETED"]:
            btns.insert(-1, self.cancellation_preview_button(obj.pk, obj.ref))

        return btns


class OrderCountryFilter(SimpleListFilter):
    title = "Shipping destination"
    parameter_name = "shipping"

    def lookups(self, request, model_admin):
        return [("ES", "Spain"), ("INTL", "International")]

    def queryset(self, request, queryset):
        if self.value() == "ES":
            ids = [x.id for x in queryset if x.extra.get("country", None) == "ES"]
            return queryset.filter(id__in=ids)
        if self.value() == "INTL":
            ids = [x.id for x in queryset if x.extra.get("country", None) != "ES"]
            return queryset.filter(id__in=ids)


class OrderSendlcoudFilter(SimpleListFilter):
    title = "Sendcloud status"
    parameter_name = "sendcloud"

    def lookups(self, request, model_admin):
        return OrderSendcloudStatus.choices

    def filter_queryset_by_status(self, queryset, status: str):
        ids = [
            x.id
            for x in queryset
            if x.extra.get("sendcloud_status", OrderSendcloudStatus.PENDING) == status
        ]
        return queryset.filter(id__in=ids)

    def queryset(self, request, queryset):
        if self.value():
            return self.filter_queryset_by_status(queryset, status=self.value())


class OrderAdminForm(OrderModelForm):
    tracking_number = forms.CharField(required=False)
    tracking_url = forms.URLField(required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["tracking_url"].initial = self.instance.extra.get(
            "tracking_url", ""
        )
        self.fields["tracking_number"].initial = self.instance.extra.get(
            "tracking_number", ""
        )

    def save(self, commit=True):
        self.instance.extra["tracking_url"] = self.cleaned_data.get("tracking_url", "")
        self.instance.extra["tracking_number"] = self.cleaned_data.get(
            "tracking_number", ""
        )
        return super().save(commit)


def _format_internal_ref(value, instance, request):
    try:
        return instance.internal_ref.ref
    except OrderInternalReference.DoesNotExist:
        return "-"


def _format_reconstructed_json(value, obj, request):
    """
    Enhanced JSON formatter that attempts to reconstruct basket data for empty extra_rows.
    """
    from salesman.admin.utils import format_json
    from shop.views.order_extra import reconstruct_basket_from_order

    # For empty extra_rows, try reconstruction
    try:
        # Only proceed if this is an Order instance
        if isinstance(obj, Order):
            _extra_rows = obj.extra_rows
            if _extra_rows:
                return value  # previously formatted

            basket, errors = reconstruct_basket_from_order(obj, request)

            if not errors and basket.extra_rows:
                extra_rows = ExtraRowsField().to_representation(basket.extra_rows)
                return format_json(
                    extra_rows,
                    context={"order": True},
                )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.warning(f"Failed to reconstruct extra_rows for order: {str(e)}")

    # Fallback to original formatter
    return format_json(
        value,
        context={"order": True},
    )


class OrderAdmin(BaseOrderAdmin):
    button_helper_class = OrderButtonHelper
    url_helper_class = OrderUrlHelper
    permission_helper_class = OrderPermissionHelper
    create_view_class = OrderCreateView
    print_view_class = OrderPrintView
    report_view_class = OrderReportView
    dispatch_trigger_view_class = OrderDispatchTriggerView
    cancellation_preview_view_class = OrderCancellationPreviewView
    edit_template_name = "shop/order_edit.html"
    index_template_name = "shop/order_index.html"

    list_display = [
        "__str__",
        "internal_ref",
        "admin_customer_display",
        "email",
        "admin_status",
        "total_display",
        "admin_is_paid",
        "date_created",
    ]
    list_filter = BaseOrderAdmin.list_filter + [
        OrderCountryFilter,
        OrderSendlcoudFilter,
    ]
    search_fields = ["ref", "email", "token", "internal_ref__ref"]

    panels = BaseOrderAdmin.panels
    panels[0] = MultiFieldPanel(
        [
            ReadOnlyPanel("ref"),
            ReadOnlyPanel(
                "ref",
                formatter=_format_internal_ref,
                heading="Internal reference",
                help_text="Unique auto-incremented internal order reference.",
            ),
            ReadOnlyPanel("token"),
        ],
        heading="Info",
    )

    shipping_panels = [
        MultiFieldPanel(
            [
                FieldPanel("tracking_number", heading="Tracking number"),
                FieldPanel("tracking_url", heading="Tracking URL"),
            ],
            heading="Tracking info",
        ),
        InlinePanel("shippings", heading="Shipping"),
    ]

    history_panels = [
        ReadOnlyPanel(
            "logs",
            classname="salesman-order-items",
            renderer=_render_status_transition,
            heading="Logs",
        ),
    ]

    edit_handler = TabbedInterface(
        [
            ObjectList(panels, heading="Summary"),
            ObjectList(BaseOrderAdmin.items_panels, heading="Items"),
            ObjectList(BaseOrderAdmin.payments_panels, heading="Payments"),
            ObjectList(shipping_panels, heading="Shipping"),
            ObjectList(BaseOrderAdmin.notes_panels, heading="Notes"),
            ObjectList(history_panels, heading="History"),
        ],
        base_form_class=OrderAdminForm,
    )

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        Status = Order.get_statuses()
        if request.GET.get("status", "") != Status.NEW:
            queryset = queryset.exclude(status=Status.NEW)
        return queryset

    def get_admin_urls_for_registration(self):
        urls = super().get_admin_urls_for_registration()
        urls += (
            re_path(
                self.url_helper.get_action_url_pattern("print"),
                self.print_view,
                name=self.url_helper.get_action_url_name("print"),
            ),
            re_path(
                self.url_helper._get_action_url_pattern("report"),
                self.report_view,
                name=self.url_helper.get_action_url_name("report"),
            ),
            re_path(
                self.url_helper.get_action_url_pattern("dispatch_trigger"),
                self.dispatch_trigger_view,
                name=self.url_helper.get_action_url_name("dispatch_trigger"),
            ),
            re_path(
                self.url_helper.get_action_url_pattern("cancellation_preview"),
                self.cancellation_preview_view,
                name=self.url_helper.get_action_url_name("cancellation_preview"),
            ),
        )
        return urls

    def print_view(self, request, instance_pk):
        """
        Print order invoice view.
        """
        kwargs = {"model_admin": self, "instance_pk": instance_pk}
        view_class = self.print_view_class
        return view_class.as_view(**kwargs)(request)

    def cancellation_preview_view(self, request, instance_pk):
        """
        Cancellation preview view.
        """
        kwargs = {"model_admin": self, "instance_pk": instance_pk}
        view_class = self.cancellation_preview_view_class
        return view_class.as_view(**kwargs)(request)

    def report_view(self, request):
        """
        Generate report view.
        """
        kwargs = {"model_admin": self}
        view_class = self.report_view_class
        return view_class.as_view(**kwargs)(request)

    def dispatch_trigger_view(self, request, instance_pk):
        """
        Dispatch trigger view.
        """
        kwargs = {"model_admin": self, "instance_pk": instance_pk}
        view_class = self.dispatch_trigger_view_class
        return view_class.as_view(**kwargs)(request)

    def get_edit_handler(self, instance, request):
        """
        Override to use our custom formatter for extra_rows display
        """
        edit_handler = super().get_edit_handler(instance, request)

        # Find and update the extra_rows panel to use our custom formatter
        for tab in edit_handler.children:
            if tab.heading == "Summary":
                totals_panel = tab.children[3]
                if totals_panel and isinstance(totals_panel, MultiFieldPanel):
                    extra_rows = totals_panel.children[1]
                    extra_rows.formatter = _format_reconstructed_json

        return edit_handler


modeladmin_register(OrderAdmin)


class ShippingMethodEditView(EditView):
    def __init__(self, model_admin, instance_pk):
        super().__init__(model_admin, instance_pk)
        # Trigger prices data save on edid view
        self.instance.get_prices_data()

    def get_success_url(self):
        return self.edit_url


class ShippingMethodAdmin(ModelAdmin):
    model = ShippingMethod
    menu_icon = "resubmit"
    list_display = ["name", "admin_base_price", "admin_country_count"]
    edit_view_class = ShippingMethodEditView

    def admin_base_price(self, obj):
        return salesman_settings.SALESMAN_PRICE_FORMATTER(obj.base_price, {})

    admin_base_price.short_description = "Base price"  # type: ignore

    def admin_country_count(self, obj):
        return len(obj.countries) if obj.countries else 0

    admin_country_count.short_description = "Countries"  # type: ignore


class ShippingRegionAdmin(ModelAdmin):
    model = ShippingRegion
    menu_icon = "cog"

    list_display = ["name"]


class AbandonedBasketButtonHelper(_OrderButtonHelper):
    def edit_button(self, *args, **kwargs):
        button = super().edit_button(*args, **kwargs)
        button.update({"label": _("View"), "title": _("View this Basket")})
        return button

    def get_buttons_for_obj(self, obj, **kwargs):
        buttons = super().get_buttons_for_obj(obj, **kwargs)
        # Remove the delete button from the list of buttons
        buttons = [button for button in buttons if button["label"] != "Delete"]
        return buttons


class AbandonedBasketPermissionHelper(PermissionHelper):
    def user_can_create(self, user):
        return False

    def user_can_delete_obj(self, user, obj):
        return False


def render_extra_display(value, obj, request):
    formatted_extra = salesman_settings.format_json(
        obj.extra,
        context={"order": True},
    )
    formatted_extra = formatted_extra.replace("\\n", " ")
    return mark_safe(formatted_extra)


def render_basket_items(value, obj, request):
    obj.update(request)

    head = f"""<tr>
        <td>{_('Name')}</td>
        <td>{_('Unit price')}</td>
        <td>{_('Quantity')}</td>
        <td>{_('Subtotal')}</td>
        <td>{_('Total')}</td>
    </tr>"""

    body = ""
    for item in obj.get_items():
        item.update(request)
        body += "<tr>"
        body += f'<td class="title"><h2>{item.name}</h2></td>'
        body += f"<td>{salesman_settings.SALESMAN_PRICE_FORMATTER(item.unit_price, {})}</td>"
        body += f"<td>{item.quantity}</td>"
        body += (
            f"<td>{salesman_settings.SALESMAN_PRICE_FORMATTER(item.subtotal, {})}</td>"
        )
        body += f"<td>{salesman_settings.SALESMAN_PRICE_FORMATTER(item.total, {})}</td>"
        body += "</tr>"

    if not body:
        return mark_safe("<p>No items in the basket.</p>")

    footer = f"""<tr>
        <td></td>
        <td></td>
        <td></td>
        <td>{_('Potential Total + Taxes')}</td>
        <td>{salesman_settings.SALESMAN_PRICE_FORMATTER(obj.total, {})}</td>
    </tr>"""

    return format_html(
        '<table class="listing full-width">'
        "<thead>{}</thead>"
        "<tbody>{}</tbody>"
        "<tfoot>{}</tfoot>"
        "</table>",
        mark_safe(head),
        mark_safe(body),
        mark_safe(footer),
    )


class BasketOwnerFilter(SimpleListFilter):
    title = "Has Valid Owner"
    parameter_name = "owner"

    def lookups(self, request, model_admin):
        return (
            ("yes", _("Yes")),
            ("no", _("No")),
        )

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.exclude(owner=None)
        if self.value() == "no":
            return queryset.filter(owner=None)
        return queryset


class AbandonedBasketAdmin(ModelAdmin):
    model = Basket
    menu_label = "Baskets"
    menu_icon = "chain-broken"
    button_helper_class = AbandonedBasketButtonHelper
    permission_helper_class = AbandonedBasketPermissionHelper
    list_display = [
        "owner",
    ]

    search_fields = ["owner__username"]
    list_filter = ["date_updated", "date_created", BasketOwnerFilter]
    list_per_page = 20
    report_view_class = BasketReportView
    index_template_name = "shop/basket_index.html"

    panels = [
        MultiFieldPanel(
            [
                ReadOnlyPanel("owner", heading="Owner"),
                ReadOnlyPanel("extra", renderer=render_extra_display, heading="Extra"),
                ReadOnlyPanel(
                    "extra",
                    classname="salesman-order-items",
                    renderer=render_basket_items,
                    heading="Basket Items",
                ),
            ],
            heading="Info",
        ),
    ]
    edit_handler = TabbedInterface(
        [
            ObjectList(panels, heading="Summary"),
        ],
    )

    def get_admin_urls_for_registration(self):
        urls = super().get_admin_urls_for_registration()

        urls += (
            re_path(
                self.url_helper._get_action_url_pattern("report"),
                self.report_view,
                name=self.url_helper.get_action_url_name("report"),
            ),
        )
        return urls

    def report_view(self, request):
        """
        Generate report view.
        """
        kwargs = {"model_admin": self}
        view_class = self.report_view_class
        return view_class.as_view(**kwargs)(request)


class DiscountAdmin(ModelAdmin):
    model = Discount
    menu_label = "Discount Codes"
    list_display = [
        "code",
        "discount_type",
        "value",
        "is_active",
        "expires_at",
        "usage_status",
    ]
    list_filter = ["discount_type", "is_active", "expires_at"]
    search_fields = ["code", "description"]

    def usage_status(self, obj):
        """Display current usage status"""
        if not obj.max_uses:
            return "Unlimited"

        from shop.discount.validators import DiscountValidator

        try:
            reserved_baskets = DiscountValidator._get_reserved_baskets(obj.code)
            reserved_count = len(reserved_baskets)
            total_usage = obj.times_used + reserved_count
            remaining = max(0, obj.max_uses - total_usage)
            usage_percent = (total_usage / obj.max_uses * 100) if obj.max_uses else 0

            if usage_percent >= 90:
                color = "red"
            elif usage_percent >= 75:
                color = "orange"
            else:
                color = "green"

            return format_html(
                '<span style="color: {};">{}/{} used ({}% - {} reserved, {} remaining)</span>',
                color,
                obj.times_used,
                obj.max_uses,
                round(usage_percent, 1),
                reserved_count,
                remaining,
            )
        except Exception:
            return f"{obj.times_used}/{obj.max_uses} used"

    usage_status.short_description = "Usage Status"

    def get_admin_urls_for_registration(self):
        urls = super().get_admin_urls_for_registration()
        from django.urls import path

        urls += (
            path(
                f'{self.url_helper.get_action_url_pattern("index")}monitor/',
                self.monitor_view,
                name="discount_modeladmin_monitor",
            ),
            path(
                f'{self.url_helper.get_action_url_pattern("index")}clear-cache/<str:discount_code>/',
                self.clear_cache_view,
                name="discount_modeladmin_clear_cache",
            ),
        )
        return urls

    def monitor_view(self, request):
        """Monitor discount usage view"""
        from django.shortcuts import render
        from shop.discount.validators import DiscountValidator

        discounts_data = []
        discounts = Discount.objects.filter(is_active=True, max_uses__isnull=False)

        for discount in discounts:
            try:
                reserved_baskets = DiscountValidator._get_reserved_baskets(
                    discount.code
                )
                reserved_count = len(reserved_baskets)
                total_usage = discount.times_used + reserved_count
                remaining = max(0, (discount.max_uses or 0) - total_usage)
                usage_percent = (
                    (total_usage / discount.max_uses * 100) if discount.max_uses else 0
                )

                discounts_data.append(
                    {
                        "discount": discount,
                        "reserved_count": reserved_count,
                        "total_usage": total_usage,
                        "remaining": remaining,
                        "usage_percent": usage_percent,
                        "reserved_baskets": list(reserved_baskets),
                    }
                )
            except Exception as e:
                discounts_data.append(
                    {
                        "discount": discount,
                        "error": str(e),
                    }
                )

        context = {
            "discounts_data": discounts_data,
            "title": "Discount Usage Monitor",
        }
        return render(request, "admin/discount_monitor.html", context)

    def clear_cache_view(self, request, discount_code):
        """Clear cache for specific discount"""
        from django.shortcuts import redirect
        from django.contrib import messages
        from shop.discount.validators import DiscountValidator

        try:
            DiscountValidator.cleanup_expired_reservations(discount_code)
            messages.success(
                request, f"Cache cleared for discount code: {discount_code}"
            )
        except Exception as e:
            messages.error(request, f"Error clearing cache: {e}")

        return redirect("discount_modeladmin_monitor")


class AutomaticShippingDiscountAdmin(ModelAdmin):
    model = AutomaticShippingDiscount
    menu_label = "Auto Shipping Discounts"
    menu_icon = "tag"
    list_display = [
        "name",
        "discount_percentage",
        "min_basket_value",
        "max_basket_value",
        "is_active",
        "priority",
    ]
    list_filter = ["is_active", "priority", "valid_from", "valid_until"]
    search_fields = ["name", "description"]


class ShopAdmin(ModelAdminGroup):
    menu_label = "Shop"
    menu_icon = "cog"
    menu_order = 1000
    items = (
        ShippingRegionAdmin,
        ShippingMethodAdmin,
        AbandonedBasketAdmin,
        DiscountAdmin,
        AutomaticShippingDiscountAdmin,
    )


modeladmin_register(ShopAdmin)
