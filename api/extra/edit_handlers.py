from codemirror.widgets import CodeMirrorTextarea
from salesman.admin.edit_handlers import ReadOnlyPanel as BaseReadOnlyPanel
from wagtail.admin.edit_handlers import FieldPanel


class ReadOnlyPanel(BaseReadOnlyPanel):
    def render(self):
        value = super().render()
        return value if value is not None else "-"


class ReadOnlyDatePanel(ReadOnlyPanel):
    def __init__(self, attr, *args, **kwargs):
        kwargs["formatter"] = self.format_date
        super().__init__(attr, *args, **kwargs)

    def format_date(self, value, obj, request):
        from salesman.admin.wagtail_hooks import _format_date

        return _format_date(value, obj, request)


class CodeMirrorPanel(FieldPanel):
    mode = ""
    theme = "base16-light"
    dependencies: tuple[str, ...] = ()
    addon_js: tuple[str, ...] = ()
    addon_css: tuple[str, ...] = ()
    custom_css: tuple[str, ...] = ("codemirror-custom.css",)

    def __init__(self, field_name, *args, **kwargs):
        kwargs["widget"] = CodeMirrorTextarea(
            mode=self.mode,
            theme=self.theme,
            dependencies=self.dependencies,
            addon_js=self.addon_js,
            custom_css=self.custom_css,
        )
        kwargs["classname"] = "codemirror-field"
        super().__init__(field_name, *args, **kwargs)


class CodeMirrorHTMLPanel(CodeMirrorPanel):
    mode = "htmlmixed"
    dependencies = ("xml", "javascript", "css")


class CodeMirrorDjangoHTMLPanel(CodeMirrorPanel):
    mode = "django"
    dependencies = ("xml", "javascript", "css")
    addon_js = ("mode/overlay",)
