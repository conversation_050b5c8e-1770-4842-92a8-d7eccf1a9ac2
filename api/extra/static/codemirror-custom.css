.codemirror-field .object-layout {
  display: block;
}
.CodeMirror {
  height: auto;
  min-height: 251px;
  font-size: 1.2em;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  background-color: #fafafa !important;
  max-width: calc(100vw - 40px);
}
.CodeMirror.CodeMirror-focused {
  border-color: var(--color-input-focus-border);
  background-color: var(--color-input-focus) !important;
}
.CodeMirror-sizer {
  min-height: 251px !important;
}

@media screen and (min-width: 50em) {
  .CodeMirror {
    max-width: calc(100vw - 200px);
  }
  .multi-field .CodeMirror {
    max-width: calc(100vw - 390px);
  }
}
@media screen and (min-width: 80em) {
  .CodeMirror {
    max-width: 853px !important;
  }
}
