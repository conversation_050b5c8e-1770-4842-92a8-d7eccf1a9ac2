#!/usr/bin/env python
"""
Simple test script for customer export functionality (no database required).
Tests the core logic without needing a running database.
"""

import os
import pytest
import sys
from datetime import datetime, timedelta
from decimal import Decimal

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_helper_functions():
    """Test the helper functions work correctly."""
    print("🧪 Testing helper functions...")
    
    # Import our functions (this will test imports work)
    try:
        from catalog.reports import calculate_order_analytics, extract_name_from_address
        print("✅ Successfully imported functions")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        pytest.fail("Test failed")
    # Test calculate_order_analytics with empty data
    empty_analytics = calculate_order_analytics([], datetime.now())
    expected_empty = ["0", "€0.00", "€0.00", "", "", "", "", "", "", ""]
    
    if empty_analytics == expected_empty:
        print("✅ Empty analytics calculation works")
    else:
        print(f"❌ Empty analytics failed: got {empty_analytics}")
        pytest.fail("Test failed")
    # Test extract_name_from_address with mock order
    class MockOrder:
        def __init__(self, shipping_address):
            self.shipping_address = shipping_address
    
    # Test with simple name
    order1 = MockOrder("John Doe\n123 Main St\nCity, State")
    first, last = extract_name_from_address(order1)
    if first == "John" and last == "Doe":
        print(f"✅ Name extraction test 1: '{first}' '{last}'")
    else:
        print(f"❌ Name extraction test 1 failed: '{first}' '{last}'")
        pytest.fail("Test failed")
    # Test with JSON address
    order2 = MockOrder('{"first_name": "Jane", "last_name": "Smith", "address": "456 Oak Ave"}')
    first, last = extract_name_from_address(order2)
    if first == "Jane" and last == "Smith":
        print(f"✅ Name extraction test 2: '{first}' '{last}'")
    else:
        print(f"❌ Name extraction test 2 failed: '{first}' '{last}'")
        pytest.fail("Test failed")
    # Test with empty address
    order3 = MockOrder("")
    first, last = extract_name_from_address(order3)
    if first == "" and last == "":
        print(f"✅ Name extraction test 3 (empty): '{first}' '{last}'")
    else:
        print(f"❌ Name extraction test 3 failed: '{first}' '{last}'")
        pytest.fail("Test failed")
    print("✅ All helper functions work correctly!\n")
    # Test passed
def test_mock_order_analytics():
    """Test order analytics with mock data."""
    print("📊 Testing order analytics with mock data...")
    
    from catalog.reports import calculate_order_analytics
    
    # Create mock orders
    class MockOrder:
        def __init__(self, total, date_created):
            self.total = Decimal(str(total))
            self.date_created = date_created
    
    # Test with sample orders
    orders = [
        MockOrder(50.00, datetime(2024, 1, 15)),
        MockOrder(75.50, datetime(2024, 2, 20)),
        MockOrder(30.25, datetime(2024, 3, 10)),
    ]
    
    registration_date = datetime(2024, 1, 1)
    analytics = calculate_order_analytics(orders, registration_date)
    
    # Verify results
    expected_total_orders = "3"
    expected_total_spent = "€155.75"
    expected_avg_order = "€51.92"  # 155.75 / 3 = 51.916...
    
    if analytics[0] == expected_total_orders:
        print(f"✅ Total orders correct: {analytics[0]}")
    else:
        print(f"❌ Total orders wrong: expected {expected_total_orders}, got {analytics[0]}")
        pytest.fail("Test failed")
    if analytics[1] == expected_total_spent:
        print(f"✅ Total spent correct: {analytics[1]}")
    else:
        print(f"❌ Total spent wrong: expected {expected_total_spent}, got {analytics[1]}")
        pytest.fail("Test failed")
    # Check average (allow for rounding)
    if analytics[2].startswith("€51."):
        print(f"✅ Average order value correct: {analytics[2]}")
    else:
        print(f"❌ Average order value wrong: expected ~€51.92, got {analytics[2]}")
        pytest.fail("Test failed")
    print("✅ Order analytics calculations work correctly!\n")
    # Test passed
def test_csv_structure():
    """Test CSV structure and headers."""
    print("📋 Testing CSV structure...")
    
    # Test headers
    base_headers = [
        "Customer Type", "Email", "First Name", "Last Name", "Full Name",
        "Registration Date", "Is Activated", "Address"
    ]
    
    order_headers = [
        "Total Orders", "Total Spent", "Average Order Value",
        "First Order Date", "Last Order Date", "Last Order Amount",
        "Highest Order Amount", "Lowest Order Amount",
        "Days Since Last Order", "Customer Lifetime (Days)"
    ]
    
    all_headers = base_headers + order_headers
    
    print(f"✅ Base headers ({len(base_headers)}): {', '.join(base_headers[:3])}...")
    print(f"✅ Order headers ({len(order_headers)}): {', '.join(order_headers[:3])}...")
    print(f"✅ Total headers: {len(all_headers)}")
    
    # Test passed
def test_url_patterns():
    """Test that URL patterns are correctly defined."""
    print("🔗 Testing URL patterns...")
    
    try:
        # This tests that the wagtail_hooks imports work
        from catalog.wagtail_hooks import register_reports_urls
        print("✅ URL registration function imported successfully")
        
        # Test that our view function exists
        from catalog.reports import export_customers_view
        print("✅ Export view function imported successfully")
        
        # Test passed
    except ImportError as e:
        print(f"❌ URL pattern test failed: {e}")
        pytest.fail("Test failed")
def test_template_exists():
    """Test that the template file exists."""
    print("📄 Testing template file...")
    
    template_path = "catalog/templates/catalog/export_customers.html"
    if os.path.exists(template_path):
        print(f"✅ Template file exists: {template_path}")
        
        # Check template has basic structure
        with open(template_path, 'r') as f:
            content = f.read()
            
        required_elements = [
            "{% extends", "{% csrf_token %}", "include_orders", 
            "date_from", "date_to", "Export Customers"
        ]
        
        for element in required_elements:
            if element in content:
                print(f"✅ Template contains: {element}")
            else:
                print(f"❌ Template missing: {element}")
                pytest.fail("Test failed")
        # Test passed
    else:
        print(f"❌ Template file not found: {template_path}")
        pytest.fail("Test failed")
def main():
    """Run all tests that don't require database."""
    print("🧪 Customer Export System - Simple Tests")
    print("=" * 50)
    print("(No database connection required)")
    print()
    
    tests = [
        test_helper_functions,
        test_mock_order_analytics,
        test_csv_structure,
        test_url_patterns,
        test_template_exists,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed\n")
        except Exception as e:
            print(f"❌ Test error: {str(e)}\n")
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The customer export system is ready.")
        print("\n📋 Next steps:")
        print("1. Start your Django server with database")
        print("2. Go to Django admin: http://localhost:8000/admin/")
        print("3. Navigate to 'Catalog Reports' → 'Export Customers'")
        print("4. Test the export functionality manually")
        # Test passed
    else:
        print("❌ Some tests failed. Please check the errors above.")
        pytest.fail("Test failed")
if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
