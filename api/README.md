# LeeosMerch

Wagtail project.

## Development guide

Configure `.env` file using `.env.example` then run:

```bash
poetry install
poetry run python manage.py migrate
poetry run python manage.py createsuperuser
make runserver
# Optionally start worker using:
make runworker
```

Run tests:

```bash
poetry install -E tests
make test
```

Other available commands:

```bash
# Cleanup the junk files from project:
make clean
# Check code style:
make check
```