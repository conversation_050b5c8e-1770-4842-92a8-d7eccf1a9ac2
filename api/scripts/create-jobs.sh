#!/bin/sh
COMMAND_TOKEN=$1

if [ -z "$COMMAND_TOKEN" ]; then
    echo "Command token must be supplied as argument"
    exit 1
fi

echo "Creating jobs:"

echo "- Run tasks every minute"  # (At every minute)
gcloud scheduler jobs create http run_tasks_every_minute \
    --schedule="* * * * *" \
    --uri="https://cms.madkat.store/api/v1/command/run_tasks_every_minute/" \
    --headers="Authorization=Bearer $COMMAND_TOKEN"

echo "- Run tasks every hour"  # (At **:00)
gcloud scheduler jobs create http run_tasks_every_hour \
    --schedule="0 * * * *" \
    --uri="https://cms.madkat.store/api/v1/command/run_tasks_every_hour/" \
    --headers="Authorization=Bearer $COMMAND_TOKEN"

echo "- Run tasks every day"  # (At 01:00)
gcloud scheduler jobs create http run_tasks_every_day \
    --schedule="0 1 * * *" \
    --uri="https://cms.madkat.store/api/v1/command/run_tasks_every_day/" \
    --headers="Authorization=Bearer $COMMAND_TOKEN"

echo "- Run tasks every week"  # (At 02:00 on Monday)
gcloud scheduler jobs create http run_tasks_every_week \
    --schedule="0 2 * * 1" \
    --uri="https://cms.madkat.store/api/v1/command/run_tasks_every_week/" \
    --headers="Authorization=Bearer $COMMAND_TOKEN"

echo "- Clear cache every year"  # (At 00:00 on day-of-month 1 in January)
gcloud scheduler jobs create http clear_cache_every_year \
    --schedule="0 0 1 1 *" \
    --uri="https://cms.madkat.store/api/v1/command/clear_cache/" \
    --headers="Authorization=Bearer $COMMAND_TOKEN"

echo "Done."
