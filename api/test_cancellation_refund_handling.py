#!/usr/bin/env python
"""
Test script to verify cancellation and refund handling functionality.
"""
import os
import sys
import django
import pytest
import uuid
from decimal import Decimal

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project.settings.dev")
django.setup()

from django.test import RequestFactory
from salesman.orders.models import Order, OrderItem
from salesman.basket.models import Basket

from catalog.models.product import ProductVariant
from stats.models import ProductStats
from shop.signals import (
    handle_order_cancellation_or_refund,
    get_order_cancellation_summary,
)


@pytest.mark.django_db
def test_cancellation_stock_restoration():
    """Test that cancelling an order restores stock correctly"""
    print("🧪 Testing cancellation stock restoration...")

    # Create test variants with different stock scenarios
    from wagtail.core.models import Locale

    locale, _ = Locale.objects.get_or_create(language_code="en")

    # Limited stock variant
    limited_variant = ProductVariant.objects.create(
        code="LIMITED-001",
        price=Decimal("50.00"),
        quantity=10,  # Limited stock
        product_translation_key=uuid.uuid4(),
    )

    # Unlimited stock variant
    unlimited_variant = ProductVariant.objects.create(
        code="UNLIMITED-001",
        price=Decimal("30.00"),
        quantity=None,  # Unlimited stock
        product_translation_key=uuid.uuid4(),
    )

    # Create a mock order
    order = Order.objects.create(
        ref="TEST-001", status="PROCESSING", email="<EMAIL>"
    )

    # Create order items
    limited_item = OrderItem.objects.create(
        order=order,
        product_id=limited_variant.id,
        product_type="catalog.ProductVariant",
        unit_price=Decimal("50.00"),
        quantity=3,
        total=Decimal("150.00"),
        subtotal=Decimal("150.00"),
        product_data={"name": "Limited Product", "code": "LIMITED-001"},
    )

    unlimited_item = OrderItem.objects.create(
        order=order,
        product_id=unlimited_variant.id,
        product_type="catalog.ProductVariant",
        unit_price=Decimal("30.00"),
        quantity=2,
        total=Decimal("60.00"),
        subtotal=Decimal("60.00"),
        product_data={"name": "Unlimited Product", "code": "UNLIMITED-001"},
    )

    # Create sales stats
    ProductStats.objects.create(
        variant=limited_variant, sold_quantity=5, sold_total=Decimal("250.00")
    )

    ProductStats.objects.create(
        variant=unlimited_variant, sold_quantity=10, sold_total=Decimal("300.00")
    )

    # Record initial state
    initial_limited_stock = limited_variant.quantity
    initial_unlimited_stock = unlimited_variant.quantity

    limited_stats = ProductStats.objects.get(variant=limited_variant)
    unlimited_stats = ProductStats.objects.get(variant=unlimited_variant)

    initial_limited_sold = limited_stats.sold_quantity
    initial_unlimited_sold = unlimited_stats.sold_quantity
    initial_limited_total = limited_stats.sold_total
    initial_unlimited_total = unlimited_stats.sold_total

    print(f"   Initial limited stock: {initial_limited_stock}")
    print(f"   Initial unlimited stock: {initial_unlimited_stock}")
    print(f"   Initial limited sold: {initial_limited_sold}")
    print(f"   Initial unlimited sold: {initial_unlimited_sold}")

    # Test cancellation
    handle_order_cancellation_or_refund(
        sender=Order, order=order, new_status="CANCELLED", old_status="PROCESSING"
    )

    # Verify stock restoration
    limited_variant.refresh_from_db()
    unlimited_variant.refresh_from_db()

    assert (
        limited_variant.quantity == initial_limited_stock + 3
    ), f"Limited stock should be restored: {limited_variant.quantity} != {initial_limited_stock + 3}"
    assert (
        unlimited_variant.quantity is None
    ), f"Unlimited stock should remain None: {unlimited_variant.quantity}"

    print(
        f"   ✅ Limited stock restored: {initial_limited_stock} → {limited_variant.quantity}"
    )
    print(
        f"   ✅ Unlimited stock unchanged: {initial_unlimited_stock} → {unlimited_variant.quantity}"
    )

    # Verify sales adjustment
    limited_stats.refresh_from_db()
    unlimited_stats.refresh_from_db()

    expected_limited_sold = initial_limited_sold - 3
    expected_unlimited_sold = initial_unlimited_sold - 2
    expected_limited_total = initial_limited_total - Decimal("150.00")
    expected_unlimited_total = initial_unlimited_total - Decimal("60.00")

    assert (
        limited_stats.sold_quantity == expected_limited_sold
    ), f"Limited sold quantity should be adjusted: {limited_stats.sold_quantity} != {expected_limited_sold}"
    assert (
        unlimited_stats.sold_quantity == expected_unlimited_sold
    ), f"Unlimited sold quantity should be adjusted: {unlimited_stats.sold_quantity} != {expected_unlimited_sold}"
    assert (
        limited_stats.sold_total == expected_limited_total
    ), f"Limited sold total should be adjusted: {limited_stats.sold_total} != {expected_limited_total}"
    assert (
        unlimited_stats.sold_total == expected_unlimited_total
    ), f"Unlimited sold total should be adjusted: {unlimited_stats.sold_total} != {expected_unlimited_total}"

    print(
        f"   ✅ Limited sales adjusted: {initial_limited_sold} → {limited_stats.sold_quantity}"
    )
    print(
        f"   ✅ Unlimited sales adjusted: {initial_unlimited_sold} → {unlimited_stats.sold_quantity}"
    )
    print(
        f"   ✅ Limited total adjusted: {initial_limited_total} → {limited_stats.sold_total}"
    )
    print(
        f"   ✅ Unlimited total adjusted: {initial_unlimited_total} → {unlimited_stats.sold_total}"
    )


@pytest.mark.django_db
def test_refund_handling():
    """Test that refunding an order works the same as cancellation"""
    print("\n🧪 Testing refund handling...")

    # Create test variant
    from wagtail.core.models import Locale

    locale, _ = Locale.objects.get_or_create(language_code="en")

    variant = ProductVariant.objects.create(
        code="REFUND-001",
        price=Decimal("100.00"),
        quantity=5,
        product_translation_key=uuid.uuid4(),
    )

    # Create order
    order = Order.objects.create(
        ref="REFUND-TEST-001", status="COMPLETED", email="<EMAIL>"
    )

    OrderItem.objects.create(
        order=order,
        product_id=variant.id,
        product_type="catalog.ProductVariant",
        unit_price=Decimal("100.00"),
        quantity=2,
        total=Decimal("200.00"),
        subtotal=Decimal("200.00"),
        product_data={"name": "Refund Product", "code": "REFUND-001"},
    )

    ProductStats.objects.create(
        variant=variant, sold_quantity=8, sold_total=Decimal("800.00")
    )

    initial_stock = variant.quantity
    initial_stats = ProductStats.objects.get(variant=variant)
    initial_sold = initial_stats.sold_quantity
    initial_total = initial_stats.sold_total

    # Test refund
    handle_order_cancellation_or_refund(
        sender=Order, order=order, new_status="REFUNDED", old_status="COMPLETED"
    )

    # Verify changes
    variant.refresh_from_db()
    initial_stats.refresh_from_db()

    assert variant.quantity == initial_stock + 2, f"Stock should be restored on refund"
    assert (
        initial_stats.sold_quantity == initial_sold - 2
    ), f"Sales should be adjusted on refund"
    assert initial_stats.sold_total == initial_total - Decimal(
        "200.00"
    ), f"Sales total should be adjusted on refund"

    print(f"   ✅ Refund stock restored: {initial_stock} → {variant.quantity}")
    print(
        f"   ✅ Refund sales adjusted: {initial_sold} → {initial_stats.sold_quantity}"
    )


@pytest.mark.django_db
def test_cancellation_summary():
    """Test the cancellation summary function"""
    print("\n🧪 Testing cancellation summary...")

    # Create test data
    from wagtail.core.models import Locale

    locale, _ = Locale.objects.get_or_create(language_code="en")

    variant = ProductVariant.objects.create(
        code="SUMMARY-001",
        price=Decimal("25.00"),
        quantity=20,
        product_translation_key=uuid.uuid4(),
    )

    order = Order.objects.create(
        ref="SUMMARY-TEST-001", status="PROCESSING", email="<EMAIL>"
    )

    OrderItem.objects.create(
        order=order,
        product_id=variant.id,
        product_type="catalog.ProductVariant",
        unit_price=Decimal("25.00"),
        quantity=5,
        total=Decimal("125.00"),
        subtotal=Decimal("125.00"),
        product_data={"name": "Summary Product", "code": "SUMMARY-001"},
    )

    ProductStats.objects.create(
        variant=variant, sold_quantity=15, sold_total=Decimal("375.00")
    )

    # Get summary
    summary = get_order_cancellation_summary(order)

    assert summary["total_items"] == 1, f"Should have 1 item in summary"
    assert summary["has_limited_stock"] == True, f"Should detect limited stock"

    item = summary["items"][0]
    assert item["variant_code"] == "SUMMARY-001", f"Should have correct variant code"
    assert item["quantity_to_restore"] == 5, f"Should restore 5 units"
    assert item["current_stock"] == 20, f"Should show current stock"
    assert item["stock_after_restore"] == 25, f"Should calculate restored stock"
    assert item["current_sold_quantity"] == 15, f"Should show current sales"
    assert item["sold_after_adjustment"] == 10, f"Should calculate adjusted sales"

    print(f"   ✅ Summary generated correctly")
    print(
        f"   ✅ Stock calculation: {item['current_stock']} → {item['stock_after_restore']}"
    )
    print(
        f"   ✅ Sales calculation: {item['current_sold_quantity']} → {item['sold_after_adjustment']}"
    )


@pytest.mark.django_db
def test_edge_cases():
    """Test edge cases and error conditions"""
    print("\n🧪 Testing edge cases...")

    # Test 1: Order with no items
    empty_order = Order.objects.create(
        ref="EMPTY-001", status="PROCESSING", email="<EMAIL>"
    )

    # Should not crash
    handle_order_cancellation_or_refund(
        sender=Order, order=empty_order, new_status="CANCELLED", old_status="PROCESSING"
    )
    print("   ✅ Empty order handled gracefully")

    # Test 2: Double cancellation (should be skipped)
    from wagtail.core.models import Locale

    locale, _ = Locale.objects.get_or_create(language_code="en")

    variant = ProductVariant.objects.create(
        code="DOUBLE-001",
        price=Decimal("10.00"),
        quantity=10,
        product_translation_key=uuid.uuid4(),
    )

    order = Order.objects.create(
        ref="DOUBLE-001",
        status="CANCELLED",  # Already cancelled
        email="<EMAIL>",
    )

    OrderItem.objects.create(
        order=order,
        product_id=variant.id,
        product_type="catalog.ProductVariant",
        unit_price=Decimal("10.00"),
        quantity=1,
        total=Decimal("10.00"),
        subtotal=Decimal("10.00"),
        product_data={"name": "Double Product", "code": "DOUBLE-001"},
    )

    initial_stock = variant.quantity

    # Try to cancel again (should be skipped)
    handle_order_cancellation_or_refund(
        sender=Order,
        order=order,
        new_status="CANCELLED",
        old_status="CANCELLED",  # Was already cancelled
    )

    variant.refresh_from_db()
    assert (
        variant.quantity == initial_stock
    ), f"Stock should not change on double cancellation"
    print("   ✅ Double cancellation skipped correctly")


@pytest.mark.django_db
def test_production_edge_cases():
    """Test production-specific edge cases and error conditions"""
    print("\n🧪 Testing production edge cases...")

    # Test 1: Order with missing variant (variant deleted after order creation)
    print("\n   Test 1: Missing variant handling")
    order = Order.objects.create(
        ref="MISSING-VARIANT-001", status="PROCESSING", email="<EMAIL>"
    )

    # Create order item with non-existent product_id
    OrderItem.objects.create(
        order=order,
        product_id=99999,  # Non-existent variant
        product_type="catalog.ProductVariant",
        unit_price=Decimal("50.00"),
        quantity=2,
        total=Decimal("100.00"),
        subtotal=Decimal("100.00"),
        product_data={"name": "Missing Product", "code": "MISSING-001"},
    )

    # Should not crash when variant is missing
    try:
        handle_order_cancellation_or_refund(
            sender=Order, order=order, new_status="CANCELLED", old_status="PROCESSING"
        )
        print("   ✅ Missing variant handled gracefully")
    except Exception as e:
        print(f"   ❌ Missing variant caused error: {e}")

    # Test 2: Order with corrupted ProductStats
    print("\n   Test 2: Missing ProductStats handling")
    from wagtail.core.models import Locale

    locale, _ = Locale.objects.get_or_create(language_code="en")

    variant = ProductVariant.objects.create(
        code="NO-STATS-001",
        price=Decimal("25.00"),
        quantity=5,
        product_translation_key=uuid.uuid4(),
    )

    order = Order.objects.create(
        ref="NO-STATS-001", status="PROCESSING", email="<EMAIL>"
    )

    OrderItem.objects.create(
        order=order,
        product_id=variant.id,
        product_type="catalog.ProductVariant",
        unit_price=Decimal("25.00"),
        quantity=1,
        total=Decimal("25.00"),
        subtotal=Decimal("25.00"),
        product_data={"name": "No Stats Product", "code": "NO-STATS-001"},
    )

    # Ensure no ProductStats exists
    ProductStats.objects.filter(variant=variant).delete()

    try:
        handle_order_cancellation_or_refund(
            sender=Order, order=order, new_status="CANCELLED", old_status="PROCESSING"
        )
        print("   ✅ Missing ProductStats handled gracefully")
    except Exception as e:
        print(f"   ❌ Missing ProductStats caused error: {e}")

    # Test 3: Large order with many items (performance test)
    print("\n   Test 3: Large order performance")
    large_order = Order.objects.create(
        ref="LARGE-ORDER-001", status="PROCESSING", email="<EMAIL>"
    )

    # Create 50 order items
    variants = []
    for i in range(50):
        variant = ProductVariant.objects.create(
            code=f"BULK-{i:03d}",
            price=Decimal("10.00"),
            quantity=100,
            product_translation_key=uuid.uuid4(),
        )
        variants.append(variant)

        OrderItem.objects.create(
            order=large_order,
            product_id=variant.id,
            product_type="catalog.ProductVariant",
            unit_price=Decimal("10.00"),
            quantity=2,
            total=Decimal("20.00"),
            subtotal=Decimal("20.00"),
            product_data={"name": f"Bulk Product {i}", "code": f"BULK-{i:03d}"},
        )

        ProductStats.objects.create(
            variant=variant, sold_quantity=10, sold_total=Decimal("100.00")
        )

    import time

    start_time = time.time()

    try:
        handle_order_cancellation_or_refund(
            sender=Order,
            order=large_order,
            new_status="CANCELLED",
            old_status="PROCESSING",
        )

        end_time = time.time()
        processing_time = end_time - start_time
        print(
            f"   ✅ Large order (50 items) processed in {processing_time:.2f} seconds"
        )

        # Verify bulk operations worked
        updated_variants = ProductVariant.objects.filter(
            id__in=[v.id for v in variants]
        )
        for variant in updated_variants:
            if variant.quantity != 102:  # 100 + 2 restored
                print(f"   ❌ Bulk stock update failed for {variant.code}")
                break
        else:
            print("   ✅ Bulk stock updates successful")

    except Exception as e:
        print(f"   ❌ Large order processing failed: {e}")


@pytest.mark.django_db
def test_concurrent_cancellation_safety():
    """Test thread safety and concurrent cancellation scenarios"""
    print("\n🧪 Testing concurrent cancellation safety...")

    from wagtail.core.models import Locale

    locale, _ = Locale.objects.get_or_create(language_code="en")

    # Create variant with limited stock
    variant = ProductVariant.objects.create(
        code="CONCURRENT-001",
        price=Decimal("50.00"),
        quantity=10,
        product_translation_key=uuid.uuid4(),
    )

    ProductStats.objects.create(
        variant=variant, sold_quantity=20, sold_total=Decimal("1000.00")
    )

    # Create multiple orders for the same variant
    orders = []
    for i in range(3):
        order = Order.objects.create(
            ref=f"CONCURRENT-{i+1:03d}",
            status="PROCESSING",
            email=f"concurrent{i+1}@example.com",
        )

        OrderItem.objects.create(
            order=order,
            product_id=variant.id,
            product_type="catalog.ProductVariant",
            unit_price=Decimal("50.00"),
            quantity=2,
            total=Decimal("100.00"),
            subtotal=Decimal("100.00"),
            product_data={"name": "Concurrent Product", "code": "CONCURRENT-001"},
        )
        orders.append(order)

    initial_stock = variant.quantity
    initial_stats = ProductStats.objects.get(variant=variant)
    initial_sold = initial_stats.sold_quantity

    # Cancel all orders (simulating concurrent cancellations)
    for order in orders:
        handle_order_cancellation_or_refund(
            sender=Order, order=order, new_status="CANCELLED", old_status="PROCESSING"
        )

    # Verify final state
    variant.refresh_from_db()
    final_stats = ProductStats.objects.get(variant=variant)

    expected_stock = initial_stock + (3 * 2)  # 3 orders × 2 items each
    expected_sold = initial_sold - (3 * 2)

    assert (
        variant.quantity == expected_stock
    ), f"Stock should be {expected_stock}, got {variant.quantity}"
    assert (
        final_stats.sold_quantity == expected_sold
    ), f"Sold should be {expected_sold}, got {final_stats.sold_quantity}"

    print(f"   ✅ Concurrent cancellations handled correctly")
    print(
        f"   ✅ Stock: {initial_stock} → {variant.quantity} (+{expected_stock - initial_stock})"
    )
    print(
        f"   ✅ Sales: {initial_sold} → {final_stats.sold_quantity} (-{initial_sold - final_stats.sold_quantity})"
    )


@pytest.mark.django_db
def test_status_transition_validation():
    """Test all valid and invalid status transitions"""
    print("\n🧪 Testing status transition validation...")

    from wagtail.core.models import Locale

    locale, _ = Locale.objects.get_or_create(language_code="en")

    variant = ProductVariant.objects.create(
        code="STATUS-001",
        price=Decimal("30.00"),
        quantity=10,
        product_translation_key=uuid.uuid4(),
    )

    ProductStats.objects.create(
        variant=variant, sold_quantity=5, sold_total=Decimal("150.00")
    )

    # Test valid transitions that should trigger restoration
    valid_transitions = [
        ("PROCESSING", "CANCELLED"),
        ("SHIPPED", "CANCELLED"),
        ("COMPLETED", "REFUNDED"),
        ("PROCESSING", "REFUNDED"),
    ]

    for old_status, new_status in valid_transitions:
        order = Order.objects.create(
            ref=f"STATUS-{old_status}-{new_status}",
            status=old_status,
            email="<EMAIL>",
        )

        OrderItem.objects.create(
            order=order,
            product_id=variant.id,
            product_type="catalog.ProductVariant",
            unit_price=Decimal("30.00"),
            quantity=1,
            total=Decimal("30.00"),
            subtotal=Decimal("30.00"),
            product_data={"name": "Status Product", "code": "STATUS-001"},
        )

        initial_stock = variant.quantity

        handle_order_cancellation_or_refund(
            sender=Order, order=order, new_status=new_status, old_status=old_status
        )

        variant.refresh_from_db()
        assert (
            variant.quantity == initial_stock + 1
        ), f"Stock should increase for {old_status} → {new_status}"
        print(f"   ✅ {old_status} → {new_status}: Stock restored")

    # Test invalid transitions that should NOT trigger restoration
    invalid_transitions = [
        ("NEW", "CANCELLED"),
        ("CREATED", "CANCELLED"),
        ("CANCELLED", "REFUNDED"),  # Already cancelled
        ("REFUNDED", "CANCELLED"),  # Already refunded
    ]

    for old_status, new_status in invalid_transitions:
        order = Order.objects.create(
            ref=f"INVALID-{old_status}-{new_status}",
            status=old_status,
            email="<EMAIL>",
        )

        OrderItem.objects.create(
            order=order,
            product_id=variant.id,
            product_type="catalog.ProductVariant",
            unit_price=Decimal("30.00"),
            quantity=1,
            total=Decimal("30.00"),
            subtotal=Decimal("30.00"),
            product_data={"name": "Invalid Product", "code": "STATUS-001"},
        )

        initial_stock = variant.quantity

        handle_order_cancellation_or_refund(
            sender=Order, order=order, new_status=new_status, old_status=old_status
        )

        variant.refresh_from_db()
        assert (
            variant.quantity == initial_stock
        ), f"Stock should NOT change for {old_status} → {new_status}"
        print(f"   ✅ {old_status} → {new_status}: Stock unchanged (correct)")


if __name__ == "__main__":
    test_cancellation_stock_restoration()
    test_refund_handling()
    test_cancellation_summary()
    test_edge_cases()
    test_production_edge_cases()
    test_concurrent_cancellation_safety()
    test_status_transition_validation()
    print("\n🎉 All tests passed!")
