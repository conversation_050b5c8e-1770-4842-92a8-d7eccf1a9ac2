#!/usr/bin/env python3
"""
Quick setup script for discount load testing.
Creates the required test discount codes for load testing.
"""

import os
import sys
import django
from decimal import Decimal

# Configure Django settings
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project.settings.dev")
django.setup()

from shop.models import Discount, AutomaticShippingDiscount


def create_test_discount_codes():
    """Create essential test discount codes for load testing"""

    test_codes = {
        "FLASH50": {
            "description": "Flash Sale 50% Off - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("50.00"),
            "max_uses": 100,
            "is_active": True,
        },
        "SAVE20": {
            "description": "Save 20% Off - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("20.00"),
            "max_uses": 50,
            "is_active": True,
        },
        "RACE50": {
            "description": "Race Condition Test Discount",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("50.00"),
            "max_uses": 10,  # Very limited for race testing
            "is_active": True,
        },
        "UNLIMITED10": {
            "description": "Unlimited 10% Off - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("10.00"),
            "max_uses": None,  # Unlimited
            "is_active": True,
        },
        "STUDENT5": {
            "description": "Student 5% Discount - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("5.00"),
            "max_uses": None,  # Unlimited
            "is_active": True,
        },
    }

    created_count = 0
    updated_count = 0

    for code, config in test_codes.items():
        try:
            discount = Discount.objects.get(code=code)
            # Update existing
            for field, value in config.items():
                setattr(discount, field, value)
            discount.times_used = 0  # Reset counter
            discount.save()
            updated_count += 1
            print(f"✅ Updated: {code}")

        except Discount.DoesNotExist:
            # Create new
            discount = Discount.objects.create(code=code, times_used=0, **config)
            created_count += 1
            print(f"🆕 Created: {code}")

    return created_count, updated_count


def create_automatic_shipping_discounts():
    """Create automatic shipping discount configurations"""

    shipping_discounts = [
        {
            "name": "20% Shipping Discount - Load Test",
            "description": "20% off shipping for baskets €75-€149.99",
            "discount_percentage": Decimal("20.00"),
            "min_basket_value": Decimal("75.00"),
            "max_basket_value": Decimal("149.99"),
            "is_active": True,
        },
        {
            "name": "Free Shipping - Load Test",
            "description": "Free shipping for baskets €150+",
            "discount_percentage": Decimal("100.00"),
            "min_basket_value": Decimal("150.00"),
            "max_basket_value": None,
            "is_active": True,
        },
    ]

    created_count = 0
    updated_count = 0

    for config in shipping_discounts:
        try:
            discount = AutomaticShippingDiscount.objects.get(name=config["name"])
            # Update existing
            for field, value in config.items():
                setattr(discount, field, value)
            discount.save()
            updated_count += 1
            print(f"✅ Updated shipping: {config['name']}")

        except AutomaticShippingDiscount.DoesNotExist:
            # Create new
            discount = AutomaticShippingDiscount.objects.create(**config)
            created_count += 1
            print(f"🆕 Created shipping: {config['name']}")

    return created_count, updated_count


def main():
    """Main setup function"""

    print("🚀 Setting up discount codes for load testing")
    print("=" * 50)

    try:
        # Create discount codes
        created_codes, updated_codes = create_test_discount_codes()
        print(
            f"\n📊 Manual Discount Codes: {created_codes} created, {updated_codes} updated"
        )

        # Create shipping discounts
        created_shipping, updated_shipping = create_automatic_shipping_discounts()
        print(
            f"📦 Shipping Discounts: {created_shipping} created, {updated_shipping} updated"
        )

        print("\n✅ Setup complete!")
        print("\nNext steps:")
        print("1. Run basic load test: make runloadtest-basic")
        print("2. Run with discounts: make runloadtest-discounts")
        print("3. Run race condition test: make runloadtest-race")
        print("4. Monitor in admin: /admin/shop/discount/monitor/")

        return 0

    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
