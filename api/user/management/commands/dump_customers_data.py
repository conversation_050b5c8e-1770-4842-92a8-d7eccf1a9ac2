import csv
import io

from django.core.management.base import BaseCommand
from django.db.models import Sum
from django.utils.formats import date_format
from django.utils.translation import gettext_lazy as _
from django.utils.translation import pgettext_lazy
from salesman.conf import app_settings

from user.models import User


class Command(BaseCommand):
    help = "Dumps customer data as CSV"

    def handle(self, *args, **options):
        users = User.objects.filter(is_staff=False).prefetch_related("order_set")
        data = self.render_csv(users)
        self.stdout.write(data)

    def render_csv(self, users):
        stream = io.StringIO()
        writer = csv.writer(stream)
        headers = [
            pgettext_lazy("Customers CSV", "ID"),
            pgettext_lazy("Customers CSV", "Email address"),
            pgettext_lazy("Customers CSV", "First name"),
            pgettext_lazy("Customers CSV", "Last name"),
            pgettext_lazy("Customers CSV", "Date joined"),
            pgettext_lazy("Customers CSV", "Is activated"),
            pgettext_lazy("Customers CSV", "Number of orders"),
            pgettext_lazy("Customers CSV", "Amount spent"),
        ]
        writer.writerow(headers)
        for user in users:
            row = [
                user.id,
                user.email,
                user.first_name,
                user.last_name,
                date_format(user.date_joined, format="Y-m-d H:i"),
                _("Yes") if user.is_activated else _("No"),
                user.order_set.count(),
                app_settings.SALESMAN_PRICE_FORMATTER(
                    user.order_set.aggregate(Sum("total"))["total__sum"] or "0", {}
                ),
            ]
            writer.writerow(row)
        return stream.getvalue()
