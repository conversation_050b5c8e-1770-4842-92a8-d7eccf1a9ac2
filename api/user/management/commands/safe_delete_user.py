#!/usr/bin/env python
"""
Management command to safely delete users without causing recursion errors.
"""
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Safely delete a user by email or ID, handling related objects properly'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email address of the user to delete',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='ID of the user to delete',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Skip confirmation prompt',
        )

    def handle(self, *args, **options):
        email = options.get('email')
        user_id = options.get('user_id')
        dry_run = options.get('dry_run', False)
        force = options.get('force', False)

        if not email and not user_id:
            raise CommandError('You must specify either --email or --user-id')

        if email and user_id:
            raise CommandError('You cannot specify both --email and --user-id')

        # Find the user
        try:
            if email:
                user = User.objects.get(email=email)
            else:
                user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise CommandError(f'User not found: {email or user_id}')

        # Show user information
        self.stdout.write(f"\nUser to delete:")
        self.stdout.write(f"  ID: {user.id}")
        self.stdout.write(f"  Email: {user.email}")
        self.stdout.write(f"  Name: {user.get_full_name() or 'N/A'}")
        self.stdout.write(f"  Date joined: {user.date_joined}")
        self.stdout.write(f"  Is active: {user.is_active}")
        self.stdout.write(f"  Is staff: {user.is_staff}")

        # Check related objects
        from salesman.orders.models import Order
        from shop.models.discount import DiscountUsage

        order_count = Order.objects.filter(user=user).count()
        discount_usage_count = DiscountUsage.objects.filter(user=user).count()

        self.stdout.write(f"\nRelated objects:")
        self.stdout.write(f"  Orders: {order_count}")
        self.stdout.write(f"  Discount usages: {discount_usage_count}")

        if order_count > 0:
            self.stdout.write(self.style.WARNING(
                f"\n⚠️  This user has {order_count} orders. "
                "Orders will be ANONYMIZED (not deleted) to preserve accounting data."
            ))

        if discount_usage_count > 0:
            self.stdout.write(self.style.WARNING(
                f"⚠️  This user has {discount_usage_count} discount usages. "
                "These will be DELETED."
            ))

        if dry_run:
            self.stdout.write(self.style.SUCCESS(
                "\n🔍 DRY RUN: No changes will be made."
            ))
            return

        # Confirmation
        if not force:
            confirm = input(f"\nAre you sure you want to delete user '{user.email}'? (yes/no): ")
            if confirm.lower() != 'yes':
                self.stdout.write("Deletion cancelled.")
                return

        # Perform safe deletion
        try:
            self.stdout.write(f"\n🗑️  Deleting user {user.email}...")
            result = user.safe_delete()
            
            self.stdout.write(self.style.SUCCESS(
                f"\n✅ Successfully deleted user {result['email']} (ID: {result['user_id']})"
            ))
            self.stdout.write(f"   📦 Orders anonymized: {result['orders_anonymized']}")
            self.stdout.write(f"   🎫 Discount usages deleted: {result['discount_usages_deleted']}")
            
            if result['other_related_objects']:
                self.stdout.write(f"   🔗 Other related objects: {result['other_related_objects']}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"\n❌ Error deleting user: {e}"))
            raise CommandError(f"Failed to delete user: {e}")
