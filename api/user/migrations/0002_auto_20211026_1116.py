# Generated by Django 3.2.8 on 2021-10-26 09:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='address_city',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=255, verbose_name='City'),
        ),
        migrations.AddField(
            model_name='user',
            name='address_country',
            field=models.CharField(blank=True, max_length=255, verbose_name='Country'),
        ),
        migrations.AddField(
            model_name='user',
            name='address_line',
            field=models.Char<PERSON>ield(blank=True, max_length=255, verbose_name='Address'),
        ),
        migrations.AddField(
            model_name='user',
            name='address_phone',
            field=models.CharField(blank=True, max_length=255, verbose_name='Phone'),
        ),
        migrations.AddField(
            model_name='user',
            name='address_postal_code',
            field=models.Char<PERSON>ield(blank=True, max_length=255, verbose_name='Postal code'),
        ),
    ]
