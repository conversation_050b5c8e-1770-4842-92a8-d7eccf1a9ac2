# Generated by Django 3.2.9 on 2021-12-06 09:34

from django.db import migrations, models
import shop.validators


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0004_user_is_activated'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='address_city',
        ),
        migrations.RemoveField(
            model_name='user',
            name='address_country',
        ),
        migrations.RemoveField(
            model_name='user',
            name='address_line',
        ),
        migrations.RemoveField(
            model_name='user',
            name='address_phone',
        ),
        migrations.RemoveField(
            model_name='user',
            name='address_postal_code',
        ),
        migrations.AddField(
            model_name='user',
            name='address',
            field=models.TextField(blank=True, validators=[shop.validators.validate_address], verbose_name='Address'),
        ),
    ]
