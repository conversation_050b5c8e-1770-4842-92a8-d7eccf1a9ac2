from django.contrib.auth.models import AbstractUser
from django.contrib.auth.models import User<PERSON><PERSON><PERSON> as BaseUserManager
from django.db import models
from django.template.loader import render_to_string
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.db import transaction
from rest_framework_simplejwt.models import TokenUser as BaseTokenUser
from wagtail.admin.edit_handlers import FieldPanel, MultiFieldPanel
from wagtail.search import index

from extra.edit_handlers import ReadOnlyDatePanel, ReadOnlyPanel
from shop.validators import validate_address

from .utils import encode_str

import logging

logger = logging.getLogger(__name__)


class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        username = encode_str(email)
        return super().create_user(username, email, password, **extra_fields)

    def create_superuser(self, email, password=None, **extra_fields):
        username = encode_str(email)
        return super().create_superuser(username, email, password, **extra_fields)


class TokenUser(BaseTokenUser):
    """
    User model returned from JWT Token instead of DB.
    """

    @cached_property
    def email(self):
        return self.token.get("email", "")

    @cached_property
    def first_name(self):
        return self.token.get("first_name", "")

    @cached_property
    def last_name(self):
        return self.token.get("last_name", "")

    @cached_property
    def full_name(self):
        return self.token.get("full_name", "")

    def to_user(self):
        return User.objects.filter(id=self.id).first()


class User(AbstractUser, index.Indexed):
    email = models.EmailField("Email address", unique=True)
    is_activated = models.BooleanField("Activated", default=False)
    address = models.TextField("Address", blank=True, validators=[validate_address])

    objects = UserManager()

    panels = [
        MultiFieldPanel(
            [
                ReadOnlyPanel("email", heading="Email address"),
                ReadOnlyPanel("first_name", heading="First name"),
                ReadOnlyPanel("last_name", heading="Last name"),
                FieldPanel("address", heading="Address"),
                FieldPanel("is_activated"),
            ],
            heading="Info",
        ),
        ReadOnlyDatePanel("date_joined"),
        ReadOnlyPanel("admin_customer_orders", heading="Order history"),
    ]

    search_fields = [
        index.SearchField("email"),
        index.SearchField("first_name"),
        index.SearchField("last_name"),
    ]

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS: list[str] = []

    class Meta:
        verbose_name = "Customer"

    def save(self, *args, **kwargs):
        self.username = encode_str(self.email)
        super().save(*args, **kwargs)

    @property
    def full_name(self):
        return self.get_full_name()

    @property
    def admin_is_activated(self):
        icon, color = ("tick", "#157b57") if self.is_activated else ("cross", "#cd3238")
        template = '<span class="icon icon-{}" style="color: {};"></span>'
        return format_html(template, icon, color)

    @property
    def admin_customer_orders(self):
        return render_to_string(
            "user/customer_orders.html", {"orders": self.order_set.all()}
        )

    def to_user(self):
        # Placeholder to handle converting token user to user
        return self

    def safe_delete(self):
        """
        Safely delete a user by handling related objects to prevent recursion errors.
        This method addresses the JSONField recursion issue in salesman orders.
        """
        logger.info(f"Starting safe deletion of user {self.id} ({self.email})")

        with transaction.atomic():
            # Step 1: Handle related orders by anonymizing them instead of deleting
            # This preserves order data for accounting/reporting while removing user reference
            from salesman.orders.models import Order

            user_orders = Order.objects.filter(user=self)
            order_count = user_orders.count()

            if order_count > 0:
                logger.info(f"Found {order_count} orders for user {self.email}")

                # Anonymize orders instead of deleting them
                # This preserves order history for accounting and reporting
                user_orders.update(
                    user=None,  # Remove user reference
                    email=f"deleted-user-{self.id}@anonymized.local",  # Anonymize email
                )
                logger.info(f"Anonymized {order_count} orders")

            # Step 2: Handle discount usages
            from shop.models.discount import DiscountUsage

            discount_usages = DiscountUsage.objects.filter(user=self)
            usage_count = discount_usages.count()

            if usage_count > 0:
                logger.info(
                    f"Found {usage_count} discount usages for user {self.email}"
                )
                # Delete discount usages as they're not critical for accounting
                discount_usages.delete()
                logger.info(f"Deleted {usage_count} discount usages")

            # Step 3: Handle any other related objects that might cause issues
            # Check for any other foreign key relationships
            related_objects = []
            for field in self._meta.get_fields():
                if field.one_to_many or field.one_to_one:
                    if hasattr(self, field.get_accessor_name()):
                        try:
                            related_manager = getattr(self, field.get_accessor_name())
                            if hasattr(related_manager, "count"):
                                count = related_manager.count()
                                if count > 0:
                                    related_objects.append(
                                        (field.related_model.__name__, count)
                                    )
                        except Exception as e:
                            # Skip tables that don't exist (like shop_ordercreate in tests)
                            logger.warning(
                                f"Skipping related field {field.get_accessor_name()}: {e}"
                            )
                            continue

            if related_objects:
                logger.info(f"Found additional related objects: {related_objects}")

            # Step 4: Finally delete the user
            logger.info(f"Deleting user {self.id} ({self.email})")
            super().delete()
            logger.info(f"Successfully deleted user {self.id}")

            return {
                "user_id": self.id,
                "email": self.email,
                "orders_anonymized": order_count,
                "discount_usages_deleted": usage_count,
                "other_related_objects": related_objects,
            }

    def delete(self, using=None, keep_parents=False):
        """
        Override delete to use safe_delete by default.
        This prevents the recursion error when deleting users through admin.
        """
        return self.safe_delete()
