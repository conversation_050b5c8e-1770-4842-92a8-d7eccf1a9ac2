from typing import Optional
from urllib.parse import urlencode

from django.conf import settings
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ValidationError as DjangoValidationError
from django.utils.translation import pgettext_lazy
from rest_framework import serializers
from rest_framework.settings import api_settings
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.serializers import (
    TokenObtainPairSerializer,
    TokenRefreshSerializer,
)
from rest_framework_simplejwt.tokens import RefreshToken

from notification.models import NotificationTrigger
from notification.utils import get_email_template_context
from shop.validators import validate_address

from .models import User
from .utils import decode_str, encode_str


class CustomerSerializer(serializers.ModelSerializer):
    address = serializers.Char<PERSON>ield(
        required=False,
        allow_blank=True,
        validators=[validate_address],
        style={"base_template": "textarea.html"},
    )
    access = serializers.CharField(read_only=True)
    refresh = serializers.CharField(read_only=True)
    detail = serializers.Char<PERSON>ield(read_only=True)

    class Meta:
        model = User
        fields = [
            "email",
            "first_name",
            "last_name",
            "full_name",
            "address",
            "refresh",
            "access",
            "detail",
        ]
        read_only_fields = ["email"]

    def save(self, **kwargs):
        request = self.context["request"]

        msg = pgettext_lazy("User", "Info saved successfully.")
        self.validated_data["detail"] = msg

        for attr, value in self.validated_data.items():
            setattr(self.instance, attr, value)

        # Refresh token with new user data.
        token = getattr(request.user, "token", None)
        if token:
            refresh = RefreshSerializer.get_refreshed_token(token, self.instance)
            self.validated_data["refresh"] = str(refresh)
            self.validated_data["access"] = str(refresh.access_token)

        return super().save(**kwargs)


class UserSerializer(CustomerSerializer):
    """
    Alias for CustomerSerializer.
    """


class TokenUserSerializer(serializers.Serializer):
    email = serializers.CharField(read_only=True)
    first_name = serializers.CharField(read_only=True)
    last_name = serializers.CharField(read_only=True)
    full_name = serializers.CharField(read_only=True)

    def save(self, **kwargs):
        raise NotImplementedError("Can't save `TokenUserSerializer`.")


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(
        style={"input_type": "password"}, write_only=True
    )
    new_password = serializers.CharField(
        style={"input_type": "password"}, write_only=True
    )
    detail = serializers.CharField(read_only=True)

    default_error_messages = {
        "invalid": pgettext_lazy("User", "Invalid old password provided."),
    }

    def validate(self, attrs):
        request = self.context["request"]
        self.user = request.user.to_user()

        if not self.user:
            raise serializers.ValidationError("Missing user")

        old_password, new_password = attrs["old_password"], attrs["new_password"]
        if not self.user.check_password(old_password):
            raise serializers.ValidationError(
                {"old_password": self.error_messages["invalid"]}
            )
        validate_password_or_error(new_password, self.user, field="new_password")
        return attrs

    def save(self):
        self.user.set_password(self.validated_data["new_password"])
        self.user.save()
        msg = pgettext_lazy("User", "Password changed successfully.")
        self.validated_data["detail"] = msg


class LoginSerializer(TokenObtainPairSerializer):
    email = serializers.EmailField(write_only=True)
    password = serializers.CharField(style={"input_type": "password"}, write_only=True)
    access = serializers.CharField(read_only=True)
    refresh = serializers.CharField(read_only=True)
    detail = serializers.CharField(read_only=True)

    default_error_messages = {
        "no_active_account": pgettext_lazy(
            "User", "Invalid login credentials provided."
        ),
    }

    def validate(self, attrs):
        data = super().validate(attrs)
        data["detail"] = pgettext_lazy("User", "Logged in successfully.")
        return data

    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        for field in TokenUserSerializer().get_fields():
            token[field] = getattr(user, field, None)
        return token


class RefreshSerializer(TokenRefreshSerializer):
    @classmethod
    def get_refreshed_token(cls, token, user):
        LogoutSerializer.blacklist_token(token)
        return LoginSerializer.get_token(user)


class LogoutSerializer(serializers.Serializer):
    """
    Logout by blacklisting the refresh token.
    Access token will still work if used.
    """

    refresh = serializers.CharField(write_only=True)
    detail = serializers.CharField(read_only=True)

    def validate(self, attrs):
        self.blacklist_token(attrs["refresh"])
        return attrs

    def save(self):
        msg = pgettext_lazy("User", "Logged out successfully.")
        self.validated_data["detail"] = msg

    @classmethod
    def blacklist_token(cls, token):
        try:
            refresh = RefreshToken(token)
            refresh.blacklist()
        except TokenError:
            pass


class RegisterSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(style={"input_type": "password"}, write_only=True)
    return_url = serializers.URLField(write_only=True)
    access = serializers.CharField(read_only=True)
    refresh = serializers.CharField(read_only=True)
    detail = serializers.CharField(read_only=True)

    default_error_messages = {
        "exists": pgettext_lazy("User", "Account already exists."),
    }

    def validate(self, attrs):
        validate_password_or_error(
            attrs["password"],
            User(email=attrs["email"]),
            field="password",
        )
        if User.objects.filter(email=attrs["email"]).exists():
            raise serializers.ValidationError({"email": self.error_messages["exists"]})
        return attrs

    def save(self):
        request = self.context["request"]
        user = User.objects.create_user(
            email=self.validated_data["email"],
            password=self.validated_data["password"],
        )

        if getattr(settings, "USER_LOGIN_AFTER_REGISTRATION", False):
            refresh = LoginSerializer.get_token(user)
            self.validated_data["refresh"] = str(refresh)
            self.validated_data["access"] = str(refresh.access_token)

        dispatch_uid_and_token_notification(
            "CUSTOMER_REGISTERED",
            self.validated_data["return_url"],
            request,
            user,
        )
        msg = pgettext_lazy(
            "User",
            "Registered successfully and an activation link "
            "was sent to your email address.",
        )
        self.validated_data["detail"] = msg
        return user


class ResetPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(write_only=True)
    return_url = serializers.URLField(write_only=True)
    detail = serializers.CharField(read_only=True)

    def save(self):
        msg = pgettext_lazy("User", 'A password reset email was sent to "%s".')
        self.validated_data["detail"] = msg % self.validated_data["email"]

        try:
            user = User.objects.get(email=self.validated_data["email"])
        except User.DoesNotExist:
            # If user missing still return as if the request succeeded, this is for
            # safety reasons so that existance of users is not exposed.
            return

        dispatch_uid_and_token_notification(
            "RESET_PASSWORD",
            self.validated_data["return_url"],
            self.context["request"],
            user,
        )


class UidAndTokenSerializer(serializers.Serializer):
    uid = serializers.CharField(write_only=True)
    token = serializers.CharField(write_only=True)

    default_error_messages = {
        "invalid_uid": pgettext_lazy("User", "Provided UID is invalid."),
        "invalid_token": pgettext_lazy("User", "Provided token is invalid."),
    }

    def validate(self, attrs):
        attrs = super().validate(attrs)
        uid = self.initial_data.get("uid", "")
        token = self.initial_data.get("token", "")
        try:
            self.user = User.objects.get(pk=decode_str(uid))
        except Exception:
            raise serializers.ValidationError(
                {"uid": self.error_messages["invalid_uid"]}
            )
        if not default_token_generator.check_token(self.user, token):
            raise serializers.ValidationError(
                {"token": self.error_messages["invalid_token"]}
            )
        return attrs


class ActivateSerializer(UidAndTokenSerializer):
    detail = serializers.CharField(read_only=True)

    default_error_messages = {
        "already_activated": pgettext_lazy("User", "Account already activated."),
    }

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if self.user.is_activated:
            self.fail("already_activated")
        return attrs

    def save(self):
        self.user.is_activated = True
        self.user.save(update_fields=["is_activated"])
        msg = pgettext_lazy("User", "Account activated successfully.")
        self.validated_data["detail"] = msg


class ResetPasswordConfirmSerializer(UidAndTokenSerializer):
    new_password = serializers.CharField(
        style={"input_type": "password"}, write_only=True
    )
    detail = serializers.CharField(read_only=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        validate_password_or_error(
            attrs["new_password"], self.user, field="new_password"
        )
        return attrs

    def save(self):
        self.user.set_password(self.validated_data["new_password"])
        self.user.save()
        msg = pgettext_lazy("User", "Password reset successfully.")
        self.validated_data["detail"] = msg


def validate_password_or_error(password: str, user: User, field: Optional[str] = None):
    """
    Validates password for user or raises ValidationError.
    """
    try:
        validate_password(password, user)
    except DjangoValidationError as exc:
        error = serializers.as_serializer_error(exc)[api_settings.NON_FIELD_ERRORS_KEY]
        if field:
            error = {field: error}
        raise serializers.ValidationError(error)


def dispatch_uid_and_token_notification(trigger_id, return_url, request, user):
    """
    Helper function that dispatches a notification containing return `url`
    with user id and token params.
    """
    params = {
        "u": encode_str(user.id),
        "t": default_token_generator.make_token(user),
    }
    return_url += f"?{urlencode(params)}"
    context = get_email_template_context(request, user, return_url=return_url)
    NotificationTrigger.dispatch_triggers(trigger_id, context)
