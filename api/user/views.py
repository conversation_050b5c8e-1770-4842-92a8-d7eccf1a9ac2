from django.http.response import Http404
from generic_chooser.views import ChooserListingTabMixin, ModelChooserViewSet
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

from .models import User
from .serializers import (
    ActivateSerializer,
    ChangePasswordSerializer,
    LoginSerializer,
    LogoutSerializer,
    RefreshSerializer,
    RegisterSerializer,
    ResetPasswordConfirmSerializer,
    ResetPasswordSerializer,
    UserSerializer,
)


class UserChooserListingTabMixin(ChooserListingTabMixin):
    results_template = "user/user_chooser_results.html"

    def get_row_data(self, item):
        data = super().get_row_data(item)
        data["full_name"] = item.get_full_name() or "-"
        return data


class UserChooserViewSet(ModelChooserViewSet):
    icon = "user"
    model = User
    page_title = "Choose User"
    search_fields = ["email"]
    listing_tab_mixin_class = UserChooserListingTabMixin


class UserViewSet(viewsets.GenericViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_view_name(self):
        name = super().get_view_name()
        name = "User" if name == "User List" else name
        return name

    def list(self, request):
        raise Http404

    @action(["GET", "PUT"], detail=False, serializer_class=UserSerializer)
    def me(self, request):
        # Convert TokenUser to User
        user = request.user.to_user()
        if not user:
            raise Http404("User not found")

        if request.method == "GET":
            serializer = self.get_serializer(user)
            return Response(serializer.data)

        serializer = self.get_serializer(user, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(
        ["POST"],
        detail=False,
        url_path="change-password",
        serializer_class=ChangePasswordSerializer,
    )
    def change_password(self, request):
        return self._post_action(request)

    @action(
        ["POST"],
        detail=False,
        serializer_class=LoginSerializer,
        permission_classes=[AllowAny],
        authentication_classes=(),
    )
    def login(self, request):
        view = TokenObtainPairView.as_view(serializer_class=LoginSerializer)
        return view(request._request)

    @action(
        ["POST"],
        detail=False,
        url_path="refresh",
        serializer_class=RefreshSerializer,
        permission_classes=[AllowAny],
        authentication_classes=(),
    )
    def refresh(self, request):
        view = TokenRefreshView.as_view(serializer_class=RefreshSerializer)
        return view(request._request)

    @action(
        ["POST"],
        detail=False,
        serializer_class=LogoutSerializer,
        permission_classes=[AllowAny],
        authentication_classes=(),
    )
    def logout(self, request):
        return self._post_action(request)

    @action(
        ["POST"],
        detail=False,
        serializer_class=RegisterSerializer,
        permission_classes=[AllowAny],
        authentication_classes=(),
    )
    def register(self, request):
        return self._post_action(request, status.HTTP_201_CREATED)

    @action(
        ["POST"],
        detail=False,
        serializer_class=ActivateSerializer,
        permission_classes=[AllowAny],
    )
    def activate(self, request):
        return self._post_action(request)

    @action(
        ["POST"],
        detail=False,
        url_path="reset-password",
        serializer_class=ResetPasswordSerializer,
        permission_classes=[AllowAny],
        authentication_classes=(),
    )
    def reset_password(self, request):
        return self._post_action(request)

    @action(
        ["POST"],
        detail=False,
        url_path="reset-password-confirm",
        serializer_class=ResetPasswordConfirmSerializer,
        permission_classes=[AllowAny],
        authentication_classes=(),
    )
    def reset_password_confirm(self, request):
        return self._post_action(request)

    def _post_action(self, request, status=status.HTTP_200_OK):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status)
