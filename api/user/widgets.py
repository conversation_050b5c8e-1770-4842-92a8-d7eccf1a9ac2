from django.urls import reverse
from generic_chooser.widgets import AdminChooser

from .models import User


class UserChooser(AdminChooser):
    choose_one_text = "Choose Customer"
    choose_another_text = "Choose another Customer"
    link_to_chosen_text = "Edit this Customer"
    model = User
    choose_modal_url_name = "user_chooser:choose"

    def get_edit_item_url(self, item):
        return reverse("user_user_modeladmin_edit", args=[item.pk])
