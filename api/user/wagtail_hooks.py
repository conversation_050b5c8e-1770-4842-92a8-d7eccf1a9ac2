from wagtail.contrib.modeladmin.options import ModelAdmin, modeladmin_register
from wagtail.core import hooks

from .models import User
from .views import UserChooserViewSet


@hooks.register("register_admin_viewset")
def register_user_chooser_viewset():
    return UserChooserViewSet("user_chooser", url_prefix="user-chooser")


@modeladmin_register
class UserAdmin(ModelAdmin):
    model = User
    menu_label = "Customers"
    menu_order = 1000
    menu_icon = "user"
    list_display = ["admin_title", "date_joined", "admin_is_activated"]
    search_fields = ["email", "first_name", "last_name"]
    title_field_name = "__str__"

    def admin_title(self, obj):
        return str(obj)

    admin_title.short_description = "Customer"  # type: ignore

    def admin_is_activated(self, obj):
        return obj.is_activated

    admin_is_activated.boolean = True  # type: ignore
    admin_is_activated.short_description = "Activated"  # type: ignore
