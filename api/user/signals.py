from __future__ import annotations

import os

import google.auth
from django.contrib.auth import get_user_model
from google.auth.exceptions import GoogleAuthError
from google.cloud import secretmanager


def create_admin(sender, **kwargs):
    """
    Dynamically create an admin user after migrations.
    Password is pulled from Google Secret Manger.
    """

    if os.getenv("DJANGO_SETTINGS_MODULE") in [
        "project.settings.dev",
        "project.settings.test",
    ]:
        print("Dev/test environment, skipping admin creation...")
        return

    secret = os.getenv("DJANGO_ADMIN_PASSWORD_SECRET")
    if not secret:
        print("Missing DJANGO_ADMIN_PASSWORD_SECRET, skipping admin creation...")
        return

    try:
        # Uses `GOOGLE_APPLICATION_CREDENTIALS` environment variable.
        _, project = google.auth.default()
    except GoogleAuthError:
        print("No Google credentials, skipping admin creation...")
        return

    if not project:
        print("No Google project, skipping admin creation...")
        return

    print(f"Reading secret `{secret}` from `{project}`...")
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project}/secrets/{secret}/versions/latest"
    response = client.access_secret_version(request={"name": name})
    payload = response.payload.data.decode("UTF-8")

    print("Creating admin user...")
    User = get_user_model()
    try:
        admin = User.objects.get(email="<EMAIL>")
        admin.set_password(payload)
        admin.save()
    except User.DoesNotExist:
        User.objects.create_superuser(
            email="<EMAIL>",
            password=payload,
        )
