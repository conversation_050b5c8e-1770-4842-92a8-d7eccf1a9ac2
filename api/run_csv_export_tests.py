#!/usr/bin/env python
"""
Test runner script for CSV export functionality.

This script helps run the CSV export tests with proper Django setup.
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

if __name__ == "__main__":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project.settings.dev")
    django.setup()
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Define test modules for CSV export functionality
    csv_export_tests = [
        'tests.test_csv_export_reporting',
        'tests.test_csv_export_integration', 
        'tests.test_csv_export_urls',
    ]
    
    print("🧪 Running CSV Export Tests")
    print("=" * 50)
    
    # Run all CSV export tests
    failures = test_runner.run_tests(csv_export_tests)
    
    if failures:
        print(f"\n❌ {failures} test(s) failed")
        sys.exit(1)
    else:
        print("\n✅ All CSV export tests passed!")
        sys.exit(0)
