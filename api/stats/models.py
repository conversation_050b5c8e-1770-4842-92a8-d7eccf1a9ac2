from __future__ import annotations

from typing import Optional

from django.db import models, transaction
from salesman.basket.models import Basket
from salesman.orders.models import OrderItem

from catalog.models import ProductVariant


class ProductStats(models.Model):
    variant = models.OneToOneField(
        ProductVariant,
        on_delete=models.CASCADE,
        related_name="stats",
    )

    sold_quantity = models.IntegerField(default=0)
    sold_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    def populate_from_item(self, item: OrderItem):
        self.sold_quantity += item.quantity
        self.sold_total += item.total
        return ["sold_quantity", "sold_total"]

    @classmethod
    def from_item(cls, item: OrderItem) -> ProductStats:
        obj = cls(variant_id=item.product_id)
        obj.populate_from_item(item)
        return obj

    @classmethod
    @transaction.atomic
    def record_basket(cls, basket: Basket):
        """
        Record product stats from basket.
        """
        items = basket.get_items()
        product_ids = [x.product_id for x in items]
        create_objs = []
        update_objs = list(cls.objects.filter(variant_id__in=product_ids))
        update_fields: Optional[list] = None
        objs_product_id_map = {x.variant_id: x for x in update_objs}

        for item in items:
            try:
                obj = objs_product_id_map[item.product_id]
                update_fields = obj.populate_from_item(item)
            except KeyError:
                obj = cls.from_item(item)
                create_objs.append(obj)

        if len(update_objs) > 1:
            cls.objects.bulk_update(update_objs, update_fields)
        elif update_objs and update_fields:
            update_objs[0].save(update_fields=update_fields)
        if len(create_objs) > 1:
            cls.objects.bulk_create(create_objs)
        elif create_objs:
            create_objs[0].save()
