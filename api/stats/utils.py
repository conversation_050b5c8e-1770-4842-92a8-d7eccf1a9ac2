from __future__ import annotations

from dataclasses import dataclass, field
from decimal import Decimal
from typing import TYPE_CHECKING, Optional

from django.utils.safestring import mark_safe

from shop.formatters import price_format

from .models import ProductStats

if TYPE_CHECKING:
    from catalog.models import Product, ProductVariant


@dataclass
class ProductStatsData:
    sold_quantity: int
    sold_total: Decimal
    sold_total_display: str = field(init=False, default="")

    def __post_init__(self):
        self.sold_total_display = price_format(self.sold_total, {})


def get_stats_for_product(product: Product) -> ProductStatsData:
    variant_ids = product.get_variants().values_list("id", flat=True)
    objs = list(ProductStats.objects.filter(variant_id__in=variant_ids))

    return ProductStatsData(
        sold_quantity=sum([x.sold_quantity for x in objs]),
        sold_total=Decimal(sum([x.sold_total for x in objs])),
    )


def get_stats_for_variant(variant: ProductVariant) -> ProductStatsData:
    obj = ProductStats.objects.filter(variant_id=variant.id).first()
    return ProductStatsData(
        sold_quantity=obj.sold_quantity if obj else 0,
        sold_total=obj.sold_total if obj else 0,
    )


def admin_format_stats(
    stats: Optional[ProductStatsData] = None,
    product: Optional[Product] = None,
    variant: Optional[ProductVariant] = None,
) -> str:
    """
    Format stats for admin display.
    """

    if not stats:
        if product:
            stats = get_stats_for_product(product)
        elif variant:
            stats = get_stats_for_variant(variant)
        else:
            stats = ProductStatsData(0, 0)

    output = f"""
    <p>Units sold: <span class="status-tag primary">{stats.sold_quantity}</span></p>
    <p style="margin-bottom: 0">Total sales: <strong>{stats.sold_total_display}</strong></p>
    """
    return mark_safe(output)
