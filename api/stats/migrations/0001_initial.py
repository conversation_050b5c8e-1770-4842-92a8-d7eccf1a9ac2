# Generated by Django 3.2.8 on 2021-10-26 12:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('catalog', '0005_auto_20211022_1512'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sold_quantity', models.IntegerField(default=0)),
                ('sold_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('variant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stats', to='catalog.productvariant')),
            ],
        ),
    ]
