import json

from django.core.management.base import BaseCommand, CommandParser

from loadtest.utils import get_loadtest_data


class Command(BaseCommand):
    help = "Dump data for load-testing"

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("--file")

    def handle(self, *args, **options):
        data = get_loadtest_data()
        out = json.dumps(data, indent=2)
        self.stdout.write(out)
