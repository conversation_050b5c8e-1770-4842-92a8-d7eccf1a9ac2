from django.http import JsonResponse
from django.urls import path
from wagtail.core import hooks

from .utils import get_loadtest_data


def loadtest_json_data_view(request):
    """
    Render load-testing data as JSON.
    """
    data = get_loadtest_data()
    return JsonResponse(data, json_dumps_params={"indent": 2})


@hooks.register("register_admin_urls")
def register_loadtest_urls():
    return [
        path("loadtest-json-data/", loadtest_json_data_view, name="loadtest-json-data")
    ]
