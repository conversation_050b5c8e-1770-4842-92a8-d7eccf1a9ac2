"""
Integrated Load Testing with Enhanced Discount & Race Condition Testing
This file combines your existing load testing with comprehensive discount testing.
"""

import random
import time
import json
import os
from typing import Dict, List, Optional
from decimal import Decimal

from locust import FastHttpUser, between, task, events
from locust.exception import RescheduleTask

# Configuration - discount testing enabled by default
ENABLE_DISCOUNT_TESTING = os.getenv("ENABLE_DISCOUNT_TESTING", "true").lower() == "true"
DISCOUNT_TEST_INTENSITY = int(os.getenv("DISCOUNT_TEST_INTENSITY", "3"))  # 1-5 scale
RACE_CONDITION_TESTING = os.getenv("RACE_CONDITION_TESTING", "true").lower() == "true"

# Auto-detect if we're in a test environment vs production
# In production, we might want to disable some aggressive testing
IS_PRODUCTION = os.getenv("DJANGO_SETTINGS_MODULE", "").endswith("production")
if IS_PRODUCTION:
    RACE_CONDITION_TESTING = False  # Disable aggressive race testing in production
    DISCOUNT_TEST_INTENSITY = min(DISCOUNT_TEST_INTENSITY, 2)  # Lower intensity

# Your existing product data
LOADTEST_DATA = {
    "product_urls": [
        "/en/collections/urban-collection/madkat-mosaic-t-shirt/",
        "/en/collections/urban-collection/madkat-mosaic-hoodie/",
        "/en/collections/urban-collection/madkat-shadow-t-shirt/",
        "/en/collections/urban-collection/madkat-shadow-hoodie/",
        "/en/collections/urban-collection/madkat-urban-mosaic-kata-t-shirt/",
        "/en/collections/urban-collection/madkat-hoodie-mosaic-kata/",
        "/en/collections/urban-collection/madkat-shadow-kata-t-shirt/",
        "/en/collections/urban-collection/madkat-shadow-kata-hoodie/",
        "/en/collections/urban-collection/madkat-bright-shadow-camiseta/",
        "/en/collections/urban-collection/urban-cap/",
        "/en/collections/urban-collection/urban-white-cap/",
        "/en/collections/original-collection/mad-kat-originals-t-shirt/",
        "/en/collections/original-collection/madkat-originals-hoodie/",
        "/en/collections/original-collection/madkat-originals-embroidered-t-shirt/",
        "/en/collections/original-collection/madkat-originals-embroidered-hoodie/",
        "/en/collections/original-collection/madkat-beanie/",
        "/en/collections/original-collection/madkat-originals-bottle/",
        "/en/collections/original-collection/madkat-originals-backpack/",
        "/en/collections/original-collection/madkat-pins/",
    ],
    "collection_urls": [
        "/en/collections/tactical-collection/",
        "/en/collections/urban-collection/",
        "/en/collections/original-collection/",
        "/en/collections/jackets-hoodies/",
        "/en/collections/t-shirts/",
        "/en/collections/accessories/",
        "/es/colecciones/tactical-collection/",
        "/es/colecciones/urban-collection/",
        "/es/colecciones/original-collection/",
        "/es/colecciones/jackets-hoodies/",
        "/es/colecciones/t-shirts/",
        "/es/colecciones/accessories/",
    ],
    # Enhanced discount testing data
    "discount_codes": [
        "FLASH50",  # Limited discount for race testing
        "SAVE20",  # Limited discount
        "RACE50",  # Very limited for intensive race testing
        "UNLIMITED10",  # Unlimited discount
        "STUDENT5",  # Unlimited discount
        "EXPIRED",  # Invalid/expired for error testing
    ],
    "shipping_thresholds": [
        {"value": 50.0, "expected": None},  # Below threshold
        {"value": 75.0, "expected": "20%"},  # 20% shipping discount
        {"value": 150.0, "expected": "FREE"},  # Free shipping
    ],
}

# Global statistics for discount testing
discount_stats = {
    "successful_applications": 0,
    "failed_applications": 0,
    "race_condition_detected": 0,
    "automatic_discounts_applied": 0,
    "shipping_discounts_applied": 0,
}


def log_discount_event(event_type: str, discount_code: str = None, details: str = None):
    """Log discount-related events"""
    if not ENABLE_DISCOUNT_TESTING:
        return
    timestamp = time.time()
    message = f"[DISCOUNT] {event_type}"
    if discount_code:
        message += f" - Code: {discount_code}"
    if details:
        message += f" - {details}"
    print(f"{timestamp}: {message}")


def get_random_data(property_name: str):
    return random.choice(LOADTEST_DATA.get(property_name, []))


def build_basket_to_value(client, target_value: float) -> bool:
    """Build a basket to reach approximately the target value"""
    current_value = 0.0
    attempts = 0
    max_attempts = 8

    while current_value < target_value and attempts < max_attempts:
        product_url = get_random_data("product_urls")
        response = client.get("/api/v1/pages/find" + product_url)

        if response.status_code == 200:
            try:
                data = response.json()
                variants = data.get("variants", [])
                if variants:
                    variant = random.choice(variants)
                    price = float(variant.get("price", 0))

                    if current_value + price <= target_value * 1.3:  # Allow 30% overage
                        item_data = {
                            "product_type": variant["product_type"],
                            "product_id": variant["id"],
                        }
                        add_response = client.post(
                            "/api/v1/basket/?basket", json=item_data
                        )

                        if add_response.status_code in [200, 201]:
                            current_value += price

            except (ValueError, KeyError, json.JSONDecodeError):
                pass

        attempts += 1

    return current_value >= target_value * 0.8  # Success if within 80% of target


class Customer(FastHttpUser):
    """
    Enhanced Customer user with integrated discount testing.
    Maintains your existing behavior while adding discount capabilities.
    """

    wait_time = between(1, 7)

    def on_start(self):
        """Initialize user session"""
        self.applied_discounts = []

    @task(8)  # Your existing task
    def view_collection(self):
        self.client.get("/api/v1/pages/find" + get_random_data("collection_urls"))

    @task(5)  # Your existing task with discount enhancement
    def view_and_add_product(self):
        response = self.client.get(
            "/api/v1/pages/find" + get_random_data("product_urls")
        )

        # 25% chance to add product to basket
        if response.status_code == 200 and random.random() < 0.25:
            variants = response.json()["variants"]
            if len(variants):
                obj = random.choice(variants)
                data = {"product_type": obj["product_type"], "product_id": obj["id"]}
                self.client.post("/api/v1/basket/?basket", json=data)

                # Enhanced: Test discount codes occasionally
                if ENABLE_DISCOUNT_TESTING and random.random() < 0.3:
                    self._try_apply_discount()

                # 50% chance to purchase the product
                if random.random() < 0.50:
                    self._create_checkout()

    @task(2)  # Your existing task
    def view_basket(self):
        self.client.get("/api/v1/basket/")

    @task(1)  # Your existing task
    def view_checkout(self):
        self.client.get("/api/v1/checkout/")

    # Enhanced discount testing tasks (active by default)
    @task(4 if ENABLE_DISCOUNT_TESTING else 0)
    def test_automatic_shipping_discount(self):
        """Test automatic shipping discounts"""
        if not ENABLE_DISCOUNT_TESTING:
            return

        scenario = random.choice(LOADTEST_DATA["shipping_thresholds"])
        target_value = scenario["value"]
        expected = scenario["expected"]

        log_discount_event(
            "TESTING_AUTOMATIC_SHIPPING",
            details=f"Target: €{target_value}, Expected: {expected}",
        )

        # Clear basket
        self.client.delete("/api/v1/basket/")

        # Build basket to target value
        if build_basket_to_value(self.client, target_value):
            # Check for automatic discounts
            basket_response = self.client.get("/api/v1/basket/")
            if basket_response.status_code == 200:
                try:
                    basket_data = basket_response.json()
                    extra_rows = basket_data.get("extra_rows", [])

                    shipping_discount_found = any(
                        row.get("modifier") == "automatic_shipping_discount"
                        for row in extra_rows
                    )

                    if shipping_discount_found:
                        discount_stats["automatic_discounts_applied"] += 1
                        discount_stats["shipping_discounts_applied"] += 1
                        log_discount_event(
                            "AUTOMATIC_SHIPPING_APPLIED",
                            details=f"Value: €{target_value}",
                        )
                    elif expected:
                        log_discount_event(
                            "AUTOMATIC_SHIPPING_MISSING",
                            details=f"Expected {expected} but not found",
                        )

                except (ValueError, KeyError, json.JSONDecodeError) as e:
                    log_discount_event("AUTOMATIC_SHIPPING_ERROR", details=str(e))

    @task(3 if RACE_CONDITION_TESTING else 0)
    def test_limited_discount_race_condition(self):
        """Test limited discount codes for race conditions"""
        if not RACE_CONDITION_TESTING:
            return

        # Focus on limited codes for race testing
        limited_codes = ["FLASH50", "SAVE20", "RACE50"]
        discount_code = random.choice(limited_codes)

        log_discount_event("TESTING_RACE_CONDITION", discount_code)

        # Clear basket and add items
        self.client.delete("/api/v1/basket/")

        # Add a few items quickly
        for _ in range(random.randint(1, 2)):
            product_url = get_random_data("product_urls")
            response = self.client.get("/api/v1/pages/find" + product_url)

            if response.status_code == 200:
                try:
                    variants = response.json().get("variants", [])
                    if variants:
                        variant = random.choice(variants)
                        item_data = {
                            "product_type": variant["product_type"],
                            "product_id": variant["id"],
                        }
                        self.client.post("/api/v1/basket/?basket", json=item_data)
                        break  # Only add one item for speed
                except (ValueError, KeyError, json.JSONDecodeError):
                    continue

        # Try to apply the discount
        self._apply_discount_code(discount_code, track_race_conditions=True)

    def _try_apply_discount(self):
        """Try to apply a random discount code"""
        if not ENABLE_DISCOUNT_TESTING:
            return

        # Mix of limited and unlimited codes
        if random.random() < 0.7:  # 70% unlimited codes
            discount_codes = ["UNLIMITED10", "STUDENT5"]
        else:  # 30% limited codes
            discount_codes = ["FLASH50", "SAVE20"]

        discount_code = random.choice(discount_codes)
        self._apply_discount_code(discount_code)

    def _apply_discount_code(
        self, discount_code: str, track_race_conditions: bool = False
    ):
        """Apply a discount code and track results"""
        discount_data = {"discount_code": discount_code}
        discount_response = self.client.post(
            "/api/v1/basket/discount/", json=discount_data
        )

        if discount_response.status_code == 200:
            discount_stats["successful_applications"] += 1
            log_discount_event("DISCOUNT_APPLIED_SUCCESS", discount_code)
            self.applied_discounts.append(discount_code)
        elif discount_response.status_code == 400:
            try:
                error_data = discount_response.json()
                error_message = error_data.get("error", "Unknown error")

                if (
                    track_race_conditions
                    and "no longer available" in error_message.lower()
                ):
                    discount_stats["race_condition_detected"] += 1
                    log_discount_event(
                        "RACE_CONDITION_DETECTED", discount_code, error_message
                    )
                else:
                    discount_stats["failed_applications"] += 1
                    log_discount_event("DISCOUNT_FAILED", discount_code, error_message)
            except json.JSONDecodeError:
                discount_stats["failed_applications"] += 1
                log_discount_event(
                    "DISCOUNT_FAILED", discount_code, "JSON decode error"
                )
        else:
            discount_stats["failed_applications"] += 1
            log_discount_event(
                "DISCOUNT_FAILED",
                discount_code,
                f"HTTP {discount_response.status_code}",
            )

    def _create_checkout(self):
        """
        Test checkout using dummy payment, available only for load-testing.
        """
        data = {
            "email": "<EMAIL>",
            "shipping_address": "Load\nTest\nKorzo 35\n51000\nRijeka\nHR\n\n+385922332183",
            "billing_address": "Load\nTest\nKorzo 35\n51000\nRijeka\nHR\n\n+385922332183",
            "payment_method": "dummy",
        }
        self.client.post("/api/v1/checkout/", json=data)


# Specialized user class for intensive race condition testing
class RaceConditionTester(FastHttpUser):
    """
    Aggressive race condition tester (active by default for comprehensive testing).
    """

    wait_time = between(0.5, 2)  # Moderate speed for balanced testing
    weight = 3 if RACE_CONDITION_TESTING else 0  # Active by default

    def on_start(self):
        self.target_discount = "RACE50"  # Focus on most limited discount
        self.successful_applications = 0

    @task(10)
    def rapid_discount_application(self):
        """Rapidly try to apply the same limited discount code"""
        if not RACE_CONDITION_TESTING:
            return

        # Clear basket and add one item quickly
        self.client.delete("/api/v1/basket/")

        product_url = get_random_data("product_urls")
        response = self.client.get("/api/v1/pages/find" + product_url)

        if response.status_code == 200:
            try:
                variants = response.json().get("variants", [])
                if variants:
                    variant = random.choice(variants)
                    item_data = {
                        "product_type": variant["product_type"],
                        "product_id": variant["id"],
                    }
                    add_response = self.client.post(
                        "/api/v1/basket/?basket", json=item_data
                    )

                    if add_response.status_code in [200, 201]:
                        # Immediately try to apply discount
                        discount_data = {"discount_code": self.target_discount}
                        discount_response = self.client.post(
                            "/api/v1/basket/discount/", json=discount_data
                        )

                        if discount_response.status_code == 200:
                            self.successful_applications += 1
                            log_discount_event(
                                "RACE_TESTER_SUCCESS",
                                self.target_discount,
                                f"Total: {self.successful_applications}",
                            )
                        elif discount_response.status_code == 400:
                            try:
                                error_data = discount_response.json()
                                error_message = error_data.get("error", "")
                                if "no longer available" in error_message.lower():
                                    log_discount_event(
                                        "RACE_TESTER_BLOCKED",
                                        self.target_discount,
                                        "Correctly blocked by race prevention",
                                    )
                            except json.JSONDecodeError:
                                pass

            except (ValueError, KeyError, json.JSONDecodeError):
                pass


# Event listeners for startup and statistics reporting
@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Print configuration when test starts"""
    print("\n" + "=" * 60)
    print("🚀 ENHANCED LOAD TESTING WITH DISCOUNT VALIDATION")
    print("=" * 60)
    print(
        f"Discount Testing: {'✅ ENABLED' if ENABLE_DISCOUNT_TESTING else '❌ DISABLED'}"
    )
    print(
        f"Race Condition Testing: {'✅ ENABLED' if RACE_CONDITION_TESTING else '❌ DISABLED'}"
    )
    print(f"Test Intensity: {DISCOUNT_TEST_INTENSITY}/5")
    print(f"Production Mode: {'✅ YES' if IS_PRODUCTION else '❌ NO'}")
    print()
    print("Testing Features:")
    if ENABLE_DISCOUNT_TESTING:
        print("  🎯 Automatic shipping discounts (€75 → 20%, €150 → Free)")
        print("  🎫 Manual discount codes with usage limits")
        print("  🔄 Mixed discount scenarios")
    if RACE_CONDITION_TESTING:
        print("  🏁 Race condition prevention validation")
        print("  ⚡ Concurrent discount code application")
    print("  📊 Comprehensive statistics and monitoring")
    print("=" * 60)


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Print discount testing statistics when test stops"""
    if not ENABLE_DISCOUNT_TESTING:
        return

    print("\n" + "=" * 60)
    print("DISCOUNT TESTING STATISTICS")
    print("=" * 60)
    print(f"Successful Applications: {discount_stats['successful_applications']}")
    print(f"Failed Applications: {discount_stats['failed_applications']}")
    print(f"Race Conditions Detected: {discount_stats['race_condition_detected']}")
    print(
        f"Automatic Discounts Applied: {discount_stats['automatic_discounts_applied']}"
    )
    print(f"Shipping Discounts Applied: {discount_stats['shipping_discounts_applied']}")

    total_attempts = (
        discount_stats["successful_applications"]
        + discount_stats["failed_applications"]
    )
    if total_attempts > 0:
        success_rate = (
            discount_stats["successful_applications"] / total_attempts
        ) * 100
        race_condition_rate = (
            discount_stats["race_condition_detected"] / total_attempts
        ) * 100
        print(f"Success Rate: {success_rate:.2f}%")
        print(f"Race Condition Rate: {race_condition_rate:.2f}%")

    print("=" * 60)
