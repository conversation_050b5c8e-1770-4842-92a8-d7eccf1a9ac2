from rest_framework.schemas.openapi import AutoSchema as BaseAutoSchema
from rest_framework.schemas.openapi import <PERSON>hema<PERSON><PERSON><PERSON> as BaseSchemaGenerator


class SchemaGenerator(BaseSchemaGenerator):
    """
    Overriden OpenAPI schema generator class that
    adds security definitions.

    Use it with schema generation command:
    ``python manage.py generateschema --generator_class=core.schemas.SchemaGenerator``
    """

    def get_schema(self, request=None, public=False):
        schema = super().get_schema(request, public)

        if "components" not in schema:
            schema["components"] = {}

        # Add auth definition.
        schema["components"]["securitySchemes"] = {
            "bearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
            }
        }
        schema["security"] = [{"bearerAuth": []}]

        return schema


class AutoSchema(BaseAutoSchema):
    """
    Overriden OpenAPI schema that limits filters to lists.
    """

    def allows_filters(self, path, method):
        value = super().allows_filters(path, method)
        if value and hasattr(self.view, "action"):
            return self.view.action in ["list"]
        return value
