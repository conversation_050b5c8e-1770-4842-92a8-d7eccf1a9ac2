from __future__ import annotations

import hashlib
import itertools
from typing import TYPE_CHECKING, Iterable, Optional
from urllib.parse import urlparse

from django.core.cache import cache
from django.core.cache.backends.base import DEFAULT_TIMEOUT
from rest_framework.request import Request
from rest_framework.response import Response

if TYPE_CHECKING:
    from .models import CorePage

__all__ = [
    "cache",
    "get_url_cache_key",
    "get_response_from_cache_url",
    "get_response_from_cache",
    "cache_response_url",
    "cache_response",
    "invalidate_cached_response_url",
    "invalidate_cached_response",
]


def get_url_cache_key(url: str) -> tuple[str, Optional[str]]:
    """
    Returns cache key and base key for the given URL.
    Caret symbol `^` marks exact match and will result in `base_key` being None.
    """
    if url.startswith("^"):
        parsed = urlparse(url[1:])
        base_key = None
    else:
        parsed = urlparse(url)
        base_url = hashlib.md5(parsed.path.encode("ascii")).hexdigest()
        base_key = f"{base_url}:base"

    url = f"{parsed.path}?{parsed.query}".rstrip("?")
    key = hashlib.md5(url.encode("ascii")).hexdigest()
    return (key, base_key)


def get_response_from_cache_url(url: str) -> Optional[Response]:
    """
    Returns response from cache based on URL, or None if empty.
    """
    key = get_url_cache_key(url)[0]
    data = cache.get(key, None)
    return Response(data) if data else None


def get_response_from_cache(request: Request) -> Optional[Response]:
    """
    Returns response from cache based on request, or None if empty.
    """
    return get_response_from_cache_url(request.get_full_path())


def cache_response_url(url: str, response: Response, timeout: Optional[int] = None):
    """
    Cache response data based on URL.
    """
    key, base_key = get_url_cache_key(url)
    if base_key:
        keys = list(cache.get(base_key, []))
        keys.append(key)
        keys = list(set(keys))
        cache.set(base_key, keys, timeout=None)
    cache.set(key, response.data, timeout=timeout or DEFAULT_TIMEOUT)


def cache_response(request: Request, response: Response, timeout: Optional[int] = None):
    """
    Cache response data based on request.
    """
    cache_response_url(request.get_full_path(), response, timeout)


def invalidate_cached_response_url(url: str):
    """
    Invalidates response for a given URL.
    """
    key, base_key = get_url_cache_key(url)
    if base_key:
        keys = list(cache.get(base_key, []))
        keys.append(base_key)
        keys = list(set(keys))
        cache.delete_many(keys)
    else:
        cache.delete(key)


def invalidate_cached_response(request: Request):
    """
    Invalidates response for a given URL.
    """
    invalidate_cached_response_url(request.get_full_path())


def invalidate_cached_response_urls(urls: list[str]):
    """
    Ivalidates response for multiple given URLs.
    """
    keys = []
    base_keys = []
    for url in urls:
        key, base_key = get_url_cache_key(url)
        if base_key:
            base_keys.append(base_key)
        else:
            keys.append(key)
    if base_keys:
        keys += itertools.chain(*cache.get_many(base_keys).values(), base_keys)
    keys = list(set(keys))
    cache.delete_many(keys)


def invalidate_cached_responses(requests: list[Request]):
    """
    Invalidates response for multiple given requests.
    """
    invalidate_cached_response_urls([request.get_full_path() for request in requests])


def invalidate_cached_responses_for_pages(pages: Iterable[CorePage]):
    """
    Invalidates responses for a given page list.
    """
    urls = []
    for page in pages:
        urls += page.get_cached_api_urls()
    urls = list(set(urls))
    invalidate_cached_response_urls(urls)
