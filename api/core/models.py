from __future__ import annotations

import secrets
from typing import Iterable
from urllib.parse import urlencode, urljoin

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.dispatch import receiver
from django.shortcuts import redirect
from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from wagtail.admin.edit_handlers import FieldPanel, MultiFieldPanel
from wagtail.core.models import Page
from wagtail.core.signals import page_published, page_unpublished, post_page_move
from wagtail.images.edit_handlers import ImageChooserPanel
from wagtail_localize.fields import SynchronizedField

from .cache import cache, invalidate_cached_response_urls

PREVIEW_TOKEN_KEY = "preview_token"


class RedirectPageMixin:
    """
    Mixin used on Page to instruct a redirect to another url.
    Defaults to first found subpage or raises 404.
    """

    def get_redirect_url(self, request):
        subpage = self.get_children().live().first()
        if subpage:
            return subpage.get_url(request=request)

    def serve(self, request, data):
        redirect_url = self.get_redirect_url(request)
        if redirect_url:
            data = {"redirect_url": redirect_url}
            return Response(data, status=status.HTTP_301_MOVED_PERMANENTLY)
        raise NotFound


class NotRoutablePageMixin:
    """
    Mixin used on Page to make it not routable.
    Page is still displayed in a listing API view.
    """

    def get_url(self, request):
        return None

    def get_full_url(self, request):
        return None

    def serve(self, request, data):
        raise NotFound


class CorePage(Page):
    """
    Core page for the project, should be used as base for other pages.
    """

    search_image = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.SET_NULL,
        verbose_name="Search image",
        help_text="The image used for snippet display in search engine results.",
    )

    _menu_title = models.CharField(
        "Menu title",
        max_length=64,
        blank=True,
        help_text="Title used in menus, defaults to page title.",
    )

    show_in_footer_menus = models.BooleanField(
        verbose_name="show in footer menus",
        default=False,
        help_text=(
            "Whether a link to this page will appear in automatically "
            "generated footer menus"
        ),
    )

    reverse_id = models.CharField(
        "Reverse ID",
        max_length=64,
        blank=True,
        null=True,
        db_index=True,
        help_text="A unique ID used to idetify this page.",
    )

    override_translatable_fields = [
        SynchronizedField("reverse_id"),
    ]

    promote_panels = [
        MultiFieldPanel(
            [
                FieldPanel("slug"),
                FieldPanel("seo_title"),
                FieldPanel("search_description"),
                ImageChooserPanel("search_image"),
            ],
            heading="For search engines",
        ),
        MultiFieldPanel(
            [
                FieldPanel("show_in_menus"),
                FieldPanel("show_in_footer_menus"),
                FieldPanel("_menu_title"),
            ],
            heading="For site menus",
        ),
    ]

    # Remove `Privacy` panel from settings and add custom fields
    settings_panels = Page.settings_panels[:1] + [
        FieldPanel("reverse_id"),
    ]

    is_creatable = False

    # Customize page serializers.
    page_serializer = "core.serializers.PageSerializer"
    page_summary_serializer = "core.serializers.PageSummarySerializer"
    page_submit_serializer = "core.serializers.PageSubmitSerializer"

    def save(self, *args, **kwargs):
        # Force to null to keep unique constraint.
        if self.reverse_id == "":
            self.reverse_id = None
        super().save(*args, **kwargs)

    def clean(self):
        super().clean()
        if (
            self.reverse_id
            and CorePage.objects.filter(reverse_id=self.reverse_id, locale=self.locale)
            .exclude(id=self.id)
            .exists()
        ):
            raise ValidationError({"reverse_id": "This ID is already in use."})

    @property
    def search_title(self):
        return self.seo_title or self.title

    @property
    def menu_title(self):
        return self._menu_title or self.title

    def get_url(self, request=None, current_site=None):
        """
        If page is an alias of another page, redirect page url.
        """
        if self.alias_of_id and self.alias_of:
            return self.alias_of.get_url(request, current_site)
        return super().get_url(request, current_site)

    def get_menu_pages(self, request) -> Iterable[CorePage]:
        """
        Should return pages representing menu items for this page.
        """

    def get_footer_menu_pages(self, request) -> Iterable[CorePage]:
        """
        Should return pages representing footer menu items for this page.
        """

    def serve(self, request, data):
        """
        Optionally override page details API view.
        """
        if self.alias_of_id and self.alias_of:
            redirect_url = self.alias_of.get_url(request)
            if redirect_url:
                data = {"redirect_url": redirect_url}
                return Response(data, status=status.HTTP_301_MOVED_PERMANENTLY)
            raise NotFound
        return Response(data)

    def serve_preview(self, request, mode_name):
        """
        Store a preview token in cache that lasts 5 min and redirect to page front-end.
        """
        new_token = secrets.token_urlsafe()
        tokens = list(cache.get(PREVIEW_TOKEN_KEY, []))
        tokens.append(new_token)
        cache.set(PREVIEW_TOKEN_KEY, tokens, timeout=300)
        url = urljoin(settings.SITE_URL, self.url_path)
        query = urlencode({PREVIEW_TOKEN_KEY: new_token})
        return redirect(f"{url}?{query}")

    def get_cached_api_urls(self) -> list[str]:
        """
        Method that returns a list of urls to be invalidated during page publish / move.
        """
        pages_url = reverse("page-list")
        config_url = reverse("config-list")
        urls = [config_url, pages_url]
        items = list(self.get_descendants(inclusive=True).values_list("id", "url_path"))
        items += list(self.get_translations().values_list("id", "url_path"))
        for id, url_path in items:
            urls += [f"{pages_url}{id}/", f"{pages_url}find{url_path}"]
        return urls


def get_and_invalidate_cached_api_urls(instance: CorePage):
    """
    Helper method that gets and invalidates API urls for a given page instance.
    """
    try:
        urls = instance.specific_deferred.get_cached_api_urls()
        invalidate_cached_response_urls(urls)
    except AttributeError:
        pass


@receiver(page_published, dispatch_uid="core_page_published")
@receiver(page_unpublished, dispatch_uid="core_page_unpublished")
@receiver(post_page_move, dispatch_uid="core_page_move")
def on_page_update(sender, **kwargs):
    get_and_invalidate_cached_api_urls(kwargs["instance"])
