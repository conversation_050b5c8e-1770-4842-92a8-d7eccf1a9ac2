{% extends "modeladmin/index.html" %}
{% load modeladmin_tags wagtailadmin_tags %}

{% block header_extra %}
  <div class="right">
    {% if locale %}
      <div class="col">
        {% include 'wagtailadmin/shared/locale_selector.html' with class='c-dropdown--large' %}
      </div>
    {% endif %}
    {% if user_can_create %}
      <div class="actionbutton col">
        {% include 'modeladmin/includes/button.html' with button=view.button_helper.add_button %}
      </div>
    {% endif %}
    {% if view.list_export %}
      <div class="dropdown dropdown-button match-width col">
        <a href="?export=xlsx&{{ request.GET.urlencode }}" class="button bicolor button--icon">{% icon name="download" wrapped=1 %}Download XLSX</a>
        <div class="dropdown-toggle">{% icon name="arrow-down" %}</div>
        <ul>
          <li><a class="button bicolor button--icon" href="?export=csv&{{ request.GET.urlencode }}">{% icon name="download" wrapped=1 %}Download CSV</a></li>
        </ul>
      </div>
    {% endif %}
  </div>
{% endblock %}
