{% extends "modeladmin/edit.html" %}
{% load i18n wagtailadmin_tags %}

{% block header %}
  {% include "wagtailadmin/shared/header_with_locale_selector.html" with title=editing_str subtitle=instance icon="snippet" tabbed=1 merged=1 %}
{% endblock %}

{% block form_actions %}
  <div class="dropdown dropup dropdown-button match-width">
    <button type="submit" class="button action-save button-longrunning" data-clicked-text="Saving…">
      {% icon name="spinner" %}<em>Save</em>
    </button>

      <div class="dropdown-toggle">{% icon name="arrow-up" %}</div>
      <ul>
        {% if is_translation %}
          <li>
            <form method="POST">
              <input type="hidden" name="localize-restart-translation">
              <button class="button">Restart translation</button>
            </form>
          </li>
        {% endif %}

        {% if user_can_delete %}
          <li><a href="{{ view.delete_url }}" class="shortcut">Delete</a></li>
        {% endif %}

        {% for button in action_buttons %}
          <li><a href="{{ button.url }}" class="{{ button.classes }}" title="{{ button.title }}">{{ button.label }}</a></li>
        {% endfor %}
      </ul>
  </div>
{% endblock %}
