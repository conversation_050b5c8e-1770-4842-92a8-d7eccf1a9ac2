from urllib.parse import urljoin

from django.conf import settings
from storages.backends.gcloud import GoogleCloudStorage as BaseGoogleCloudStorage


class GoogleCloudStorage(BaseGoogleCloudStorage):
    """
    Google Cloud Storage class used to store both
    media and static files in the same bucket.
    """

    location = None
    base_url = None

    def __init__(self, *args, **kwargs):
        assert self.location is not None, "Must specify `location`."
        assert self.base_url is not None, "Must specify `base_url`."
        kwargs["location"] = self.location
        super().__init__(*args, **kwargs)

    def url(self, name):
        return urljoin(self.base_url, name)


class GoogleCloudMediaStorage(GoogleCloudStorage):
    location = settings.MEDIA_LOCATION
    base_url = settings.MEDIA_URL


class GoogleCloudStaticStorage(GoogleCloudStorage):
    location = settings.STATIC_LOCATION
    base_url = settings.STATIC_URL
