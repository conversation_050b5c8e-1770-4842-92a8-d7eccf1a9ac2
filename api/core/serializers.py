import json
from typing import Any

from rest_framework import serializers
from rest_framework.exceptions import MethodNotAllowed, NotFound
from rest_framework.reverse import reverse
from rest_framework.utils.encoders import <PERSON><PERSON><PERSON>ncoder
from wagtail.core.rich_text import expand_db_html
from wagtail.documents.models import Document
from wagtail.images.api.fields import ImageR<PERSON><PERSON><PERSON>ield as BaseImageRenditionField
from wagtail.images.models import Image

from .models import CorePage


def force_dict(data):
    return json.loads(json.dumps(data, cls=JSONEncoder))


def serializer_to_dict(serializer):
    return force_dict(serializer.data)


class PageTypeField(serializers.CharField):
    def get_attribute(self, instance):
        return instance

    def to_representation(self, page):
        if page.specific_class is None:
            return None
        return page.specific_class._meta.app_label + "." + page.specific_class.__name__


class PageLocaleField(serializers.CharField):
    def get_attribute(self, instance):
        return instance

    def to_representation(self, page):
        try:
            return page.url_path.split("/")[1]
        except IndexError:
            pass


class RichTextField(serializers.CharField):
    def to_representation(self, value):
        return expand_db_html(value)


class ImageRenditionField(BaseImageRenditionField):
    def to_representation(self, image):
        data = super().to_representation(image)
        if "request" in self.context and "url" in data:
            request = self.context["request"]
            data["url"] = request.build_absolute_uri(data["url"])
        return data


class LabelField(serializers.CharField):
    def __init__(self, value, **kwargs):
        self.value = value
        super().__init__(**kwargs)

    def get_attribute(self, instance):
        return self.value


class LabelsField(serializers.DictField):
    def __init__(self, *values: tuple[str, Any], **kwargs):
        self.data = {key: value for key, value in values}
        super().__init__(self, **kwargs)

    def get_attribute(self, instance):
        return self.data


class ImageSerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField()

    class Meta:
        model = Image
        fields = ["url", "title", "width", "height"]

    def get_url(self, obj):
        request = self.context["request"]
        return request.build_absolute_uri(obj.file.url)


class DocumentSerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField()

    class Meta:
        model = Document
        fields = ["url", "title"]

    def get_url(self, obj):
        request = self.context["request"]
        return request.build_absolute_uri(obj.file.url)


class PageSummarySerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField()
    url_path = serializers.SerializerMethodField()
    url_full = serializers.SerializerMethodField()
    live_at = serializers.DateTimeField(source="go_live_at")

    type = PageTypeField()
    locale = PageLocaleField()

    class Meta:
        model = CorePage
        fields = [
            "id",
            "url",
            "url_path",
            "url_full",
            "type",
            "live",
            "live_at",
            "has_unpublished_changes",
            "locale",
            "slug",
            "title",
        ]

    def get_url_path(self, obj):
        request = self.context.get("request", None)
        return obj.specific_deferred.get_url(request=request)

    def get_url_full(self, obj):
        request = self.context.get("request", None)
        return obj.specific_deferred.get_full_url(request=request)

    def get_url(self, obj):
        request = self.context.get("request", None)
        url = reverse("page-detail", args=[obj.id])
        return request.build_absolute_uri(url) if request else url


class PageSearchSerializer(PageSummarySerializer):
    search_image = ImageSerializer()

    class Meta:
        model = CorePage
        fields = PageSummarySerializer.Meta.fields + [
            "search_title",
            "search_description",
            "search_image",
        ]


class PageSerializer(PageSearchSerializer):
    translated_urls = serializers.SerializerMethodField()

    class Meta:
        model = CorePage
        fields = PageSearchSerializer.Meta.fields + ["translated_urls"]

    def get_translated_urls(self, obj):
        request = self.context.get("request", None)
        return {
            page.locale.language_code: page.get_url(request=request)
            for page in obj.get_translations().select_related("locale")
        }


class PageSubmitSerializer(serializers.Serializer):
    def save(self, page):
        raise MethodNotAllowed("POST")


class PageMenuSerializer(serializers.ModelSerializer):
    title = serializers.CharField(source="menu_title")
    url = serializers.SerializerMethodField()
    children = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        self.with_children = kwargs.pop("with_children", True)
        self.get_child_pages_method = kwargs.pop(
            "get_child_pages_method",
            "get_menu_pages",
        )
        super().__init__(*args, **kwargs)

    class Meta:
        model = CorePage
        fields = ["title", "url", "children"]

    def get_url(self, obj):
        request = self.context["request"]
        try:
            res = obj.serve(request, {})
        except NotFound:
            return None
        if res and "redirect_url" in res.data:
            return res.data["redirect_url"]
        return obj.get_url(request=self.context["request"])

    def get_children(self, obj):
        if self.with_children:
            request = self.context["request"]
            data = getattr(obj.specific_deferred, self.get_child_pages_method)(request)
            if data:
                serializer = PageMenuSerializer(context=self.context, many=True)
                return serializer.to_representation(data)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if not self.with_children:
            del data["children"]
        return data
