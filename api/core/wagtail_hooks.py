from django.apps import apps
from django.core.cache import cache
from django.shortcuts import redirect
from django.urls import path, reverse
from wagtail.admin.action_menu import ActionMenuItem
from wagtail.admin.menu import MenuItem
from wagtail.admin.messages import messages
from wagtail.core import hooks
from wagtail.snippets.models import get_snippet_models
from wagtail.snippets.views import snippets
from wagtail_localize.wagtail_hooks import page_listing_more_buttons

from .modeladmin.views import SubmitModelAdminTranslationView


class ConfirmMenuItem(MenuItem):
    template = "wagtailadmin/shared/confirm_menu_item.html"

    def __init__(self, *args, **kwargs):
        self.confirm_message = kwargs.pop("confirm_message", "Are you sure?")
        super().__init__(*args, **kwargs)

    def get_context(self, request):
        context = super().get_context(request)
        context["confirm_message"] = self.confirm_message
        return context


@hooks.register("register_settings_menu_item")
def add_clear_cache_menu_item():
    return ConfirmMenuItem(
        "Clear cache",
        reverse("clear_cache"),
        icon_name="undo",
        order=10000,
        confirm_message="Do you want to fully clear site cache?",
    )


def clear_cache_view(request):
    cache.clear()
    messages.success(request, "Site cache successfully cleared.")
    return redirect("wagtailadmin_home")


@hooks.register("register_admin_urls")
def register_admin_urls():
    return [
        path("clear_cache/", clear_cache_view, name="clear_cache"),
        path(
            "submit-modeladmin-translation/<slug:app_label>/<slug:model_name>/<str:pk>/",
            SubmitModelAdminTranslationView.as_view(),
            name="submit_modeladmin_translation",
        ),
        path(
            "snippets/<slug:app_label>/<slug:model_name>/edit/<str:pk>/",
            SnippetViews.edit,
            name="snippet_edit",
        ),
        path(
            "snippets/<slug:app_label>/<slug:model_name>/delete/<str:pk>/",
            SnippetViews.delete,
            name="snippet_delete",
        ),
    ]


class TranslationMenuItem(ActionMenuItem):
    icon_name = "site"

    def __init__(self, url, label, order=None):
        self.url = url
        self.label = label
        super().__init__(order=order)

    def get_url(self, request, context):
        return f"{self.url}?next={request.path}"


@hooks.register("construct_page_action_menu")
def add_copy_page_action(menu_items, request, context):
    """
    Add "Copy" button to the page edit.
    """

    if "page" in context:

        class CopyActionMenuItem(ActionMenuItem):
            label = "Copy"
            icon_name = "repeat"

            def get_url(self, *args):
                return reverse("wagtailadmin_pages:copy", args=[context["page"].id])

        menu_items.insert(2, CopyActionMenuItem())


@hooks.register("construct_page_action_menu")
def add_translation_actions(menu_items, request, context):
    """
    Add translation actions from page listing to the page edit view.
    """
    page = context.get("page", None)

    if page:
        page_perms = page.permissions_for_user(request.user)
        buttons = page_listing_more_buttons(page, page_perms)

        for button in buttons:
            item = TranslationMenuItem(button.url, button.label)
            menu_items.append(item)


@hooks.register("after_edit_page")
def redirect_to_edit_view(request, page):
    """
    Stay on edit view even when page is published.
    """
    return redirect("wagtailadmin_pages:edit", page.id)


class SnippetViews:
    """
    Overriden snippet views that redirect to modeladmin instance if model is not
    registered as snippet. This is a hack to make modeladmin translations work as if
    they are snippets since `wagtail_localize` does not support modeladmin translations.
    """

    @staticmethod
    def is_snippet_model(app_label, model_name):
        try:
            model = apps.get_model(app_label, model_name)
            return model in get_snippet_models()
        except LookupError:
            return False

    @classmethod
    def edit(cls, request, app_label, model_name, pk):
        if not cls.is_snippet_model(app_label, model_name):
            return redirect(f"{app_label}_{model_name}_modeladmin_edit", pk)
        return snippets.edit(request, app_label, model_name, pk)

    @classmethod
    def delete(cls, request, app_label, model_name, pk):
        if not cls.is_snippet_model(app_label, model_name):
            return redirect(f"{app_label}_{model_name}_modeladmin_delete", pk)
        return snippets.delete(request, app_label, model_name, pk)
