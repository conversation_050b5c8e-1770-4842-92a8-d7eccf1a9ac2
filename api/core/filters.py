from django.conf import settings
from django.http.response import Http404
from django.utils.translation import get_language
from django_filters.rest_framework import filters, filterset
from rest_framework.filters import BaseFilterBackend
from rest_framework.filters import OrderingFilter as BaseOrderingFilter
from wagtail.api.v2.filters import SearchFilter as BaseSearchFilter


class HelpFilter(filters.Filter):
    """
    Filter used as placeholder to display help for non-supported filter types.
    """

    def filter(self, qs, value):
        return qs


class HelpChoiceFilter(filters.ChoiceFilter, HelpFilter):
    """
    Same as `HelpFilter` but allows for choices to be passed in.
    """


class LocaleFilter(BaseFilterBackend):
    """
    Default django's localization is used, to select a language
    you shoud pass in "Accept-Language" param in request header or supply `locale`
    in the querystring.
    """

    def filter_queryset(self, request, queryset, view):
        if getattr(settings, "WAGTAIL_I18N_ENABLED", False):

            _filtered_by_child_of = getattr(queryset, "_filtered_by_child_of", None)

            language = get_language()
            languages = getattr(settings, "WAGTAIL_CONTENT_LANGUAGES", [])
            if language not in [x[0] for x in languages]:
                raise Http404
            queryset = queryset.filter(locale__language_code=language)

            if _filtered_by_child_of:
                queryset._filtered_by_child_of = _filtered_by_child_of

        return queryset


class OrderingFilter(BaseOrderingFilter):
    ordering_param = "order"


class SearchFilter(BaseSearchFilter):
    def filter_queryset(self, request, queryset, view):
        return super().filter_queryset(request, queryset, view)


class PageFilterSet(filterset.FilterSet):
    """
    Default Page FilterSet.
    """
