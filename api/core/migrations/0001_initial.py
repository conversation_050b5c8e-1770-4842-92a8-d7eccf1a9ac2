# Generated by Django 3.2.7 on 2021-09-15 14:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('wagtailimages', '0023_add_choose_permissions'),
        ('wagtailcore', '0062_comment_models_and_pagesubscription'),
    ]

    operations = [
        migrations.CreateModel(
            name='CorePage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('_menu_title', models.CharField(blank=True, help_text='Title used in menus, defaults to page title.', max_length=64, verbose_name='Menu title')),
                ('reverse_id', models.CharField(blank=True, db_index=True, help_text='A unique ID used to idetify this page.', max_length=64, null=True, verbose_name='Reverse ID')),
                ('search_image', models.ForeignKey(blank=True, help_text='The image used for snippet display in search engine results.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='wagtailimages.image', verbose_name='Search image')),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
    ]
