from typing import Optional

from django.db import DEFAULT_DB_ALIAS

_forced_db_alias = None


def set_forced_db_alias(value: str = DEFAULT_DB_ALIAS) -> None:
    global _forced_db_alias
    _forced_db_alias = value


def get_forced_db_alias() -> Optional[str]:
    return _forced_db_alias


class PrimaryReplicaRouterMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith("/admin/"):
            set_forced_db_alias()
        response = self.get_response(request)
        return response


class PrimaryReplicaRouter:
    def db_for_read(self, model, **hints):
        return get_forced_db_alias() or "replica1"

    def db_for_write(self, model, **hints):
        db = get_forced_db_alias() or "default"
        instance = hints.get("instance", None)
        if instance and instance._state.db:
            instance._state.db = db
        return db

    def allow_relation(self, obj1, obj2, **hints):
        return True

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        return True
