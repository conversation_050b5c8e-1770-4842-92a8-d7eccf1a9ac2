from django.apps import apps
from django.contrib.admin.utils import quote, unquote
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.http.response import Http404
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.utils.functional import cached_property
from wagtail.admin import messages
from wagtail.contrib.modeladmin.forms import ParentChooserForm
from wagtail.contrib.modeladmin.views import (
    ChooseParentView,
    CreateView,
    DeleteView,
    EditView,
    IndexView,
)
from wagtail.core.models import Locale, TranslatableMixin
from wagtail_localize.models import Translation, TranslationSource
from wagtail_localize.views import edit_translation, submit_translations

from .helpers import get_translation_listing_buttons


class TranslatableViewMixin:
    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.bind_locale(request)
        return super().dispatch(request, *args, **kwargs)

    def bind_locale(self, request):
        """
        Bind locale to `view` and `model_admin` from instance, request or default.
        """
        if hasattr(self, "instance") and self.instance:
            self.locale = getattr(self.instance, "locale", None)
        elif "locale" in request.GET:
            try:
                self.locale = Locale.objects.get(language_code=request.GET["locale"])
            except Locale.DoesNotExist:
                return redirect(request.path + f"?locale={request.LANGUAGE_CODE}")
        else:
            self.locale = Locale.get_active()
        self.model_admin.locale = self.locale

    def get_locale_action_url(self, action, *args, locale=None, **kwargs):
        locale = locale or self.locale
        return (
            self.url_helper.get_action_url(action, *args, **kwargs)
            + f"?locale={locale.language_code}"
        )

    def get_translations_context_data(self):
        return []

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["locale"] = self.locale
        context["translations"] = self.get_translations_context_data()
        return context

    @cached_property
    def index_url(self):
        return self.get_locale_action_url("index")

    @cached_property
    def create_url(self):
        return self.get_locale_action_url("create")


class TranslatableIndexView(TranslatableViewMixin, IndexView):
    def get_filters(self, request):
        filters = super().get_filters(request)
        filters[2]["locale"] = self.locale.id
        return filters

    def get_template_names(self):
        return "modeladmin/translatable_index.html"

    def get_translations_context_data(self):
        return [
            {"locale": x, "url": self.get_locale_action_url("index", locale=x)}
            for x in Locale.objects.exclude(id=self.locale.id)
        ]


class TranslatableCreateView(TranslatableViewMixin, CreateView):
    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.bind_locale(request)
        if self.is_pagemodel:
            user = request.user
            parents = self.permission_helper.get_valid_parent_pages(user)
            parents = parents.filter(locale_id=self.locale.id)
            parent_count = parents.count()
            if parent_count == 1:
                parent = parents.get()
                parent_pk = quote(parent.pk)
                return redirect(
                    self.get_locale_action_url(
                        "add",
                        self.app_label,
                        self.model_name,
                        parent_pk,
                    )
                )
            return redirect(self.get_locale_action_url("choose_parent"))
        return super().dispatch(request, *args, **kwargs)

    def get_template_names(self):
        return "modeladmin/translatable_create.html"

    def get_translations_context_data(self):
        return [
            {"locale": x, "url": self.get_locale_action_url("create", locale=x)}
            for x in Locale.objects.exclude(id=self.locale.id)
        ]

    def get_success_url(self):
        return self.get_locale_action_url("index")

    def form_valid(self, form):
        instance = form.save(commit=False)
        instance.locale = self.locale
        instance.save()
        messages.success(
            self.request,
            self.get_success_message(instance),
            buttons=self.get_success_message_buttons(instance),
        )
        return redirect(self.get_success_url())


class TranslatableEditView(TranslatableViewMixin, EditView):
    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        instance = self.instance

        if not isinstance(instance, TranslatableMixin):
            raise ValueError(
                "Admin view `TranslatableEditView` must be used on "
                "`TranslatableMixin` models."
            )

        self.translation = Translation.objects.filter(
            source__object_id=instance.translation_key,
            target_locale_id=instance.locale_id,
        ).first()

        # Check if the user has clicked the "Restart Translation" menu item
        if (
            self.translation
            and not self.translation.enabled
            and request.method == "POST"
            and "localize-restart-translation" in request.POST
        ):
            edit_translation.restart_translation(request, self.translation, instance)

        # Override the edit view if the instance is the target of a translation
        if self.translation and self.translation.enabled:
            try:
                return edit_translation.edit_translation(
                    request, self.translation, instance
                )
            except self.model.DoesNotExist:
                # Translation objects missing, delete
                self.translation.delete()

        return super().dispatch(request, *args, **kwargs)

    def get_template_names(self):
        return "modeladmin/translatable_edit.html"

    def get_translations_context_data(self):
        return [
            {"locale": x.locale, "url": self.url_helper.get_action_url("edit", x.pk)}
            for x in self.instance.get_translations().select_related("locale")
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["is_translation"] = bool(self.translation)
        context["action_buttons"] = (
            get_translation_listing_buttons(self.instance, self.request.user)
            if not self.translation
            else []
        )
        return context


class TranslatableDeleteView(TranslatableViewMixin, DeleteView):
    @transaction.atomic
    def delete_instance(self):
        # Delete translations if source
        source = TranslationSource.objects.get_for_instance_or_none(self.instance)
        if source:
            source.translations.all().delete()
        self.instance.delete()


class TranslatableChooseParentView(TranslatableViewMixin, ChooseParentView):
    def get_form(self, request):
        parents = self.permission_helper.get_valid_parent_pages(request.user)
        parents = parents.filter(locale_id=self.locale.id)
        return ParentChooserForm(parents, request.POST or None)


class SubmitModelAdminTranslationView(submit_translations.SubmitSnippetTranslationView):
    def get_object(self):
        try:
            model = apps.get_model(self.kwargs["app_label"], self.kwargs["model_name"])
        except LookupError:
            raise Http404
        if not issubclass(model, TranslatableMixin):
            raise Http404
        return get_object_or_404(model, pk=unquote(self.kwargs["pk"]))

    def get_default_success_url(self):
        url = "{app_label}_{model_name}_modeladmin_edit".format(**self.kwargs)
        return reverse(url, args=[self.kwargs["pk"]])
