from dataclasses import dataclass
from typing import Iterator

from django.contrib.admin.utils import quote
from django.urls import reverse
from wagtail.contrib.modeladmin.helpers import ButtonHelper
from wagtail.core.models import Locale, Page, TranslatableMixin
from wagtail_localize.models import TranslationSource


class TranslatableButtonHelper(ButtonHelper):
    def add_button(self, *args, **kwargs):
        btn = super().add_button(*args, **kwargs)
        btn["url"] = self.view.get_locale_action_url("create")
        return btn

    def get_buttons_for_obj(self, obj, **kwargs):
        user = self.request.user
        btns = super().get_buttons_for_obj(obj, **kwargs)
        btns += list(get_translation_listing_buttons(obj, user))
        return btns


@dataclass
class ListingButton:
    url: str
    label: str
    title: str = ""
    classname: str = "button button-small button-secondary"

    def __post_init__(self):
        if not self.title:
            self.title = self.label


def get_translation_listing_buttons(obj, user) -> Iterator[ListingButton]:
    """
    Returns listing buttons generator for translations of object.
    """
    if not isinstance(obj, TranslatableMixin) or not user.has_perm(
        "wagtail_localize.submit_translation"
    ):
        return

    if isinstance(obj, Page):
        has_locale_to_translate_to = Locale.objects.exclude(
            id__in=obj.get_translations(inclusive=True)
            .exclude(alias_of__isnull=False)
            .values_list("locale_id", flat=True)
        ).exists()
    else:
        has_locale_to_translate_to = Locale.objects.exclude(
            id__in=obj.get_translations(inclusive=True).values_list(
                "locale_id", flat=True
            )
        ).exists()

    if has_locale_to_translate_to:
        app_label, model_name = (obj._meta.app_label, obj._meta.model_name)
        url = reverse(
            "submit_modeladmin_translation", args=[app_label, model_name, quote(obj.pk)]
        )
        yield ListingButton(
            url,
            "Translate",
            "Translate this %s" % obj._meta.verbose_name,
        )

    # If the obj is the source for translations, show "Sync translated" button
    source = TranslationSource.objects.get_for_instance_or_none(obj)
    if source is not None and source.translations.filter(enabled=True).exists():
        url = reverse("wagtail_localize:update_translations", args=[source.id])
        url += f'?next={reverse("wagtailadmin_pages:edit", args=[obj.id])}'
        yield ListingButton(url, "Sync translated %s" % obj._meta.verbose_name_plural)
