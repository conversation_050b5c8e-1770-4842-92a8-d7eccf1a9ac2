from django.contrib.admin import Simple<PERSON>istFilter
from django.core.exceptions import ImproperlyConfigured


class ParentPageListFilter(SimpleListFilter):
    """
    List filter that enables filtering objects by their parent page,
    particulary useful when pages are used as a category layer.
    """

    def __init__(self, request, params, model, model_admin):
        self.parameter_name = getattr(
            model_admin, "parent_page_parameter_name", "parent"
        )
        self.title = getattr(model_admin, "parent_page_title", "Parent")
        self.locale = getattr(model_admin, "locale", None)
        super().__init__(request, params, model, model_admin)

    def lookups(self, request, model_admin):
        try:
            self.parent_page_model = model_admin.parent_page_model
        except AttributeError:
            raise ImproperlyConfigured(
                "The `parent_page_model` attribute is required when using "
                "`ParentPageListFilter`. Missing on `%s` class."
                % model_admin.__class__.__name__
            )

        pages = self.parent_page_model._default_manager.all()
        if self.locale:
            pages = pages.filter(locale_id=self.locale.id)

        lookups = []
        if pages.count():
            root_depth = pages.first().depth
            for page in pages:
                indent_str = "-" * (page.depth - root_depth)
                choice = (page.id, "%s %s" % (indent_str, page))
                lookups.append(choice)
        return lookups

    def queryset(self, request, queryset):
        if self.value():
            try:
                parent = self.parent_page_model._default_manager.get(id=self.value())
                queryset = queryset.filter(path__startswith=parent.path)
            except self.parent_page_model.DoesNotExist:
                queryset = queryset.none()
        return queryset
