from wagtail.contrib.modeladmin.options import ModelAdmin

from .helpers import TranslatableButtonHelper
from .views import (
    TranslatableChooseParentView,
    TranslatableCreateView,
    TranslatableDeleteView,
    TranslatableEditView,
    TranslatableIndexView,
)


class TranslatableModelAdmin(ModelAdmin):
    index_view_class = TranslatableIndexView
    create_view_class = TranslatableCreateView
    edit_view_class = TranslatableEditView
    delete_view_class = TranslatableDeleteView
    choose_parent_view_class = TranslatableChooseParentView
    button_helper_class = TranslatableButtonHelper
