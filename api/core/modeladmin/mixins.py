from typing import Optional

from django.core.exceptions import ImproperlyConfigured
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from wagtail.contrib.modeladmin.mixins import ThumbnailMixin as BaseThumbnailMixin


class TitleLinkMixin:
    """
    Add's link and bolds the title of an object. Optionally adds indentadion
    based on depth property, to use it add `admin_title` to the `list_display`
    property of your `ModelAdmin`.
    """

    title_field_name = "title"
    title_col_header_text = "Title"
    title_indent_depth = False
    title_indent_root_depth: Optional[int] = None
    title_indent_field_name = "depth"

    def get_extra_class_names_for_field_col(self, obj, field_name):
        if field_name == "admin_title":
            return ["title"]
        return []

    def get_title_indent_root_depth(self):
        if self.title_indent_root_depth is None:
            # Get root depth from first object in a queryset.
            first_obj = self.model._default_manager.all().first()
            if first_obj:
                try:
                    self.title_indent_root_depth = getattr(
                        first_obj, self.title_indent_field_name
                    )
                except AttributeError:
                    raise ImproperlyConfigured(
                        "The `title_indent_field_name` attribute on your `%s` class "
                        "must name a field on your model." % self.__class__.__name__
                    )
        return self.title_indent_root_depth

    def admin_title(self, obj):
        try:
            title = getattr(obj, self.title_field_name)
        except AttributeError:
            raise ImproperlyConfigured(
                "The `title_field_name` attribute on your `%s` class "
                "must name a field on your model." % self.__class__.__name__
            )

        if callable(title):
            title = title()

        if self.title_indent_depth:
            # Add dashes for indent depth level
            depth = getattr(obj, self.title_indent_field_name)
            root_depth = self.get_title_indent_root_depth()

            indent_str = "-" * (depth - root_depth)
            title = ("%s %s" % (indent_str, title)).lstrip()

        url = self.url_helper.get_action_url("edit", obj.id)
        return format_html(
            '<div class="title-wrapper"><a href="{}">{}</a></div>', url, title
        )

    admin_title.short_description = title_col_header_text  # type: ignore


class ThumbnailMixin(BaseThumbnailMixin):
    """
    Add's thumbnail display, to use it add `admin_thumb` to the `list_display`
    property of your `ModelAdmin`.
    """

    thumb_image_filter_spec = "max-76x76"
    thumb_image_width = "auto"
    thumb_image_full_height = False

    def admin_thumb(self, obj):
        img = super().admin_thumb(obj)
        inner = '<div style="position:absolute;top:-0.5em;width:76px;height:76px;">'
        return mark_safe(f'<div style="position:relative">{inner}{img}</div></div>')

    admin_thumb.short_description = BaseThumbnailMixin.thumb_col_header_text  # type: ignore


class ExcludeAliasesMixin:
    """
    Excludes pages that are alias of another page from the queryset.
    """

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if self.is_pagemodel:
            qs = qs.exclude(alias_of__isnull=False)
        return qs
