from rest_framework.throttling import UserRateThrottle


class BurstRateThrottle(UserRateThrottle):
    scope = "burst"


class SustainedRateThrottle(UserRateThrottle):
    scope = "sustained"


class PageSubmitThrottle(UserRateThrottle):
    scope = "page_submit"

    def allow_request(self, request, view):
        if request.method.lower() != "post":
            return True

        # Disable throttle if serializer data not valid.
        view.get_object()
        serializer = view.get_serializer(data=request.data)
        if not serializer.is_valid():
            return True

        return super().allow_request(request, view)
