from django.apps import apps
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.utils.module_loading import import_string
from django_filters.rest_framework.backends import DjangoFilterBackend
from rest_framework import mixins, status
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, ParseError
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet
from wagtail.api.v2.filters import (
    ChildOfFilter,
    DescendantOfFilter,
    TranslationOfFilter,
)
from wagtail.api.v2.utils import BadRequestError

from core import cache
from core.serializers import PageSearchSerializer

from .filters import <PERSON>e<PERSON>ilter, OrderingFilter, PageFilterSet, SearchFilter
from .models import PREVIEW_TOKEN_KEY, CorePage
from .throttling import PageSubmitThrottle


def optimize_queryset_related_fields(queryset, serializer_class):
    """
    Optimize queryset by pre-fetching related data specified
    on a serializer Meta class.
    """
    if hasattr(serializer_class, "Meta"):
        if hasattr(serializer_class.Meta, "select_related_fields"):
            fields = serializer_class.Meta.select_related_fields
            queryset = queryset.select_related(*fields)
        if hasattr(serializer_class.Meta, "prefetch_related_fields"):
            fields = serializer_class.Meta.prefetch_related_fields
            queryset = queryset.prefetch_related(*fields)
    return queryset


def is_preview_request(request) -> bool:
    """
    Returns if request is preview, used to disable cache or show non-live objects.
    """
    if not hasattr(request, "is_preview"):
        if PREVIEW_TOKEN_KEY in request.GET:
            tokens = list(cache.cache.get(PREVIEW_TOKEN_KEY, []))
            request.is_preview = request.GET[PREVIEW_TOKEN_KEY] in tokens
        else:
            request.is_preview = False
    return request.is_preview


class ListPageMixin(mixins.ListModelMixin):
    pass


class RetrievePageMixin(mixins.RetrieveModelMixin):
    def retrieve(self, request, *args, **kwargs):
        obj = self.get_object()
        serializer = self.get_serializer(obj)
        return obj.serve(request, serializer.data)


class SubmitPageMixin:
    @action(["POST"], detail=True, throttle_classes=[PageSubmitThrottle])
    def submit(self, request, pk=None):
        obj = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(page=obj)
        return Response(serializer.data)


class FindPageMixin:
    @action(["GET"], detail=False, url_path=r"find/(?P<url_path>[\w\d\-\_\/]+)")
    def find(self, request, url_path=None):
        try:
            obj = self.get_queryset().get(url_path=f"/{url_path}/")
        except ObjectDoesNotExist:
            raise NotFound

        if is_preview_request(request):
            obj = obj.get_latest_revision_as_page()
        else:
            obj = obj.specific

        self.check_object_permissions(request, obj)
        self._object = obj
        return self.retrieve(request)


class CachedGenericViewSet(GenericViewSet):
    """
    Mixin that adds response caching mechanism.
    """

    class CachedResponse(Exception):
        def __init__(self, response, *args):
            self.response = response
            self.response.cached = True
            super().__init__(*args)

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        if self.cache_enabled(request):
            response = cache.get_response_from_cache(request)
            if response:
                raise self.CachedResponse(response)

    def handle_exception(self, exc):
        if isinstance(exc, self.CachedResponse):
            return exc.response
        return super().handle_exception(exc)

    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        if self.cache_enabled(request, response):
            cache.cache_response(request, response)
        return response

    def cache_enabled(self, request, response=None):
        enabled = (
            getattr(settings, "CORE_CACHE_REQUESTS", True)
            and request.method.lower() == "get"
            and not is_preview_request(request)
        )
        if enabled and response:
            enabled = (
                enabled
                and not hasattr(response, "cached")
                and status.is_success(response.status_code)
            )
        return enabled


class GenericPageViewSet(CachedGenericViewSet):
    filter_backends = [
        DjangoFilterBackend,
        ChildOfFilter,
        DescendantOfFilter,
        OrderingFilter,
        TranslationOfFilter,
        LocaleFilter,
        SearchFilter,
    ]

    filterset_class = PageFilterSet

    def get_model(self):
        """
        Get page model either from the object or query param `type`.
        """
        if hasattr(self, "_object"):
            return self._object.__class__

        page_type = self.request.query_params.get("type", None)
        if not page_type:
            return CorePage

        try:
            app_label, model_name = page_type.split(".")
            return apps.get_model(app_label, model_name)
        except ValueError:
            raise ParseError("Page type invalid.")
        except LookupError:
            raise ParseError("Page type doesn't exist.")

    def get_base_queryset(self):
        """
        Return base queryset, needed for compatibility with Wagtail's API filters.
        """
        return self.prepare_queryset(CorePage.objects.all())

    def get_model_queryset(self):
        """
        Return queryset with specific model type.
        """
        PageModel = self.get_model()
        if PageModel:
            return self.prepare_queryset(PageModel.objects.all())
        return self.get_base_queryset()

    def prepare_queryset(self, queryset):
        """
        Return prepared queryset for final filtering/display.
        """
        if not is_preview_request(self.request):
            queryset = queryset.live()
        return queryset

    def get_queryset(self):
        queryset = self.get_model_queryset()
        serializer_class = self.get_serializer_class()
        return optimize_queryset_related_fields(queryset, serializer_class)

    def get_object(self):
        if not hasattr(self, "_object"):
            obj = super().get_object()
            if is_preview_request(self.request):
                self._object = obj.get_latest_revision_as_page()
            else:
                self._object = obj.specific
        return self._object

    def get_serializer_class(self):
        model = self.get_model()
        if not model:
            return super().get_serializer_class()
        if "search" in self.request.GET:
            return PageSearchSerializer
        if self.action == "list":
            return import_string(model.page_summary_serializer)
        if self.action == "submit":
            return import_string(model.page_submit_serializer)
        return import_string(model.page_serializer)

    def handle_exception(self, exc):
        """
        Handle additional exceptions.
        """
        if isinstance(exc, BadRequestError):
            return Response({"detail": str(exc)}, status=status.HTTP_400_BAD_REQUEST)
        return super().handle_exception(exc)


class PageViewSet(
    ListPageMixin,
    RetrievePageMixin,
    SubmitPageMixin,
    FindPageMixin,
    GenericPageViewSet,
):
    """
    Pages view set.
    """
