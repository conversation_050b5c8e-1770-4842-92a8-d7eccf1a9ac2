"""
Monkey patching stuff.
"""


def patch_wagtail_page_url():
    """
    Use full url for url property to point pages to live site.
    """
    from wagtail.core.models import Page

    Page.url = property(Page.get_full_url)


def patch_wagtail_image_renditions():
    """
    Monkey-patch W<PERSON><PERSON>'s image `get_rendition` method to support
    prefetching image renditions. Assumes that you've prefetched
    your image's renditions in advance.
    """
    from wagtail.images.models import Filter, Image

    def get_rendition(self, filter):
        if isinstance(filter, str):
            filter = Filter(spec=filter)

        filter_spec = filter.spec
        focal_point_key = filter.get_cache_key(self)

        rendition = None
        for x in self.renditions.all():
            if x.filter_spec == filter_spec and x.focal_point_key == focal_point_key:
                rendition = x

        if not rendition:
            rendition = self._get_rendition(filter)

        return rendition

    Image._get_rendition = Image.get_rendition
    Image.get_rendition = get_rendition


def patch_all():
    patch_wagtail_page_url()
    patch_wagtail_image_renditions()
