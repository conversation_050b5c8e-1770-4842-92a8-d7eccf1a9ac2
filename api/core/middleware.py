from django.conf import settings
from django.conf.urls.i18n import is_language_prefix_patterns_used
from django.middleware.locale import LocaleMiddleware as BaseLocaleMiddleware
from django.utils import translation
from django.utils.cache import patch_vary_headers


class LocaleMiddleware(BaseLocaleMiddleware):
    """
    Django locale middleware with skipped 404 redirects and enabled
    setting language from `locale` querystring.
    """

    def process_request(self, request):
        super().process_request(request)
        locale = request.GET.get("locale")
        if locale and locale in [x[0] for x in settings.LANGUAGES]:
            translation.activate(locale)
            request.LANGUAGE_CODE = locale

    def process_response(self, request, response):
        language = translation.get_language()
        language_from_path = translation.get_language_from_path(request.path_info)
        urlconf = getattr(request, "urlconf", settings.ROOT_URLCONF)
        i18n_patterns_used, _ = is_language_prefix_patterns_used(urlconf)

        if not (i18n_patterns_used and language_from_path):
            patch_vary_headers(response, ("Accept-Language",))
        response.setdefault("Content-Language", language)
        return response


class FixedAdminLocaleMiddleware:
    """
    Fix admin interface to English version by default.
    """

    def __init__(self, get_response) -> None:
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith("/admin/"):
            translation.activate("en")
        return self.get_response(request)
