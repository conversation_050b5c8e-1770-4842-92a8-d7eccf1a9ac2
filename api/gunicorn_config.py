import os

from psycogreen.gevent import patch_psycopg

HOST = os.environ.get("HOST", "127.0.0.1")
PORT = os.environ.get("PORT", "8000")

bind = f"{HOST}:{PORT}"
workers = os.environ.get("WORKERS", 1)
worker_class = "gevent"
worker_connections = os.environ.get("WORKER_CONNECTIONS", 100)
timeout = 0


def post_fork(server, worker):
    # Configure Psycopg to be used with gevent in non-blocking way.
    patch_psycopg()
    worker.log.info("Made Psycopg2 Green")
