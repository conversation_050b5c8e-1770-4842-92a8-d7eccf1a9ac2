from django.db.models import Q
from django.utils.translation import get_language_from_request
from rest_framework.filters import BaseFilterBackend
from wagtail.api.v2.utils import BadRequestError

from catalog.models import ProductCollection


class CollectionFilter(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        if "collection" in request.GET:
            try:
                lang = get_language_from_request(request)
                collection = ProductCollection.objects.get(
                    slug=request.GET["collection"],
                    locale__language_code=lang,
                )
            except ProductCollection.DoesNotExist:
                raise BadRequestError("Collection doesn't exist")

            queryset = queryset.child_of(collection)

        return queryset


class ProductNameFilter(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        if "title" in request.GET:
            title_filter = request.GET["title"]
            queryset = queryset.filter(
                Q(title__icontains=title_filter)
                & Q(details__isnull=False)
                & Q(path__isnull=False)
                & Q(base_price__gt=0.00)
            )
        return queryset
