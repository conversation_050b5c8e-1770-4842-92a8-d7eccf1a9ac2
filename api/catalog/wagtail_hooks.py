from django.contrib.admin.utils import quote
from django.shortcuts import redirect
from django.templatetags.static import static
from django.urls import path
from django.utils.html import format_html
from salesman.conf import app_settings as salesman_settings
from wagtail.admin.menu import MenuItem
from wagtail.contrib.modeladmin.options import (
    ModelAdmin,
    ModelAdminGroup,
    modeladmin_register,
)
from wagtail.core import hooks
from wagtailorderable.modeladmin.mixins import OrderableMixin

from core.modeladmin.filters import ParentPageListFilter
from core.modeladmin.mixins import ExcludeAliasesMixin, ThumbnailMixin
from core.modeladmin.options import TranslatableModelAdmin
from core.modeladmin.views import TranslatableChooseParentView, TranslatableCreateView

from .models import (
    Product,
    ProductCollection,
    ProductColor,
    ProductNumericSize,
    ProductSize,
)
from .reports import (
    generate_analytics_report,
    generate_catalog_report,
    reports_index,
    export_customers_view,
)
from .views.export import export_products_csv_view
from .views import (
    ProductColorChooserViewSet,
    ProductNumericSizeChooserViewSet,
    ProductSizeChooserViewSet,
    variant_add_modal_view,
    variant_create_view,
    variant_delete_view,
    variant_edit_view,
    variant_redirect_view,
)


@hooks.register("before_edit_page")
def set_only_one_is_homepage_collection(request, page):
    if isinstance(page, ProductCollection) and page.is_homepage_collection:
        ProductCollection.objects.exclude(pk=page.pk).update(
            is_homepage_collection=False
        )


@hooks.register("insert_global_admin_css")
def global_admin_css():
    return format_html(
        '<link rel="stylesheet" href="{}">', static("catalog/product_variant_list.css")
    )


@hooks.register("register_admin_urls")
def register_variant_urls():
    return [
        path(
            "catalog/variants/<int:product_id>/modal/",
            variant_add_modal_view,
            name="variant-add-modal",
        ),
        path(
            "catalog/variants/<int:product_id>/create/<str:combo_id>/",
            variant_create_view,
            name="variant-create",
        ),
        path(
            "catalog/variants/<int:product_id>/edit/<int:variant_id>/",
            variant_edit_view,
            name="variant-edit",
        ),
        path(
            "catalog/variants/<int:product_id>/delete/<int:variant_id>/",
            variant_delete_view,
            name="variant-delete",
        ),
        path(
            "catalog/variants/<int:product_id>/",
            variant_redirect_view,
            name="variant-redirect",
        ),
    ]


@hooks.register("register_admin_viewset")
def register_product_color_chooser_viewset():
    return ProductColorChooserViewSet("product_color_chooser")


@hooks.register("register_admin_viewset")
def register_product_size_chooser_viewset():
    return ProductSizeChooserViewSet("product_size_chooser")


@hooks.register("register_admin_viewset")
def register_product_numeric_size_chooser_viewset():
    return ProductNumericSizeChooserViewSet("product_numeric_size_chooser")


class AttributeAdminMixin(OrderableMixin):
    list_display = ["name", "code"]
    title_field_name = "name"
    ordering = ["sort_order"]


class ProductColorAdmin(AttributeAdminMixin, TranslatableModelAdmin):
    model = ProductColor


class ProductSizeAdmin(AttributeAdminMixin, ModelAdmin):
    model = ProductSize


class ProductNumericSizeAdmin(AttributeAdminMixin, ModelAdmin):
    model = ProductNumericSize


class AttributesGroup(ModelAdminGroup):
    menu_label = "Attributes"
    menu_order = 500
    menu_icon = "snippet"
    items = (ProductColorAdmin, ProductSizeAdmin, ProductNumericSizeAdmin)


modeladmin_register(AttributesGroup)


class CollectionAdmin(ThumbnailMixin, ExcludeAliasesMixin, TranslatableModelAdmin):
    model = ProductCollection
    menu_icon = "folder-inverse"
    list_display = ["title", "is_featured", "is_homepage_collection", "admin_thumb"]
    thumb_image_field_name = "featured_image"


class ProductCreateView(TranslatableCreateView):
    def dispatch(self, request, *args, **kwargs):
        self.bind_locale(request)
        return redirect(self.get_locale_action_url("choose_parent"))


class ProductChooseParentView(TranslatableChooseParentView):
    def form_valid(self, form):
        parent_pk = quote(form.cleaned_data["parent_page"].pk)
        return redirect("wagtailadmin_pages:add_subpage", parent_pk)


class ProductAdmin(ThumbnailMixin, ExcludeAliasesMixin, TranslatableModelAdmin):
    model = Product
    menu_icon = "tag"
    list_display = ["title", "admin_price", "admin_thumb"]
    list_filter = [ParentPageListFilter]
    search_fields = ["id", "title", "details"]
    parent_page_model = ProductCollection
    parent_page_parameter_name = "collection"
    parent_page_title = "Collection"
    create_view_class = ProductCreateView
    choose_parent_view_class = ProductChooseParentView

    base_form_class = Product.base_form_class

    def admin_price(self, obj):
        return salesman_settings.SALESMAN_PRICE_FORMATTER(obj.get_price(), {})

    admin_price.short_description = "Price"  # type: ignore


class CatalogGroup(ModelAdminGroup):
    menu_label = "Catalog"
    menu_order = 600
    menu_icon = "list-ul"
    items = (CollectionAdmin, ProductAdmin)


modeladmin_register(CatalogGroup)


@hooks.register("register_admin_menu_item")
def register_reports_menu_item():
    return MenuItem(
        label="Catalog Reports",
        url="/admin/catalog/reports/",
        icon_name="doc-empty",
        order=1000,
    )


@hooks.register("register_admin_urls")
def register_reports_urls():
    from django.urls import path

    return [
        path("catalog/reports/", reports_index, name="catalog_reports"),
        path(
            "catalog/reports/generate/",
            generate_catalog_report,
            name="catalog_report_generate",
        ),
        path(
            "catalog/reports/analytics/",
            generate_analytics_report,
            name="catalog_report_analytics",
        ),
        path(
            "catalog/reports/export-csv/",
            export_products_csv_view,
            name="catalog_export_csv",
        ),
        path(
            "catalog/reports/export-customers/",
            export_customers_view,
            name="catalog_customers_export",
        ),
    ]
