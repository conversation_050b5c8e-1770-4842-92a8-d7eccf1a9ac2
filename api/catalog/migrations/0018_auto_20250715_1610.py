# Generated by Django 3.2.25 on 2025-07-15 14:10

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields
import wagtail.core.fields


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0018_discount_discountusage'),
        ('catalog', '0017_auto_20250703_2116'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='access_code',
            field=models.CharField(blank=True, help_text='Add only if you want to make product accessible only by this code', max_length=15, null=True, verbose_name='Allows product to only be added to basket if code is present.'),
        ),
        migrations.AddField(
            model_name='product',
            name='guides',
            field=wagtail.core.fields.RichTextField(blank=True, verbose_name='Guides'),
        ),
        migrations.AddField(
            model_name='product',
            name='product_description',
            field=wagtail.core.fields.RichTextField(blank=True, verbose_name='Product description'),
        ),
        migrations.AddField(
            model_name='productcollection',
            name='alt_featured_image_data',
            field=models.JSONField(editable=False, null=True),
        ),
        migrations.AddField(
            model_name='productcollection',
            name='is_featured',
            field=models.BooleanField(default=False, verbose_name='Featured Collection'),
        ),
        migrations.AddField(
            model_name='productvariant',
            name='ean13',
            field=models.CharField(db_index=True, editable=False, max_length=13, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='EAN-13 must be exactly 13 digits', regex='^[0-9]{13}$')], verbose_name='EAN-13 Barcode'),
        ),
        migrations.AlterField(
            model_name='product',
            name='shipping_region',
            field=modelcluster.fields.ParentalKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='shop.shippingregion'),
        ),
    ]
