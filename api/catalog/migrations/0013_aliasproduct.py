# Generated by Django 3.2.13 on 2023-03-16 15:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0012_product_local_shipping_only'),
    ]

    operations = [
        migrations.CreateModel(
            name='AliasProduct',
            fields=[
                ('product_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.product')),
                ('override_title', models.BooleanField(default=False, help_text='Override product title', verbose_name='Title')),
                ('override_media', models.BooleanField(default=False, help_text='Override product media', verbose_name='Media')),
                ('selected_variant_code', models.CharField(blank=True, help_text='Set which variant code should be preselected on navigation.', max_length=255, verbose_name='Variant code')),
                ('product_alias', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='product_aliases', to='catalog.product', verbose_name='Alias for product')),
            ],
            options={
                'verbose_name': 'Alias product',
            },
            bases=('catalog.product',),
        ),
    ]
