# Generated by Django 3.2.9 on 2021-11-11 13:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0009_rename_hs_code_productvariant__hs_code'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='productvariant',
            name='_hs_code',
            field=models.CharField(blank=True, help_text='If left empty, first 8 letters of "Code" field are used.', max_length=8, verbose_name='Harmonized System code (HS)'),
        ),
        migrations.AlterField(
            model_name='productvariant',
            name='code',
            field=models.CharField(blank=True, db_index=True, help_text='Unique product code. Leave empty to auto-generate.', max_length=255, null=True, verbose_name='Product code (SKU)'),
        ),
    ]
