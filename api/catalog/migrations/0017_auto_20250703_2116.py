# Generated by Django 3.2.25 on 2025-07-03 19:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0016_alter_product_shipping_region'),
    ]

    operations = [
        migrations.AddField(
            model_name='productcollection',
            name='is_homepage_collection',
            field=models.BooleanField(default=False, help_text='Display this collection on the homepage.', verbose_name='Homepage Collection'),
        ),
        migrations.AlterField(
            model_name='productcollection',
            name='template',
            field=models.CharField(choices=[('4-columns', '4 columns'), ('3-columns', '3 columns'), ('2-columns', '2 columns'), ('1-columns', '1 columns')], default='4-columns', help_text='Select a template for displaying this collection.', max_length=32, verbose_name='Template'),
        ),
    ]
