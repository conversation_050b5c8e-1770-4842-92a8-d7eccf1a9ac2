# Generated by Django 3.2.21 on 2024-07-10 13:31

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0017_shippingregion'),
        ('wagtailimages', '0023_add_choose_permissions'),
        ('catalog', '0014_product_shipping_region'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='old_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Old product price in EUR, leave empty to hide.', max_digits=10, null=True, verbose_name='Old price (€)'),
        ),
        migrations.AddField(
            model_name='productcollection',
            name='title_as_image',
            field=models.ForeignKey(blank=True, help_text='Image to use as the collection title, this replaces the collection title on specific pages.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='wagtailimages.image', verbose_name='Title as image'),
        ),
        migrations.AddField(
            model_name='productcollection',
            name='title_mobile_as_image',
            field=models.ForeignKey(blank=True, help_text='Image to use as the collection title on mobile, this replaces the collection title on specific pages.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='wagtailimages.image', verbose_name='Title mobile as image'),
        ),
        migrations.AddField(
            model_name='productvariant',
            name='old_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text="Old variant price in EUR, leave empty to use base product's old price. If set, overrides base product's old price. If set to 0, hides old price.", max_digits=10, null=True, verbose_name='Old price (€)'),
        ),
        migrations.AlterField(
            model_name='product',
            name='shipping_region',
            field=modelcluster.fields.ParentalKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='shop.shippingregion'),
        ),
    ]
