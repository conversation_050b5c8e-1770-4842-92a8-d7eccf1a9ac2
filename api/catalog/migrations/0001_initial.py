# Generated by Django 3.2.8 on 2021-10-22 07:59

import catalog.models.attribute
import catalog.models.product
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import modelcluster.fields
import uuid
import wagtail.core.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('wagtailimages', '0023_add_choose_permissions'),
        ('wagtailcore', '0062_comment_models_and_pagesubscription'),
        ('salesmanbasket', '0001_initial'),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CatalogPage',
            fields=[
                ('corepage_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.corepage')),
            ],
            options={
                'abstract': False,
            },
            bases=(catalog.models.product.InvalidateProductsMixin, 'core.corepage'),
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('corepage_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.corepage')),
                ('published_at', models.DateTimeField(default=django.utils.timezone.localtime, verbose_name='Published at')),
                ('media_data', models.JSONField(editable=False, null=True)),
                ('details', wagtail.core.fields.RichTextField(blank=True, verbose_name='Details')),
                ('base_price', models.DecimalField(decimal_places=2, default=0, help_text='Base product price in EUR.', max_digits=10, verbose_name='Base price (€)')),
            ],
            bases=(catalog.models.product.InvalidateProductsMixin, 'core.corepage', models.Model),
        ),
        migrations.CreateModel(
            name='ProductCollection',
            fields=[
                ('corepage_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.corepage')),
            ],
            options={
                'verbose_name': 'Collection',
            },
            bases=(catalog.models.product.InvalidateProductsMixin, 'core.corepage'),
        ),
        migrations.CreateModel(
            name='ProductNumericSize',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('code', models.CharField(db_index=True, help_text='A unique code to identify this attribute.', max_length=32, validators=[catalog.models.attribute.validate_attribute_code], verbose_name='Code')),
                ('name', models.CharField(blank=True, help_text="Display name for the attribute, defaults to 'code'.", max_length=128, verbose_name='Name')),
            ],
            options={
                'verbose_name': 'Numeric size',
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductSize',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('code', models.CharField(db_index=True, help_text='A unique code to identify this attribute.', max_length=32, validators=[catalog.models.attribute.validate_attribute_code], verbose_name='Code')),
                ('name', models.CharField(blank=True, help_text="Display name for the attribute, defaults to 'code'.", max_length=128, verbose_name='Name')),
            ],
            options={
                'verbose_name': 'Size',
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(blank=True, help_text='Leave empty to set to unlimited.', null=True, verbose_name='Quantity')),
                ('reserved_quantity', models.IntegerField(default=0, editable=False)),
                ('media_data', models.JSONField(editable=False, null=True)),
                ('product_translation_key', models.UUIDField(db_index=True, editable=False)),
                ('code', models.CharField(blank=True, db_index=True, help_text='Unique product code. Leave empty to auto-generate.', max_length=255, null=True, verbose_name='Code')),
                ('price', models.DecimalField(blank=True, decimal_places=2, help_text='Variant price in EUR, leave empty to use base product price.', max_digits=10, null=True, verbose_name='Price (€)')),
                ('combo_id', models.CharField(blank=True, editable=False, max_length=255)),
                ('combo_data', models.JSONField(editable=False, null=True)),
                ('product_data', models.JSONField(editable=False, null=True)),
            ],
            options={
                'verbose_name': 'Variant',
                'verbose_name_plural': 'Variants',
            },
        ),
        migrations.CreateModel(
            name='ColorProduct',
            fields=[
                ('product_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.product')),
            ],
            options={
                'verbose_name': 'Product with variations (colors)',
            },
            bases=('catalog.product',),
        ),
        migrations.CreateModel(
            name='NumericSizeColorProduct',
            fields=[
                ('product_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.product')),
            ],
            options={
                'verbose_name': 'Product with variations (numeric sizes + colors)',
            },
            bases=('catalog.product',),
        ),
        migrations.CreateModel(
            name='NumericSizeProduct',
            fields=[
                ('product_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.product')),
            ],
            options={
                'verbose_name': 'Product with variations (numeric sizes)',
            },
            bases=('catalog.product',),
        ),
        migrations.CreateModel(
            name='SingleProduct',
            fields=[
                ('product_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.product')),
            ],
            options={
                'verbose_name': 'Single product',
            },
            bases=('catalog.product',),
        ),
        migrations.CreateModel(
            name='SingleProductVariant',
            fields=[
                ('productvariant_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.productvariant')),
            ],
            options={
                'verbose_name': 'Single Variant',
                'abstract': False,
            },
            bases=('catalog.productvariant',),
        ),
        migrations.CreateModel(
            name='SizeColorProduct',
            fields=[
                ('product_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.product')),
            ],
            options={
                'verbose_name': 'Product with variations (sizes + colors)',
            },
            bases=('catalog.product',),
        ),
        migrations.CreateModel(
            name='SizeProduct',
            fields=[
                ('product_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.product')),
            ],
            options={
                'verbose_name': 'Product with variations (sizes)',
            },
            bases=('catalog.product',),
        ),
        migrations.CreateModel(
            name='ProductVariantMedia',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('image', models.ForeignKey(blank=True, help_text='Image will be cropped to multiple sizes and should uploaded with at least "1200x1400px" dimensions.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='wagtailimages.image')),
                ('variant', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='media', to='catalog.productvariant')),
            ],
            options={
                'verbose_name': 'Media',
                'verbose_name_plural': 'Media',
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductRelation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('product', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='relations', to='catalog.product')),
                ('to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='catalog.product')),
            ],
            options={
                'verbose_name': 'Related product',
                'verbose_name_plural': 'Related products',
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductMedia',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('image', models.ForeignKey(blank=True, help_text='Image will be cropped to multiple sizes and should uploaded with at least "1200x1400px" dimensions.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='wagtailimages.image')),
                ('product', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='media', to='catalog.product')),
            ],
            options={
                'verbose_name': 'Media',
                'verbose_name_plural': 'Media',
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductColor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('translation_key', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('code', models.CharField(db_index=True, help_text='A unique code to identify this attribute.', max_length=32, validators=[catalog.models.attribute.validate_attribute_code], verbose_name='Code')),
                ('name', models.CharField(blank=True, help_text="Display name for the attribute, defaults to 'code'.", max_length=128, verbose_name='Name')),
                ('locale', models.ForeignKey(editable=False, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='wagtailcore.locale')),
            ],
            options={
                'verbose_name': 'Color',
                'ordering': ['sort_order'],
                'abstract': False,
                'unique_together': {('translation_key', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='BasketStockReservation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reserved_products', models.JSONField(editable=False, null=True)),
                ('reserved_at', models.DateTimeField(null=True)),
                ('basket', models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_reservation', to='salesmanbasket.basket')),
            ],
        ),
        migrations.CreateModel(
            name='SizeProductVariant',
            fields=[
                ('productvariant_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.productvariant')),
                ('size', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.productsize')),
            ],
            options={
                'verbose_name': 'Variant (size)',
                'abstract': False,
            },
            bases=('catalog.productvariant',),
        ),
        migrations.CreateModel(
            name='SizeColorProductVariant',
            fields=[
                ('productvariant_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.productvariant')),
                ('color', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.productcolor')),
                ('size', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.productsize')),
            ],
            options={
                'verbose_name': 'Variant (size + color)',
                'abstract': False,
            },
            bases=('catalog.productvariant',),
        ),
        migrations.CreateModel(
            name='NumericSizeProductVariant',
            fields=[
                ('productvariant_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.productvariant')),
                ('size', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.productnumericsize')),
            ],
            options={
                'verbose_name': 'Variant (size)',
                'abstract': False,
            },
            bases=('catalog.productvariant',),
        ),
        migrations.CreateModel(
            name='NumericSizeColorProductVariant',
            fields=[
                ('productvariant_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.productvariant')),
                ('color', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.productcolor')),
                ('size', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.productnumericsize')),
            ],
            options={
                'verbose_name': 'Product Variant (numeric size + color)',
                'abstract': False,
            },
            bases=('catalog.productvariant',),
        ),
        migrations.CreateModel(
            name='ColorProductVariant',
            fields=[
                ('productvariant_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='catalog.productvariant')),
                ('color', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.productcolor')),
            ],
            options={
                'verbose_name': 'Variant (color)',
                'abstract': False,
            },
            bases=('catalog.productvariant',),
        ),
    ]
