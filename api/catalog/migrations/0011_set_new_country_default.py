# Generated by Django 3.2.9 on 2021-11-12 10:05

from django.db import migrations


def update_default_country(apps, schema_editor):
    Product = apps.get_model("catalog.Product")

    objs = []
    for product in Product.objects.all():
        if len(product.origin_country) > 2:
            product.origin_country = "ES"
            objs.append(product)

    Product.objects.bulk_update(objs, ["origin_country"])


class Migration(migrations.Migration):

    dependencies = [
        ("catalog", "0010_auto_20211111_1417"),
    ]

    operations = [
        migrations.RunPython(
            update_default_country,
            reverse_code=lambda apps, schema_editor: None,
        )
    ]
