# Generated by Django 3.2.8 on 2021-10-22 13:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0004_productcollection_template'),
    ]

    operations = [
        migrations.AddField(
            model_name='productcollection',
            name='featured_image_data',
            field=models.JSONField(editable=False, null=True),
        ),
        migrations.AlterField(
            model_name='productcollection',
            name='template',
            field=models.CharField(choices=[('4-columns', '4 columns'), ('3-columns', '3 columns')], default='4-columns', help_text='Select a template for displaying this collection.', max_length=32, verbose_name='Template'),
        ),
    ]
