from django.urls import re_path
from generic_chooser.views import ModelChooserMixin, ModelChooserViewSet
from wagtail.core.models import Locale

from ..models import ProductColor, ProductNumericSize, ProductSize


class AttributeModelChooserViewSet(ModelChooserViewSet):
    icon = "snippet"
    fields = ["id", "name"]

    def get_urlpatterns(self):
        return [
            re_path(r"^$", self.choose_view, name="choose"),
            re_path(r"^(.+)/$", self.chosen_view, name="chosen"),
        ]


class TranslatableModelChooserMixin(ModelChooserMixin):
    def get_unfiltered_object_list(self):
        objects = super().get_unfiltered_object_list()
        if "locale" in self.request.GET:
            try:
                locale = Locale.objects.get(language_code=self.request.GET["locale"])
                objects = objects.filter(locale_id=locale.id)
            except Locale.DoesNotExist:
                objects = objects.none()
        return objects


class ProductColorChooserViewSet(AttributeModelChooserViewSet):
    model = ProductColor
    page_title = "Choose a color"
    chooser_mixin_class = TranslatableModelChooserMixin


class ProductSizeChooserViewSet(AttributeModelChooserViewSet):
    model = ProductSize
    page_title = "Choose a size"


class ProductNumericSizeChooserViewSet(AttributeModelChooserViewSet):
    model = ProductNumericSize
    page_title = "Choose a numeric size"
