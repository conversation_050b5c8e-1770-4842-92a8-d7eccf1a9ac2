"""
Admin views for product CSV export functionality.
"""

import csv
import os
import tempfile
import io
import sys
from django.contrib import messages
from django.core.management import call_command
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.apps import apps
from catalog.models.product import Product


@require_http_methods(["GET", "POST"])
def export_products_csv_view(request):
    """
    Admin view for exporting products to CSV.
    Provides a form interface for the export_products_csv management command.
    """

    if request.method == "GET":
        # Get available product models for the dropdown
        concrete_product_models = get_concrete_product_models()

        context = {
            "title": "Export Products to CSV",
            "product_models": concrete_product_models,
            "default_media_url": "https://leeos-merch.storage.googleapis.com",
        }
        return render(request, "catalog/export_csv.html", context)

    elif request.method == "POST":
        # Handle form submission
        try:
            # Get form parameters
            locale = request.POST.get("locale", "en")
            fallback = request.POST.get("fallback", "es")
            product_model = request.POST.get("product_model", "")
            media_url = request.POST.get(
                "media_url", "https://leeos-merch.storage.googleapis.com"
            )
            download_format = request.POST.get(
                "download_format", "download"
            )  # download or preview

            # Create temporary file for export
            timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
            filename = f"products_export_{timestamp}.csv"

            with tempfile.NamedTemporaryFile(
                mode="w+", suffix=".csv", delete=False, encoding="utf-8"
            ) as temp_file:
                temp_path = temp_file.name

                # Build command arguments
                command_args = [
                    "--output",
                    temp_path,
                    "--locale",
                    locale,
                    "--fallback",
                    fallback,
                    "--media-url",
                    media_url,
                ]

                if product_model:
                    command_args.extend(["--product-model", product_model])

                # Capture command output
                stdout_capture = io.StringIO()
                stderr_capture = io.StringIO()

                # Execute the management command with output capture
                try:
                    call_command(
                        "export_products_csv",
                        *command_args,
                        stdout=stdout_capture,
                        stderr=stderr_capture,
                    )
                    command_output = stdout_capture.getvalue()
                    command_errors = stderr_capture.getvalue()

                    # Log command output for debugging
                    if command_output:
                        print(f"[CSV Export] Command output: {command_output}")
                    if command_errors:
                        print(f"[CSV Export] Command errors: {command_errors}")

                except Exception as cmd_error:
                    raise Exception(f"Management command failed: {str(cmd_error)}")
                finally:
                    stdout_capture.close()
                    stderr_capture.close()

                # Read the generated file
                with open(temp_path, "r", encoding="utf-8") as f:
                    content = f.read()

                # Validate CSV content
                lines = content.strip().split("\n")
                if len(lines) <= 1:
                    # Only header or empty file - run diagnostics
                    diagnosis = diagnose_product_data()
                    error_msg = "No product data was exported. "

                    if command_output:
                        error_msg += f"\n\nCommand output: {command_output}"
                    if command_errors:
                        error_msg += f"\n\nCommand errors: {command_errors}"

                    error_msg += f"\n\nDiagnostic information:\n" + "\n".join(diagnosis)
                    raise Exception(error_msg)

                print(f"[CSV Export] Successfully exported {len(lines)-1} rows of data")

                # Clean up temporary file
                os.unlink(temp_path)

                if download_format == "debug":
                    # Return debug information
                    diagnosis = diagnose_product_data()
                    debug_info = {
                        "command_output": command_output,
                        "command_errors": command_errors,
                        "csv_lines": len(lines),
                        "diagnosis": diagnosis,
                        "export_params": {
                            "locale": locale,
                            "fallback": fallback,
                            "product_model": product_model,
                            "media_url": media_url,
                        },
                    }

                    context = {
                        "title": "CSV Export Debug Information",
                        "debug_info": debug_info,
                    }
                    return render(request, "catalog/export_csv_debug.html", context)

                elif download_format == "preview":
                    # Return preview of first 10 lines
                    lines = content.split("\n")
                    preview_lines = lines[:11]  # Header + 10 data rows
                    preview_content = "\n".join(preview_lines)

                    # Parse CSV for better display
                    csv_data = []
                    csv_reader = csv.reader(preview_lines, delimiter=";")
                    for row in csv_reader:
                        csv_data.append(row)

                    context = {
                        "title": "CSV Export Preview",
                        "csv_data": csv_data,
                        "total_lines": len(lines) - 1,  # Exclude header
                        "showing_lines": min(10, len(lines) - 1),
                        "export_params": {
                            "locale": locale,
                            "fallback": fallback,
                            "product_model": product_model,
                            "media_url": media_url,
                        },
                    }
                    return render(request, "catalog/export_csv_preview.html", context)

                else:
                    # Return file for download
                    response = HttpResponse(
                        content, content_type="text/csv; charset=utf-8"
                    )
                    response["Content-Disposition"] = (
                        f'attachment; filename="{filename}"'
                    )

                    # Add success message for next request
                    messages.success(
                        request, f"Successfully exported products to {filename}"
                    )

                    return response

        except Exception as e:
            messages.error(request, f"Export failed: {str(e)}")
            try:
                return redirect(reverse("catalog_export_csv"))
            except:
                # Fallback if URL reverse fails
                return redirect("/admin/catalog/reports/export-csv/")


def get_concrete_product_models():
    """Get all concrete product models that inherit from Product"""
    product_models = []
    for app_config in apps.get_app_configs():
        for model in app_config.get_models():
            if (
                issubclass(model, Product)
                and model is not Product
                and not model._meta.abstract
            ):
                # Check if model has implemented get_variant_model
                method = model.get_variant_model
                if method.__qualname__ != "Product.get_variant_model":
                    product_models.append(
                        {
                            "name": f"{model._meta.app_label}.{model.__name__}",
                            "label": model.__name__,
                            "verbose_name": model._meta.verbose_name,
                        }
                    )

    return product_models


def diagnose_product_data():
    """Diagnose why no products might be exported"""
    diagnosis = []

    # Check all product models
    product_models = get_concrete_product_models()

    if not product_models:
        diagnosis.append(
            "No concrete product models found that implement get_variant_model"
        )
        return diagnosis

    for model_info in product_models:
        model_name = model_info["name"]

        try:
            app_label, model_name_only = model_name.split(".")
            model_class = apps.get_model(app_label, model_name_only)

            live_count = model_class.objects.live().count()
            total_count = model_class.objects.count()

            diagnosis.append(
                f"{model_name}: {live_count} live / {total_count} total products"
            )

            if live_count > 0:
                # Check if products have variants
                sample_product = model_class.objects.live().first()
                if sample_product:
                    try:
                        variants = sample_product.get_variants()
                        variant_count = variants.count() if variants else 0
                        diagnosis.append(
                            f"  Sample product '{sample_product.title}' has {variant_count} variants"
                        )
                    except Exception as e:
                        diagnosis.append(
                            f"  Error getting variants for sample product: {e}"
                        )
        except Exception as e:
            diagnosis.append(f"  Error checking {model_name}: {e}")

    return diagnosis


def export_status_api(request):
    """
    API endpoint to check export status (for future async implementation)
    """
    return JsonResponse({"status": "ready"})
