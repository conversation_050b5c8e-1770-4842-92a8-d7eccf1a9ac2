from typing import Optional

from django.contrib.admin.utils import quote
from django.http.response import Http404
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.functional import cached_property
from wagtail.admin import messages
from wagtail.admin.modal_workflow import render_modal_workflow
from wagtail.contrib.modeladmin.helpers.url import AdminURLHelper
from wagtail.contrib.modeladmin.options import ModelAdmin
from wagtail.contrib.modeladmin.views import DeleteView, EditView
from wagtail.core.models import Page

from ..models import VariantCombination


def get_product_or_404(product_id):
    try:
        return Page.objects.get(id=product_id).specific
    except Page.DoesNotExist:
        raise Http404("Product not found.")


class VariantViewMixin:
    product: Optional[int] = None

    def __init__(self, model_admin, instance_pk, product):
        self.product = product
        super().__init__(model_admin, instance_pk)
        # Activate language on instance based on product
        self.instance.set_language(self.product.locale.language_code)

    @cached_property
    def index_url(self):
        return self.url_helper.get_action_url("index", self.product.id)

    @cached_property
    def edit_url(self):
        return self.url_helper.get_action_url("edit", self.product.id, self.pk_quoted)

    @cached_property
    def delete_url(self):
        return self.url_helper.get_action_url("delete", self.product.id, self.pk_quoted)


class VariantEditView(VariantViewMixin, EditView):
    def __init__(self, model_admin, instance_pk, product):
        super().__init__(model_admin, instance_pk, product)
        # Trigger save related media data
        self.instance.get_media_data()

    def get_success_message_buttons(self, instance):
        product_id = instance.get_localized_data("product_data")["id"]
        button_url = self.url_helper.get_action_url(
            "edit", product_id, quote(instance.pk)
        )
        return [messages.button(button_url, "Edit")]

    def get_success_url(self):
        return self.edit_url


class VariantDeleteView(VariantViewMixin, DeleteView):
    pass


class VariantAdminURLHelper(AdminURLHelper):
    def get_action_url_name(self, action):
        return f"variant-{action}"

    def get_action_url(self, action, *args, **kwargs):
        if action == "index":
            return reverse("wagtailadmin_pages:edit", args=args, kwargs=kwargs)
        return super().get_action_url(action, *args, **kwargs)


def get_variant_model_admin_class(product):
    """
    Dinamically build model admin class based on product.
    """
    return type(
        "VariantModelAdmin",
        (ModelAdmin,),
        {
            "model": product.get_variant_model(),
            "url_helper_class": VariantAdminURLHelper,
            "history_view_enabled": False,
        },
    )


def variant_add_modal_view(request, product_id):
    """
    View that renders the variants modal for a product.
    """
    product = get_product_or_404(product_id)

    return render_modal_workflow(
        request,
        "catalog/product_variant_add_modal.html",
        template_vars={"product": product},
    )


def variant_create_view(request, product_id, combo_id):
    """
    View that creates a new draft variant for the given group page and parameters.
    """
    product = get_product_or_404(product_id)
    combo = VariantCombination.from_identifier(product, combo_id)
    variant = combo.get_or_create_variant()[0]

    messages.success(request, f"Created a variant '{variant}'.")
    if "edit" in request.GET:
        return redirect("variant-edit", product_id, variant.id)
    return redirect("wagtailadmin_pages:edit", product_id)


def variant_edit_view(request, product_id, variant_id):
    """
    Edit view for product variant.
    """
    product = get_product_or_404(product_id)
    model_admin = get_variant_model_admin_class(product)()
    kwargs = {
        "model_admin": model_admin,
        "instance_pk": str(variant_id),
        "product": product,
    }
    return VariantEditView.as_view(**kwargs)(request)


def variant_delete_view(request, product_id, variant_id):
    """
    Delete product variant.
    """
    product = get_product_or_404(product_id)
    model_admin = get_variant_model_admin_class(product)()
    kwargs = {
        "model_admin": model_admin,
        "instance_pk": str(variant_id),
        "product": product,
    }
    return VariantDeleteView.as_view(**kwargs)(request)


def variant_redirect_view(request, product_id):
    """
    Helper view that redirects variant url to product edit view.
    """
    return redirect("wagtailadmin_pages:edit", product_id)
