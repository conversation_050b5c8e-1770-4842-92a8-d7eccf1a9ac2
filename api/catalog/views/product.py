from core.views import GenericPageViewSet, ListPageMixin

from ..filters import <PERSON><PERSON>ilter, ProductNameFilter
from ..models import Product
from ..pagination import ProductLimitOffsetPagination


class ProductViewSet(ListPageMixin, GenericPageViewSet):
    filter_backends = GenericPageViewSet.filter_backends + [
        CollectionFilter,
        ProductNameFilter,
    ]
    pagination_class = ProductLimitOffsetPagination

    def get_model(self):
        if hasattr(self, "_object"):
            return self._object.__class__
        return Product

    def get_base_queryset(self):
        return self.prepare_queryset(Product.objects.all())
