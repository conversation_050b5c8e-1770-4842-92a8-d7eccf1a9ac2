from django.apps import apps
from django.contrib.admin.utils import quote
from django.urls import reverse
from generic_chooser.widgets import AdminChooser


class AttributeChooser(AdminChooser):
    attribute: str = "attribute"
    attribute_label: str = ""
    attribute_model_name: str = ""

    def __init__(self, *args, **kwargs):
        if "locale" not in kwargs:
            raise ValueError("Attribute chooser must be init with locale argument.")
        self.locale = kwargs.pop("locale")

        if not self.attribute_model_name:
            raise ValueError("Must specify `attribute_model_name`")
        if not self.attribute_label:
            self.attribute_label = self.attribute

        self.choose_one_text = f"Choose {self.attribute_label}"
        self.choose_another_text = f"Choose another {self.attribute_label}"
        self.link_to_chosen_text = f"Edit this {self.attribute_label}"
        self.choose_modal_url_name = f"product_{self.attribute}_chooser:choose"
        self.model = apps.get_model(f"catalog.{self.attribute_model_name}")
        super().__init__(*args, **kwargs)

    def get_edit_item_url(self, item):
        return reverse(
            f"catalog_{self.attribute_model_name.lower()}_modeladmin_edit",
            args=(quote(item.pk),),
        )

    def get_choose_modal_url(self):
        return super().get_choose_modal_url() + f"?locale={self.locale.language_code}"


class ProductColorChooser(AttributeChooser):
    attribute = "color"
    attribute_model_name = "ProductColor"


class ProductSizeChooser(AttributeChooser):
    attribute = "size"
    attribute_model_name = "ProductSize"


class ProductNumericSizeChooser(AttributeChooser):
    attribute = "numeric_size"
    attribute_label = "numeric size"
    attribute_model_name = "ProductNumericSize"
