from urllib.parse import urlencode

from django.urls import reverse
from django.utils.translation import get_language_from_request, pgettext_lazy
from rest_framework import serializers
from salesman.core.serializers import PriceField

from catalog.models.product import ProductMedia, ProductVariant
from catalog.pagination import ProductLimitOffsetPagination
from core.serializers import (
    LabelsField,
    PageSerializer,
    PageSummarySerializer,
    RichTextField,
)
from shop.formatters import price_format

from .models import AliasProduct, Product, ProductCollection


class ProductCollectionSummarySerializer(PageSummarySerializer):
    featured_image = serializers.DictField(source="get_featured_image_data")
    alt_featured_image = serializers.DictField(source="get_alt_featured_image_data")
    is_featured = serializers.BooleanField()
    is_homepage_collection = serializers.BooleanField()

    class Meta:
        model = ProductCollection
        fields = PageSummarySerializer.Meta.fields + [
            "featured_image",
            "alt_featured_image",
            "caption",
            "is_featured",
            "is_homepage_collection",
        ]


class ProductCollectionSerializer(PageSerializer):
    featured_image = serializers.DictField(source="get_featured_image_data")
    alt_featured_image = serializers.DictField(source="get_alt_featured_image_data")
    products = serializers.SerializerMethodField()
    is_featured = serializers.BooleanField()
    is_homepage_collection = serializers.BooleanField()

    class Meta:
        model = ProductCollection
        fields = PageSerializer.Meta.fields + [
            "featured_image",
            "alt_featured_image",
            "caption",
            "template",
            "products",
            "is_featured",
            "is_homepage_collection",
        ]

    def get_products(self, obj):
        limit = ProductLimitOffsetPagination.default_limit
        limit_plus_one = limit + 1
        items = obj.get_children().live().specific()[:limit_plus_one]
        data = {
            "next": None,
            "results": ProductSummarySerializer(
                items[:limit],
                context=self.context,
                many=True,
            ).data,
        }
        if len(items) > limit:
            params = {
                "collection": obj.slug,
                "limit": limit,
                "offset": limit,
            }
            data["next"] = reverse("product-list") + "?" + urlencode(params)
        return data


class ProductVariantSerializer(serializers.ModelSerializer):
    product_type = serializers.CharField(source="_meta.label")
    price = PriceField(source="get_price")
    old_price = PriceField(source="get_old_price")
    weight = serializers.CharField(source="get_weight")
    combination = serializers.SerializerMethodField()
    images = serializers.JSONField(source="get_media_data")

    class Meta:
        model = ProductVariant
        base_fields = [
            "id",
            "product_type",
            "code",
            "hs_code",
            "name",
            "price",
            "old_price",
            "weight",
            "available_quantity",
            "origin_country",
            "combination",
        ]
        fields = base_fields + ["images"]

    def get_combination(self, obj):
        request = self.context["request"]
        obj.set_language(get_language_from_request(request))
        return obj.get_localized_data("combo_data")


class ProductAliasSerializerMixin(serializers.Serializer):
    """
    Add 'alias' functionality to products serializers.
    """

    def to_representation(self, instance):
        product_alias = getattr(instance, "product_alias", None)
        if product_alias and hasattr(product_alias, "get_variant_model"):
            if instance.override_title:
                product_alias.title = instance.title
            if instance.override_media:
                product_alias.get_media_data = instance.get_media_data
            data = super().to_representation(product_alias)
            if instance.selected_variant_code:
                data["url_path"] += f"?var={instance.selected_variant_code}"
            return data
        return super().to_representation(instance)


class ProductSummarySerializer(ProductAliasSerializerMixin, PageSummarySerializer):
    price = PriceField(source="get_price")
    old_price = PriceField(source="get_old_price")
    images = serializers.SerializerMethodField()

    class Meta(PageSummarySerializer.Meta):
        model = Product
        fields = PageSummarySerializer.Meta.fields + ["price", "old_price", "images"]

    def get_images(self, obj):
        media_data = (obj.get_media_data() or [])[:2]
        return [media[ProductMedia.ImageSize.LARGE.label] for media in media_data]


class ProductSerializer(ProductAliasSerializerMixin, PageSerializer):
    base_price = PriceField()
    old_price = PriceField()
    product_access_code = serializers.SerializerMethodField()
    details = RichTextField()
    guides = RichTextField()
    product_description = RichTextField()
    images = serializers.JSONField(source="get_media_data")
    variant_attributes = serializers.SerializerMethodField()
    variants = ProductVariantSerializer(source="get_variants", many=True)
    related_products = ProductSummarySerializer(
        source="get_related_products", many=True
    )
    labels = LabelsField(
        (
            "delivery",
            pgettext_lazy("Product", "Delivery estimated in 1-5 business days."),
        ),
        ("details", pgettext_lazy("Product", "Details")),
        ("guides", pgettext_lazy("Product", "Guides")),
        ("product_description", pgettext_lazy("Product", "Product description")),
        ("code", pgettext_lazy("Product", "Product code")),
        ("similar", pgettext_lazy("Product", "Shop similar")),
    )

    class Meta(PageSerializer.Meta):
        model = Product
        fields = PageSerializer.Meta.fields + [
            "base_price",
            "old_price",
            "product_access_code",
            "details",
            "guides",
            "product_description",
            "images",
            "variant_attributes",
            "variants",
            "related_products",
            "labels",
        ]

    def get_product_access_code(self, obj):
        """
        Returns True if the product has an access code, False otherwise.
        This prevents leaking the actual access code value in the API response.
        """
        return bool(obj.access_code)

    def get_variant_attributes(self, obj):
        data = {}
        try:
            variant_model = obj.get_variant_model()
            for attr in variant_model.get_variant_attributes(obj.locale_id):
                data[attr.code] = {
                    "code": attr.code,
                    "label": attr.label,
                    "values": [
                        {"value": x.value, "label": x.label} for x in attr.values
                    ],
                }
        except (NotImplementedError, AttributeError):
            # Handle cases where get_variant_model is not properly implemented
            # or where the object doesn't have the expected structure
            pass
        return data


class ProductGroupSerializer(ProductSerializer):
    media = serializers.JSONField(source="get_media_data")
    variants = serializers.JSONField(source="variants_data")

    class Meta(ProductSerializer.Meta):
        fields = ProductSerializer.Meta.fields + ["media", "variants"]


# ======================================================================================
# Alias Product Serializer
# ======================================================================================


class AliasProductSerializer(ProductSerializer):
    class Meta(ProductSerializer.Meta):
        model = AliasProduct


class AliasProductSummarySerializer(ProductSummarySerializer):
    class Meta(ProductSummarySerializer.Meta):
        model = AliasProduct

    # def to_representation(self, instance):
    #     if instance.override_title:
    #         instance.alias_product.title = instance.title
    #     if instance.override_media:
    #         instance.alias_product.get_media_data = instance.get_media_data
    #     return super().to_representation(instance.alias_product)


# ======================================================================================
# Basket Variant Serializer
# ======================================================================================


class BasketVariantSerializer(ProductVariantSerializer):
    image = serializers.SerializerMethodField()
    base_product = serializers.SerializerMethodField()

    class Meta(ProductVariantSerializer.Meta):
        fields = ProductVariantSerializer.Meta.base_fields + [
            "image",
            "base_product",
        ]

    def get_image(self, obj):
        media_data = obj.get_media_data() or []
        return (
            media_data[0][ProductMedia.ImageSize.SMALL.label]  # type: ignore
            if media_data
            else None
        )

    def get_base_product(self, obj):
        from decimal import Decimal  # noqa

        request = self.context["request"]
        obj.set_language(get_language_from_request(request))
        product_data = obj.get_localized_data("product_data")
        return {
            "id": product_data.get("id", None),
            "url_path": product_data.get("url_path", None),
            "name": product_data.get("name", None),
            "slug": product_data.get("slug", None),
            "base_price": price_format(
                eval(product_data.get("base_price", "None")),
                context=self.context,
            ),
        }
