{% extends "wagtailadmin/base.html" %} {% load i18n wagtailadmin_tags static %} {% block titletag %}{% trans "Catalog Reports" %}{% endblock %} {% block content %} {% include
"wagtailadmin/shared/header.html" with title="Catalog Reports" icon="doc-empty" %}

<div class="nice-padding">
  {% if messages %}
  <ul class="messages">
    {% for message in messages %}
    <li class="{% if message.tags %}{{ message.tags }}{% endif %}">{{ message }}</li>
    {% endfor %}
  </ul>
  {% endif %}

  <div class="help-block">
    <p>Generate comprehensive reports for your product catalog. These reports provide detailed insights into your inventory, pricing, and product performance.</p>
  </div>

  <div class="report-card">
    <h2>Products and Variants Report</h2>
    <p>Generate a comprehensive report of all products and their variants, including:</p>
    <ul>
      <li>Basic product information (ID, name, prices, status)</li>
      <li>Variant details (SKU, inventory levels, attributes)</li>
      <li>Collection associations and categorization</li>
      <li>Media and image information</li>
      <li>Logistics data (weight, dimensions, shipping)</li>
    </ul>
    <form method="post" action="{% url 'catalog_report_generate' %}" class="report-form">
      {% csrf_token %}
      <div class="form-row">
        <div class="field">
          <label for="catalog_datetime_from">From Date:</label>
          <input type="datetime-local" id="catalog_datetime_from" name="datetime_from" value="{{ default_from_date }}" class="input" required />
        </div>
        <div class="field">
          <label for="catalog_datetime_to">To Date (optional):</label>
          <input type="datetime-local" id="catalog_datetime_to" name="datetime_to" value="{{ default_to_date }}" class="input" />
        </div>
      </div>
      <div class="report-actions">
        <button type="submit" class="button button-primary">
          <span class="icon icon-doc-full"></span>
          <em>Generate Report</em>
        </button>
      </div>
    </form>
  </div>

  <div class="report-card">
    <h2>Analytics Report</h2>
    <p>Get insights into your catalog performance and analytics:</p>
    <ul>
      <li>Product performance metrics</li>
      <li>Inventory analysis and trends</li>
      <li>Collection popularity statistics</li>
      <li>Pricing distribution analysis</li>
      <li>Variant availability overview</li>
    </ul>
    {% if user.is_superuser %}
    <form method="post" action="{% url 'catalog_report_analytics' %}" class="report-form">
      {% csrf_token %}
      <div class="form-row">
        <div class="field">
          <label for="analytics_datetime_from">From Date:</label>
          <input type="datetime-local" id="analytics_datetime_from" name="datetime_from" value="{{ default_from_date }}" class="input" required />
        </div>
        <div class="field">
          <label for="analytics_datetime_to">To Date (optional):</label>
          <input type="datetime-local" id="analytics_datetime_to" name="datetime_to" value="{{ default_to_date }}" class="input" />
        </div>
      </div>
      <div class="report-actions">
        <button type="submit" class="button button-primary">
          <span class="icon icon-snippet"></span>
          <em>View Analytics</em>
        </button>
      </div>
    </form>
    {% else %}
    <div class="permission-notice">
      <p><strong>Note:</strong> Analytics reports require superuser permissions. Please contact your administrator to access this feature.</p>
    </div>
    {% endif %}
  </div>

  <div class="report-card">
    <h2>Export Products to CSV</h2>
    <p>Export your complete product catalog to CSV format for external systems, marketplaces, or data analysis:</p>
    <ul>
      <li>Product information (ID, title, description, status, collection)</li>
      <li>Variant details (SKU, inventory, pricing, attributes)</li>
      <li>Media URLs for product images</li>
      <li>Logistics data (weight, dimensions, HS codes)</li>
      <li>Multi-language support with fallback options</li>
    </ul>
    <div class="report-actions">
      <a href="{% url 'catalog_export_csv' %}" class="button button-primary">
        <span class="icon icon-download"></span>
        <em>Export to CSV</em>
      </a>
    </div>
  </div>

  <div class="report-card">
    <h2>Export Customers to CSV</h2>
    <p>Export comprehensive customer data with actionable business insights for marketing and analytics:</p>
    <ul>
      <li>Registered users and guest customers (email-based)</li>
      <li>Customer demographics and contact information</li>
      <li>Order analytics: total spent, order frequency, customer lifetime value</li>
      <li>Purchase behavior: average order value, highest/lowest orders</li>
      <li>Customer segmentation data: recency, frequency, monetary analysis</li>
      <li>Registration dates and customer lifetime tracking</li>
    </ul>
    <div class="report-actions">
      <a href="{% url 'catalog_customers_export' %}" class="button button-primary">
        <span class="icon icon-group"></span>
        <em>Export Customers</em>
      </a>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %} {{ block.super }}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // Add any interactive functionality here
    console.log('Catalog Reports page loaded')
  })
</script>
{% endblock %} {% block extra_css %} {{ block.super }}
<style>
  .help-block {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 1em;
    margin-bottom: 2em;
  }

  .help-block p {
    margin: 0;
  }

  .report-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 1.5em;
    margin-bottom: 1.5em;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .report-card h2 {
    margin-top: 0;
    margin-bottom: 0.5em;
    color: #333;
    font-size: 1.2em;
  }

  .report-card p {
    margin-bottom: 1em;
    color: #666;
  }

  .report-card ul {
    margin-bottom: 1.5em;
    padding-left: 1.5em;
  }

  .report-card li {
    margin-bottom: 0.25em;
    color: #666;
  }

  .report-form {
    margin-top: 1em;
  }

  .form-row {
    display: flex;
    gap: 1em;
    margin-bottom: 1em;
  }

  .form-row .field {
    flex: 1;
  }

  .field label {
    display: block;
    margin-bottom: 0.25em;
    font-weight: 500;
    color: #333;
  }

  .field .input {
    width: 100%;
    padding: 0.5em;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 0.9em;
  }

  .permission-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 3px;
    padding: 1em;
    margin-top: 1em;
  }

  .permission-notice p {
    margin: 0;
    color: #856404;
  }

  .report-actions {
    border-top: 1px solid #eee;
    padding-top: 1em;
  }

  .button .icon {
    margin-right: 0.5em;
  }

  .button em {
    font-style: normal;
    font-weight: 500;
  }

  .icon {
    display: inline-block;
  }
</style>
{% endblock %}
