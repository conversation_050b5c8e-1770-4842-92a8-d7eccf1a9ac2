{% extends "wagtailadmin/base.html" %}
{% load i18n wagtailadmin_tags %}

{% block titletag %}{{ title }}{% endblock %}

{% block content %}
    <header class="header">
        <div class="row">
            <div class="left">
                <div class="col">
                    <h1 class="header-title">
                        <svg class="icon icon-cog header-title-icon" aria-hidden="true">
                            <use href="#icon-cog"></use>
                        </svg>
                        {{ title }}
                    </h1>
                </div>
            </div>
        </div>
    </header>

    <div class="nice-padding">
        <div class="help-block">
            <p><strong>Debug Mode:</strong> This page shows detailed information about the CSV export process to help diagnose issues.</p>
        </div>

        <div class="debug-section">
            <h3>Export Parameters</h3>
            <table class="debug-table">
                <tr>
                    <td><strong>Locale:</strong></td>
                    <td>{{ debug_info.export_params.locale }}</td>
                </tr>
                <tr>
                    <td><strong>Fallback:</strong></td>
                    <td>{{ debug_info.export_params.fallback }}</td>
                </tr>
                <tr>
                    <td><strong>Product Model:</strong></td>
                    <td>{{ debug_info.export_params.product_model|default:"All Models" }}</td>
                </tr>
                <tr>
                    <td><strong>Media URL:</strong></td>
                    <td>{{ debug_info.export_params.media_url }}</td>
                </tr>
                <tr>
                    <td><strong>CSV Lines Generated:</strong></td>
                    <td>{{ debug_info.csv_lines }}</td>
                </tr>
            </table>
        </div>

        <div class="debug-section">
            <h3>Product Model Diagnosis</h3>
            <div class="diagnosis-content">
                {% for line in debug_info.diagnosis %}
                    <div class="diagnosis-line">{{ line }}</div>
                {% endfor %}
            </div>
        </div>

        {% if debug_info.command_output %}
        <div class="debug-section">
            <h3>Management Command Output</h3>
            <pre class="command-output">{{ debug_info.command_output }}</pre>
        </div>
        {% endif %}

        {% if debug_info.command_errors %}
        <div class="debug-section error-section">
            <h3>Management Command Errors</h3>
            <pre class="command-errors">{{ debug_info.command_errors }}</pre>
        </div>
        {% endif %}

        <div class="debug-section">
            <h3>Troubleshooting Tips</h3>
            <ul>
                <li><strong>No products found:</strong> Check if products are published (live) and have the correct status</li>
                <li><strong>No variants found:</strong> Ensure products have variants created and they are active</li>
                <li><strong>Database errors:</strong> Check database connectivity and permissions</li>
                <li><strong>Model errors:</strong> Verify that product models implement get_variant_model() correctly</li>
                <li><strong>Language issues:</strong> Check if products have content in the selected locale</li>
            </ul>
        </div>

        <div class="actions">
            <a href="{% url 'catalog_export_csv' %}" class="button button-primary">
                <svg class="icon icon-cog" aria-hidden="true">
                    <use href="#icon-cog"></use>
                </svg>
                Try Different Settings
            </a>
            
            <a href="{% url 'catalog_reports' %}" class="button button-secondary">Back to Reports</a>
        </div>
    </div>

    <style>
        .debug-section {
            background: #f8f8f8;
            border: 1px solid #e6e6e6;
            border-radius: 3px;
            padding: 1.5em;
            margin-bottom: 2em;
        }
        
        .debug-section h3 {
            margin-top: 0;
            margin-bottom: 1em;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 0.5em;
        }
        
        .debug-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .debug-table td {
            padding: 8px 12px;
            border: 1px solid #e6e6e6;
            vertical-align: top;
        }
        
        .debug-table td:first-child {
            background-color: #f0f0f0;
            width: 200px;
            font-weight: bold;
        }
        
        .diagnosis-content {
            font-family: monospace;
            background: white;
            padding: 1em;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .diagnosis-line {
            margin-bottom: 0.5em;
            padding: 2px 0;
        }
        
        .diagnosis-line:hover {
            background-color: #f0f0f0;
        }
        
        .command-output, .command-errors {
            background: white;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 1em;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .error-section {
            border-color: #d32f2f;
            background-color: #ffebee;
        }
        
        .error-section h3 {
            color: #d32f2f;
        }
        
        .command-errors {
            border-color: #d32f2f;
            background-color: #fff5f5;
        }
        
        .help-block {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 3px;
            padding: 1em;
            margin-bottom: 2em;
        }
        
        .actions {
            margin-top: 2em;
            padding-top: 1em;
            border-top: 1px solid #e6e6e6;
        }
        
        .actions .button {
            margin-right: 0.5em;
        }
    </style>
{% endblock %}
