{% load shop_tags %}

{% for combo in product.get_existant_variant_combinations %}
{% if forloop.first %}
<table class="listing variants">
  <thead>
    <tr class="table-headers">
      <th>{{ combo.attrs_label }}</th>
      <th>Code (SKU)</th>
      <th>HS code</th>
      <th>Barcode</th>
      <th>Price</th>
      <th>Weight</th>
      <th>Quantity</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    {% endif %}
    <tr>
      {% url 'wagtailadmin_pages:edit' combo.product.id as product_url %}
      {% url 'variant-edit' combo.product.id combo.variant.id as variant_url %}
      {# Attribute `variant.name` is used in title to trigger the caching of product data. #}
      <td><a href="{{ variant_url }}" class="title" title="{{ combo.variant.name }}">{{ combo.values_label }}</a></td>
      <td><code>{{ combo.variant.code }}</code></td>
      <td><code>{{ combo.variant.hs_code }}</code></td>
      <td><code>{{ combo.variant.barcode }}</code></td>
      <td>{% priceformat combo.variant.get_price %}</td>
      <td>{% priceformat combo.variant.get_old_price %}</td>
      <td>{{ combo.variant.get_weight }} kg</td>
      <td>
        {% if combo.variant.quantity == None %}<span class="status-tag primary">max</span>
        {% elif combo.variant.quantity == 0 %}<span class="status-tag">{{ combo.variant.quantity }}</span>
        {% else %}<span class="status-tag primary">{{ combo.variant.quantity }}</span>{% endif %}
      </td>
      <td>
        <div class="button-group">
          <a href="{{ variant_url }}" class="button button--icon text-replace white">
            <svg class="icon icon-tick icon" aria-hidden="true" focusable="false">
              <use href="#icon-cog"></use>
            </svg>Edit variant
          </a>
          <a href="{% url 'variant-delete' combo.product.id combo.variant.id %}?next={{ product_url }}"
            class="button button--icon text-replace no">
            <svg class="icon icon-bin icon" aria-hidden="true" focusable="false">
              <use href="#icon-bin"></use>
            </svg>Delete variant
          </a>
        </div>
      </td>
    </tr>
    {% if forloop.last %}
  </tbody>
</table>
{% endif %}
{% empty %}
<p>No variants.</p>
{% endfor %}

{% if product.id and product.get_missing_variant_combinations|length %}
<div class="actions">
  <a href="javascript:void(0)" class="button bicolor icon icon-plus" type="button"
    onclick="ModalWorkflow({url: '{% url 'variant-add-modal' product.id %}'})">Add variant
  </a>
</div>
{% endif %}
