{% extends "wagtailadmin/base.html" %} 
{% load i18n wagtailadmin_tags %} 

{% block titletag %}{{ title }}{% endblock %} 

{% block content %}
<header class="header">
  <div class="row">
    <div class="left">
      <div class="col">
        <h1 class="header-title">
          <svg class="icon icon-group header-title-icon" aria-hidden="true">
            <use href="#icon-group"></use>
          </svg>
          {{ title }}
        </h1>
      </div>
    </div>
  </div>
</header>

<div class="nice-padding">
  <div class="help-block">
    <p>{{ description }}</p>
    <p>This export includes both registered users and guest customers (those who made purchases without creating accounts).</p>
    
    <h3>Export Options:</h3>
    <ul>
      <li><strong>Basic Customer Data:</strong> Email, name, registration date, address</li>
      <li><strong>With Order Analytics:</strong> Includes purchase history, spending patterns, and customer insights</li>
    </ul>
    
    <h3>Order Analytics Include:</h3>
    <ul>
      <li>Total orders and amount spent</li>
      <li>Average, highest, and lowest order values</li>
      <li>First and last order dates</li>
      <li>Days since last order and customer lifetime</li>
      <li>Customer segmentation insights</li>
    </ul>
  </div>

  {% if messages %}
    {% for message in messages %}
      <div class="messages">
        <div class="message-content">
          <div class="message {{ message.tags }}">
            <div class="message-text">{{ message }}</div>
          </div>
        </div>
      </div>
    {% endfor %}
  {% endif %}

  <form method="post" class="export-form">
    {% csrf_token %}
    
    <div class="field-row">
      <div class="field">
        <label class="field-label">
          <input type="checkbox" name="include_orders" id="id_include_orders" checked>
          Include Order Analytics
        </label>
        <div class="help-text">
          Include detailed order analytics and customer insights. Uncheck for basic customer data only.
        </div>
      </div>
    </div>

    <div class="field-row">
      <div class="field">
        <label for="id_date_from" class="field-label">Date From (Optional)</label>
        <input type="date" name="date_from" id="id_date_from" class="field-input">
        <div class="help-text">
          Filter customers by their registration/first order date. Leave empty to include all customers.
        </div>
      </div>
    </div>

    <div class="field-row">
      <div class="field">
        <label for="id_date_to" class="field-label">Date To (Optional)</label>
        <input type="date" name="date_to" id="id_date_to" class="field-input">
        <div class="help-text">
          Filter customers up to this date. Leave empty to include all customers up to today.
        </div>
      </div>
    </div>

    <div class="actions">
      <button type="submit" class="button button-primary">
        <span class="icon icon-download"></span>
        Export Customers to CSV
      </button>
      
      <a href="{% url 'catalog_reports' %}" class="button button-secondary">
        <span class="icon icon-arrow-left"></span>
        Back to Reports
      </a>
    </div>
  </form>

  <div class="help-block" style="margin-top: 2rem;">
    <h3>CSV Output Format:</h3>
    <p>The exported CSV will contain the following columns:</p>
    
    <h4>Basic Customer Data:</h4>
    <ul>
      <li><strong>Customer Type:</strong> "Registered User" or "Guest Customer"</li>
      <li><strong>Email:</strong> Customer's email address</li>
      <li><strong>First Name, Last Name, Full Name:</strong> Customer names</li>
      <li><strong>Registration Date:</strong> When they first registered or made their first order</li>
      <li><strong>Is Activated:</strong> Account activation status (N/A for guests)</li>
      <li><strong>Address:</strong> Customer's address information</li>
    </ul>
    
    <div id="order-analytics-info" style="display: block;">
      <h4>Order Analytics (when enabled):</h4>
      <ul>
        <li><strong>Total Orders:</strong> Number of orders placed</li>
        <li><strong>Total Spent:</strong> Total amount spent across all orders</li>
        <li><strong>Average Order Value:</strong> Average amount per order</li>
        <li><strong>First/Last Order Date:</strong> Dates of first and most recent orders</li>
        <li><strong>Last Order Amount:</strong> Value of most recent order</li>
        <li><strong>Highest/Lowest Order Amount:</strong> Maximum and minimum order values</li>
        <li><strong>Days Since Last Order:</strong> Recency of last purchase</li>
        <li><strong>Customer Lifetime:</strong> Days since first registration/order</li>
      </ul>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const includeOrdersCheckbox = document.getElementById('id_include_orders');
  const orderAnalyticsInfo = document.getElementById('order-analytics-info');
  
  function toggleOrderAnalyticsInfo() {
    if (includeOrdersCheckbox.checked) {
      orderAnalyticsInfo.style.display = 'block';
    } else {
      orderAnalyticsInfo.style.display = 'none';
    }
  }
  
  includeOrdersCheckbox.addEventListener('change', toggleOrderAnalyticsInfo);
  toggleOrderAnalyticsInfo(); // Initial state
});
</script>

<style>
.export-form .field-row {
  margin-bottom: 1.5rem;
}

.export-form .field-label {
  display: block;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.export-form .field-input {
  width: 100%;
  max-width: 300px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.export-form .help-text {
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.25rem;
}

.export-form .actions {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.export-form .actions .button {
  margin-right: 1rem;
}

.help-block h3, .help-block h4 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.help-block ul {
  margin-left: 1rem;
}

.help-block li {
  margin-bottom: 0.25rem;
}
</style>
{% endblock %}
