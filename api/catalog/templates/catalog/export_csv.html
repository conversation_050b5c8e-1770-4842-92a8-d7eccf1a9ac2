{% extends "wagtailadmin/base.html" %} {% load i18n wagtailadmin_tags %} {% block titletag %}{{ title }}{% endblock %} {% block content %}
<header class="header">
  <div class="row">
    <div class="left">
      <div class="col">
        <h1 class="header-title">
          <svg class="icon icon-doc-empty header-title-icon" aria-hidden="true">
            <use href="#icon-doc-empty"></use>
          </svg>
          {{ title }}
        </h1>
      </div>
    </div>
  </div>
</header>

<div class="nice-padding">
  <div class="help-block">
    <p>Export your product catalog to CSV format for external systems, marketplaces, or data analysis.</p>
    <p>The export includes product details, variants, pricing (with proper decimal formatting), inventory, media URLs, and both frontend and backend API URLs.</p>
  </div>

  <form method="post" class="form">
    {% csrf_token %}

    <div class="row">
      <div class="col6">
        <fieldset>
          <legend>Export Settings</legend>

          <div class="field">
            <label for="id_locale">Primary Locale:</label>
            <select id="id_locale" name="locale" class="input">
              <option value="en" selected>English (en)</option>
              <option value="es">Spanish (es)</option>
            </select>
            <div class="help">Primary language for product data</div>
          </div>

          <div class="field">
            <label for="id_fallback">Fallback Locale:</label>
            <select id="id_fallback" name="fallback" class="input">
              <option value="es" selected>Spanish (es)</option>
              <option value="en">English (en)</option>
            </select>
            <div class="help">Language to use when primary locale data is missing</div>
          </div>

          <div class="field">
            <label for="id_product_model">Product Model Filter:</label>
            <select id="id_product_model" name="product_model" class="input">
              <option value="">All Product Types</option>
              {% for model in product_models %}
              <option value="{{ model.name }}">{{ model.verbose_name }}</option>
              {% endfor %}
            </select>
            <div class="help">Filter by specific product type (optional)</div>
          </div>
        </fieldset>
      </div>

      <div class="col6">
        <fieldset>
          <legend>Media Settings</legend>

          <div class="field">
            <label for="id_media_url">Media Base URL:</label>
            <input type="url" id="id_media_url" name="media_url" value="{{ default_media_url }}" class="input" required />
            <div class="help">Base URL for product images (will be prepended to relative paths)</div>
          </div>
        </fieldset>

        <fieldset>
          <legend>Output Format</legend>

          <div class="field">
            <div class="field-content">
              <div class="input">
                <input type="radio" id="id_download" name="download_format" value="download" checked />
                <label for="id_download">Download CSV File</label>
              </div>
              <div class="input">
                <input type="radio" id="id_preview" name="download_format" value="preview" />
                <label for="id_preview">Preview First 10 Rows</label>
              </div>
              <div class="input">
                <input type="radio" id="id_debug" name="download_format" value="debug" />
                <label for="id_debug">Debug Mode (Show Diagnostic Info)</label>
              </div>
            </div>
            <div class="help">Choose to download the complete file, preview a sample, or see diagnostic information</div>
          </div>
        </fieldset>
      </div>
    </div>

    <div class="actions">
      <button type="submit" class="button button-primary">Export Products</button>

      <a href="{% url 'catalog_reports' %}" class="button button-secondary"> Back to Reports </a>
    </div>
  </form>
</div>
{% endblock %} {% block extra_css %} {{ block.super }}
<style>
  .help-block {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 1em;
    margin-bottom: 2em;
  }

  .help-block p {
    margin: 0 0 0.5em 0;
  }

  .help-block p:last-child {
    margin-bottom: 0;
  }

  fieldset {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 1em;
    margin-bottom: 1em;
  }

  legend {
    font-weight: bold;
    padding: 0 0.5em;
    color: #333;
  }

  .field {
    margin-bottom: 1em;
  }

  .field:last-child {
    margin-bottom: 0;
  }

  .help {
    font-size: 0.9em;
    color: #666;
    margin-top: 0.25em;
  }

  .input input[type='radio'] {
    margin-right: 0.5em;
  }

  .input label {
    display: inline;
    font-weight: normal;
  }

  .actions {
    margin-top: 2em;
    padding-top: 1em;
    border-top: 1px solid #ddd;
  }

  .button .icon {
    margin-right: 0.5em;
  }
</style>
{% endblock %}
