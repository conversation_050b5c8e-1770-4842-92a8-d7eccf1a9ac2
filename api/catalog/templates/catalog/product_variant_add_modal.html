{% include "wagtailadmin/shared/header.html" with title="Add variant" icon="snippet" tabbed=0 merged=0 %}

<div class="nice-padding">
  {% for combo in product.get_missing_variant_combinations %}
    {% if forloop.first %}
    <table class="listing">
      <thead>
        <tr class="table-headers">
          <th>{{ combo.attrs_label }}</th>
          <th>Actions</th>
        <tr>
      </thead>
      <tbody>
    {% endif %}
        <tr>
          <td class="title">{{ combo.values_label }}</td>
          <td>
            <div class="button-group">
              <a href="{% url 'variant-create' product.id combo.identifier %}" class="button">Create</a>
              <a href="{% url 'variant-create' product.id combo.identifier %}?edit=1" class="button">Create and edit</a>
            </div>
          </td>
        </tr>
    {% if forloop.last %}
      </tbody>
    </table>
    {% endif %}
  {% empty %}
    <p>No combinations available.</p>
  {% endfor %}
</div>
