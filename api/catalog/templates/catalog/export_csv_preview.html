{% extends "wagtailadmin/base.html" %}
{% load i18n wagtailadmin_tags %}

{% block titletag %}{{ title }}{% endblock %}

{% block content %}
    <header class="header">
        <div class="row">
            <div class="left">
                <div class="col">
                    <h1 class="header-title">
                        <svg class="icon icon-doc-empty header-title-icon" aria-hidden="true">
                            <use href="#icon-doc-empty"></use>
                        </svg>
                        {{ title }}
                    </h1>
                </div>
            </div>
        </div>
    </header>

    <div class="nice-padding">
        <div class="help-block">
            <p><strong>Export Preview:</strong> Showing {{ showing_lines }} of {{ total_lines }} total rows</p>
            <p><strong>Settings:</strong> 
                Locale: {{ export_params.locale }}{% if export_params.fallback %} (fallback: {{ export_params.fallback }}){% endif %}
                {% if export_params.product_model %} | Product Type: {{ export_params.product_model }}{% endif %}
            </p>
        </div>

        <div class="csv-preview-container">
            <table class="csv-preview-table">
                {% for row in csv_data %}
                    <tr {% if forloop.first %}class="header-row"{% endif %}>
                        {% for cell in row %}
                            {% if forloop.first %}
                                <th>{{ cell|truncatechars:30 }}</th>
                            {% else %}
                                <td title="{{ cell }}">{{ cell|truncatechars:30 }}</td>
                            {% endif %}
                        {% endfor %}
                    </tr>
                {% endfor %}
            </table>
        </div>

        {% if total_lines > showing_lines %}
            <div class="help-block">
                <p><strong>Note:</strong> This preview shows only the first {{ showing_lines }} rows. 
                   The complete export contains {{ total_lines }} rows total.</p>
            </div>
        {% endif %}

        <div class="actions">
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <input type="hidden" name="locale" value="{{ export_params.locale }}">
                <input type="hidden" name="fallback" value="{{ export_params.fallback }}">
                <input type="hidden" name="product_model" value="{{ export_params.product_model }}">
                <input type="hidden" name="media_url" value="{{ export_params.media_url }}">
                <input type="hidden" name="download_format" value="download">
                
                <button type="submit" class="button button-primary">
                    <svg class="icon icon-download" aria-hidden="true">
                        <use href="#icon-download"></use>
                    </svg>
                    Download Complete CSV
                </button>
            </form>
            
            <a href="{% url 'catalog_export_csv' %}" class="button button-secondary">
                <svg class="icon icon-cog" aria-hidden="true">
                    <use href="#icon-cog"></use>
                </svg>
                Change Settings
            </a>
            
            <a href="{% url 'catalog_reports' %}" class="button button-secondary">Back to Reports</a>
        </div>
    </div>

    <style>
        .csv-preview-container {
            overflow-x: auto;
            margin: 2em 0;
            border: 1px solid #e6e6e6;
            border-radius: 3px;
        }
        
        .csv-preview-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            min-width: 800px;
        }
        
        .csv-preview-table th,
        .csv-preview-table td {
            padding: 8px 12px;
            border: 1px solid #e6e6e6;
            text-align: left;
            vertical-align: top;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .csv-preview-table .header-row th {
            background-color: #f8f8f8;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .csv-preview-table tr:nth-child(even) {
            background-color: #fafafa;
        }
        
        .csv-preview-table tr:hover {
            background-color: #f0f0f0;
        }
        
        .help-block {
            background: #f8f8f8;
            border: 1px solid #e6e6e6;
            border-radius: 3px;
            padding: 1em;
            margin-bottom: 1em;
        }
        
        .actions {
            margin-top: 2em;
            padding-top: 1em;
            border-top: 1px solid #e6e6e6;
        }
        
        .actions .button {
            margin-right: 0.5em;
        }
        
        .actions form {
            display: inline-block;
        }
    </style>
{% endblock %}
