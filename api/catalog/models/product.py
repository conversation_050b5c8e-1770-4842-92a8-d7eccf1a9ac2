from __future__ import annotations

import itertools
import random
import secrets
from decimal import Decimal
from typing import Iterator, Optional

from django.conf import settings
from django.core.cache import cache as default_cache
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.db import models, transaction
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.template.loader import render_to_string
from django.urls import reverse
from modelcluster.fields import Parental<PERSON>ey
from modelcluster.models import ClusterableModel
from wagtail.admin.edit_handlers import (
    FieldPanel,
    InlinePanel,
    MultiFieldPanel,
    PageChooserPanel,
)
from wagtail.core.fields import RichTextField
from wagtail.core.signals import page_published, page_unpublished
from wagtail.images.edit_handlers import ImageChooserPanel
from wagtail_localize.fields import SynchronizedField
from wagtailorderable.models import Orderable

from basic.models import PublishedDateMixin
from catalog.models.inventory import InventoryMixin
from core import cache
from core.models import CorePage
from core.serializers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, force_dict
from country.models import CountryField
from extra.edit_handlers import ReadOnlyPanel

from .attribute import ProductAttributeMixin
from .variant import VariantAttribute, VariantAttributeValue, VariantCombination


class InvalidateProductsMixin:
    """
    Mixin that adds product related invalidation urls to page.
    """

    def get_cached_api_urls(self) -> list[str]:
        urls = super().get_cached_api_urls()  # type: ignore
        urls.append(reverse("product-list"))
        return urls


class ProductMediaMixin(Orderable):
    """
    Add media to product or variant.
    Requires `product` ParentalKey with `media` reverse name.
    """

    class ImageSize(models.TextChoices):
        SMALL = "200x233", "small"
        MEDIUM = "580x677", "medium"
        LARGE = "800x933", "large"
        HUGE = "1200x1400", "huge"

    image = models.ForeignKey(
        "wagtailimages.Image",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="+",
        help_text=(
            f"Image will be cropped to multiple sizes and should uploaded with "
            f'at least "{ImageSize.HUGE}px" dimensions.'
        ),
    )

    panels = [
        ImageChooserPanel("image"),
    ]

    class Meta(Orderable.Meta):
        abstract = True
        verbose_name = "Media"
        verbose_name_plural = "Media"

    def clean(self):
        super().clean()
        if self.image is None:
            raise ValidationError({"image": "Missing image."})


class ProductMediaDataMixin(models.Model):
    """
    Product mixin used for saving `media_data` on a product.
    """

    media_data = models.JSONField(null=True, editable=False)

    debug_panels = [
        ReadOnlyPanel("media_data"),
    ]

    class Meta:
        abstract = True

    def get_media_data(self):
        if self.media_data is None:
            self.save_media_data()
        return self.media_data

    def save_media_data(self, commit: bool = True):
        """
        Save related media data with generated thumbnails as JSON.
        """
        self.media_data = []
        for media in self.media.all():
            data = {}
            for size, name in ProductMedia.ImageSize.choices:
                field = ImageRenditionField(f"fill-{size}-c100")
                data[name] = force_dict(field.to_representation(media.image))
            self.media_data.append(data)
        if commit:
            self.save(update_fields=["media_data"])


class Product(
    InvalidateProductsMixin,
    PublishedDateMixin,
    ProductMediaDataMixin,
    CorePage,
):
    details = RichTextField(
        "Details",
        blank=True,
        features=["bold", "italic", "link", "ul"],
    )

    guides = RichTextField(
        "Guia de Tallas",
        blank=True,
        features=["bold", "italic", "link", "ul", "image"],
    )

    product_description = RichTextField(
        "Product description",
        blank=True,
        features=["bold", "italic", "link", "ul"],
    )

    base_price = models.DecimalField(
        "Base price (€)",
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Base product price in EUR.",
    )

    old_price = models.DecimalField(
        "Old price (€)",
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Old product price in EUR, leave empty to hide.",
    )

    access_code = models.CharField(
        "Allows product to only be added to basket if code is present.",
        max_length=15,
        blank=True,
        null=True,
        help_text="Add only if you want to make product accessible only by this code",
    )

    base_weight = models.DecimalField(
        "Base weight (kg)",
        max_digits=10,
        decimal_places=3,
        default=0,
        help_text="Base product weight in kilograms.",
    )

    origin_country = CountryField(
        "Origin country",
        help_text="Select origin country for the product.",
    )

    local_shipping_only = models.BooleanField(
        "Local shipping only",
        default=False,
        help_text="Limit product to only be allowed for purchase locally in Spain.",
    )

    override_translatable_fields = CorePage.override_translatable_fields + [
        SynchronizedField("local_shipping_only"),
    ]

    shipping_region = ParentalKey(
        "shop.ShippingRegion", blank=True, null=True, related_name="products"
    )

    panels = [
        FieldPanel("details"),
        FieldPanel("guides"),
        # FieldPanel("product_description"),
        FieldPanel("base_price"),
        FieldPanel("old_price"),
        FieldPanel("access_code"),
        FieldPanel("base_weight"),
        MultiFieldPanel(
            [
                FieldPanel("origin_country"),
                FieldPanel("local_shipping_only"),
            ],
            heading="Shipping",
        ),
        FieldPanel("shipping_region", heading="Shipping region limit"),
        ReadOnlyPanel("admin_stats_display", heading="Stats"),
        ReadOnlyPanel(
            "admin_variant_list_display",
            heading="Variants",
            classname="catalog-product-variant-list",
        ),
        InlinePanel("media", heading="Media"),
        InlinePanel("relations", heading="Related products"),
    ]

    content_panels = CorePage.content_panels + panels

    if settings.DEBUG:
        content_panels += ProductMediaDataMixin.debug_panels

    parent_page_types = ["catalog.ProductCollection"]
    page_serializer = "catalog.serializers.ProductSerializer"
    page_summary_serializer = "catalog.serializers.ProductSummarySerializer"
    is_creatable = False

    class Meta:
        pass

    @property
    def name(self):
        return self.title

    @property
    def image(self):
        media = self.media.all().first()
        if media and media.image:
            return media.image

    def get_price(self, request=None) -> Decimal:
        return self.base_price or Decimal(0)

    def get_old_price(self, request=None) -> Decimal:
        return self.old_price or Decimal(0)

    # def get_access_code(self, request=None) -> Optional[str]:
    #     return self.access_code or None

    def get_variant_model(self) -> type[ProductVariant]:
        """
        Should return the class of the variant model.
        """
        raise NotImplementedError("Must specify `get_variant_model` on a product")

    def get_variants(self) -> models.QuerySet[ProductVariant]:
        """
        Returns variants for this product.
        """
        return self.get_variant_model().objects.filter(
            product_translation_key=self.translation_key
        )

    def get_related_products(self) -> list[Product]:
        """
        Returns a list of related products.
        """
        queryset = self.relations.all().select_related("to")
        return [x.to for x in queryset if x.to.live]

    def get_variant_combinations(self) -> list[VariantCombination]:
        """
        Returns all possible combinations of variants for this product.
        """
        attrs = self.get_variant_model().get_variant_attributes(self.locale_id)
        variants = tuple(self.get_variants())
        variants_combos = [x.to_variant_combination(self) for x in variants]
        combos = []

        for values in itertools.product(*attrs):
            combo = VariantCombination(self, values)

            # Attach variant instance for combination if found
            for index, variant in enumerate(variants):
                if variants_combos[index] == combo:
                    combo.set_variant(variant)
                    break

            combos.append(combo)

        return combos

    def get_missing_variant_combinations(self) -> list[VariantCombination]:
        """
        Returns all missing variant combinations for this product.
        """
        return [x for x in self.get_variant_combinations() if not x.variant]

    def get_existant_variant_combinations(self) -> list[VariantCombination]:
        """
        Returns all existant variant combinations for this product.
        """
        return [x for x in self.get_variant_combinations() if x.variant]

    def update_variants(self):
        """
        Update product variants with product data.
        """
        variants = []
        fields = []
        for variant in self.get_variants():
            fields = variant.populate_from_product(self)
            variants.append(variant)
        count = len(variants)
        if count == 1:
            variants[0].save(update_fields=fields)
        elif count > 1:
            self.get_variant_model().objects.bulk_update(variants, fields)

    def get_cached_api_urls(self) -> list[str]:
        """
        Invalidate product collection as well.
        """
        urls = super().get_cached_api_urls()
        pages_url = reverse("page-list")
        collection = self.get_parent()
        urls.append(f"{pages_url}{collection.id}/")
        urls.append(f"{pages_url}find{collection.url_path}")
        return urls

    @property
    def admin_stats_display(self):
        from stats.utils import admin_format_stats

        return admin_format_stats(product=self)

    @property
    def admin_variant_list_display(self):
        if not self.pk:
            return "-"
        return render_to_string("catalog/product_variant_list.html", {"product": self})


class ProductMedia(ProductMediaMixin):
    product = ParentalKey(Product, related_name="media")


class ProductRelation(Orderable):
    product = ParentalKey(Product, related_name="relations")
    to = models.ForeignKey(Product, on_delete=models.CASCADE)

    panels = [
        PageChooserPanel("to"),
    ]

    class Meta(Orderable.Meta):
        verbose_name = "Related product"
        verbose_name_plural = "Related products"


class ProductVariant(InventoryMixin, ProductMediaDataMixin, ClusterableModel):
    """
    Actual product that gets added to the basket. Should contain all
    product related data to be handled separately from it's "parent" product.
    """

    product_translation_key = models.UUIDField(db_index=True, editable=False)

    code = models.CharField(
        "Product code (SKU)",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Unique product code. Leave empty to auto-generate.",
    )

    _hs_code = models.CharField(
        "Harmonized System code (HS)",
        max_length=8,
        blank=True,
        help_text='If left empty, first 8 letters of "Code" field are used.',
    )

    ean13 = models.CharField(
        "EAN-13 Barcode",
        max_length=13,
        null=True,
        unique=True,
        editable=False,
        db_index=True,  # Add index for better query performance
        validators=[
            RegexValidator(
                regex="^[0-9]{13}$",
                message="EAN-13 must be exactly 13 digits",
            ),
        ],
    )

    price = models.DecimalField(
        "Price (€)",
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Variant price in EUR, leave empty to use base product price.",
    )

    old_price = models.DecimalField(
        "Old price (€)",
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Old variant price in EUR, leave empty to use base product's old price. If set, overrides base product's old price. If set to 0, hides old price.",
    )

    weight = models.DecimalField(
        "Weight (kg)",
        max_digits=10,
        decimal_places=3,
        blank=True,
        null=True,
        help_text="Variant weight in kilograms, leave empty to use base product weight.",
    )

    combo_id = models.CharField(max_length=255, blank=True, editable=False)
    combo_data = models.JSONField(null=True, editable=False)
    product_data = models.JSONField(null=True, editable=False)

    # Current language of in-memory variant instance
    _language: Optional[str] = None
    _name: Optional[str] = None

    panels = [
        ReadOnlyPanel("admin_product_button_display", heading="Base product"),
        MultiFieldPanel(
            [
                FieldPanel("code"),
                FieldPanel("_hs_code"),
                ReadOnlyPanel("ean13", heading="EAN-13 Barcode"),
            ],
            heading="Codes",
        ),
        MultiFieldPanel(
            [
                FieldPanel("price"),
                FieldPanel("old_price"),
                FieldPanel("weight"),
            ],
            heading="Settings",
        ),
        *InventoryMixin.panels,
        ReadOnlyPanel("admin_stats_display", heading="Stats"),
        InlinePanel("media", heading="Media"),
    ]

    debug_panels = [
        ReadOnlyPanel("combo_id"),
        ReadOnlyPanel("combo_data"),
        ReadOnlyPanel("product_data"),
        *ProductMediaDataMixin.debug_panels,
    ]

    if settings.DEBUG:
        panels += debug_panels

    class Meta:
        verbose_name = "Variant"
        verbose_name_plural = "Variants"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Generate unique code if missing
        if not self.code:
            self.code = self.generate_unique_code()

        # Invalidate media data if not explicitly saved.
        if kwargs.get("update_fields", None) != ["media_data"]:
            self.media_data = None

        super().save(*args, **kwargs)
        # Invalidate related products
        cache.invalidate_cached_responses_for_pages(self.get_products())

    @property
    def name(self):
        if self._name is None:
            product_data = self.get_localized_data("product_data")
            combo_data = self.get_localized_data("combo_data")
            product_name = product_data.get("name", "")
            values_label = combo_data.get("values_label", str(self.pk))
            self._name = f"{product_name} ({values_label})"
        return self._name

    @property
    def language(self):
        return self._language or settings.LANGUAGE_CODE

    @property
    def hs_code(self):
        return self._hs_code or self.code[:8]

    @property
    def barcode(self) -> str:
        """
        Get or generate the EAN-13 barcode.
        """
        if not self.ean13:
            self.ean13 = self.generate_ean13()
            self.save(update_fields=["ean13"])
        return self.ean13

    @staticmethod
    def validate_ean13(ean13: str) -> bool:
        """
        Validate EAN-13 checksum
        """
        if not ean13 or not ean13.isdigit() or len(ean13) != 13:
            return False

        total = 0
        for i in range(12):
            digit = int(ean13[i])
            total += digit * (3 if i % 2 else 1)

        check_digit = (10 - (total % 10)) % 10
        return check_digit == int(ean13[-1])

    def generate_ean13(self) -> str:
        """
        Generate a unique EAN-13 barcode.
        First 12 digits are random, last digit is a check digit.
        Ensures uniqueness across all variants.
        """
        while True:
            # Generate 12 random digits (we'll calculate check digit)
            digits = [str(random.randint(0, 9)) for _ in range(12)]

            # Calculate check digit
            total = 0
            for i in range(12):
                digit = int(digits[i])
                total += digit * (3 if i % 2 else 1)

            check_digit = (10 - (total % 10)) % 10

            # Create complete EAN-13
            ean13 = "".join(digits) + str(check_digit)

            # Check if it's unique
            if not type(self).objects.filter(ean13=ean13).exists():
                return ean13

    @property
    def origin_country(self):
        product_data = self.get_localized_data("product_data")
        return product_data.get("origin_country", None)

    def set_language(self, language: str):
        if language not in [x[0] for x in settings.WAGTAIL_CONTENT_LANGUAGES]:
            raise ValueError("Invalid language.")
        self._language = language

    def get_price(self, request=None) -> Decimal:
        if self.price is None:
            from decimal import Decimal  # noqa

            return eval(self.get_localized_data("product_data").get("base_price", "0"))
        return self.price

    def get_old_price(self, request=None) -> Decimal:
        if self.old_price is None:
            from decimal import Decimal  # noqa

            return eval(self.get_localized_data("product_data").get("old_price", "0"))
        return self.old_price

    def get_weight(self) -> Decimal:
        if self.weight is None:
            from decimal import Decimal  # noqa

            return eval(self.get_localized_data("product_data").get("base_weight", "0"))
        return self.weight

    def get_products(self, **kwargs):
        return Product.objects.filter(
            translation_key=self.product_translation_key, **kwargs
        ).specific()

    def get_media_data(self):
        data = super().get_media_data()
        if not data:
            product_data = self.get_localized_data("product_data")
            data = product_data.get("media_data", [])
        return data

    def get_localized_data(self, field_name: str, fallback: bool = True) -> dict:
        """
        Returns localized data for given field and language.
        Fallback to default language or the first occurence or None.
        """

        def _get_data_field() -> dict:
            return dict(getattr(self, field_name) or {})

        data_field = _get_data_field()

        if not self.language:
            self.set_language(settings.WAGTAIL_CONTENT_LANGUAGES[0][0])

        # If data is missing in language, try to find it or set to None explicitly
        if self.language not in data_field:
            try:
                product = Product.objects.get(
                    translation_key=self.product_translation_key,
                    locale__language_code=self.language,
                )
                self.populate_from_product(product.specific, commit=True)
                data_field = _get_data_field()
            except Product.DoesNotExist:
                data_field[self.language] = None
                setattr(self, field_name, data_field)
                self.save(update_fields=[field_name])

        data = data_field.get(self.language, None)

        if not data and fallback:
            if settings.LANGUAGE_CODE in data_field:
                data = data_field[settings.LANGUAGE_CODE]
            elif data_field:
                data = list(data_field.values())[0]
        if not isinstance(data, dict):
            data = {}
        return data

    def generate_unique_code(self):
        code = secrets.token_urlsafe(8)
        if self._meta.model.objects.filter(code=code).exists():
            return self.generate_unique_code()
        return code

    def populate_from_product(
        self,
        product: Product,
        combo: Optional[VariantCombination] = None,
        commit: bool = False,
    ) -> list[str]:
        """
        Populate variant from product. Returns a list of populated fields.
        """
        if not combo:
            combo = self.to_variant_combination(product)

        self.product_translation_key = product.translation_key
        self.combo_id = combo.identifier

        # Store combo data for language
        if not self.combo_data:
            self.combo_data = {}
        self.combo_data[product.locale.language_code] = combo.to_dict()

        # Store product data for language
        if not self.product_data:
            self.product_data = {}
        self.product_data[product.locale.language_code] = {
            "id": product.id,
            "url_path": product.url_path,
            "live": product.live,
            "name": product.name,
            "slug": product.slug,
            "base_price": repr(product.get_price()),
            "old_price": repr(product.get_old_price()),
            "base_weight": repr(product.base_weight),
            "details": product.details,
            "guides": product.guides,
            "product_description": product.product_description,
            "origin_country": product.origin_country,
            "local_shipping_only": product.local_shipping_only,
            "media_data": product.media_data,
            "limited_countries": (
                product.shipping_region.countries if product.shipping_region else None
            ),
        }

        fields = ["product_translation_key", "combo_id", "combo_data", "product_data"]
        if commit:
            self.save(update_fields=fields)
        return fields

    def to_variant_combination(self, product: Product) -> VariantCombination:
        """
        Returns product variant as variant combination object.
        """
        attrs = tuple(self.get_variant_attributes(product.locale_id))

        values = []
        for attr in attrs:
            value = getattr(self, attr.code)
            if hasattr(value, "locale"):
                value = value.get_translation_or_none(product.locale) or value
            if isinstance(value, ProductAttributeMixin):
                values.append(VariantAttributeValue.from_instance(value))
            else:
                values.append(VariantAttributeValue(str(value)))

        combo = VariantCombination(product, tuple(values))
        combo.set_variant(self)
        return combo

    @classmethod
    def get_variant_attributes(cls, locale_id: int) -> Iterator[VariantAttribute]:
        """
        Should return an interator of variant attributes for this variant.
        """
        raise NotImplementedError("Must implement `get_variant_attributes` method")

    @classmethod
    def from_variant_combination(
        cls,
        combo: VariantCombination,
        **kwargs,
    ) -> ProductVariant:
        """
        Creates a variant instance from a given combination.
        """
        variant = cls(**kwargs)
        variant.populate_from_product(combo.product, combo=combo)
        # Assign attribute values to a new variant
        for index, attr in enumerate(combo.attrs):
            value = combo.values[index].value
            if attr.queryset:
                value = attr.queryset.filter(code=value).first()
            setattr(variant, attr.code, value)
        return variant

    @property
    def admin_product_button_display(self):
        if not self.pk:
            return "-"
        return render_to_string(
            "catalog/variant_product_button.html",
            {"product": self.get_localized_data("product_data")},
        )

    @property
    def admin_stats_display(self):
        from stats.utils import admin_format_stats

        return admin_format_stats(variant=self)


class ProductVariantMedia(ProductMediaMixin):
    variant = ParentalKey(ProductVariant, related_name="media")


@transaction.atomic
def save_product_related_data(product: Product):
    product.save_media_data()
    product.update_variants()


@receiver(page_published, dispatch_uid="on_product_published")
@receiver(page_unpublished, dispatch_uid="on_product_unpublished")
def on_product_published_or_unpublished(sender, **kwargs):
    instance = kwargs["instance"].specific_deferred
    if isinstance(instance, Product):
        save_product_related_data(instance)


def invalidate_variant_cache(variant: ProductVariant) -> None:
    """
    Invalidate cache for a specific variant

    Call this function whenever a variant is updated or deleted
    """
    if variant.code:
        # Also invalidate the barcode cache if used separately
        default_cache.delete(f"barcode-{variant.code}")


@receiver(post_save, sender=ProductVariant)
def invalidate_variant_cache_on_save(sender, instance, **kwargs):
    """Invalidate cache when a variant is saved or updated"""
    invalidate_variant_cache(instance)


@receiver(post_delete, sender=ProductVariant)
def invalidate_variant_cache_on_delete(sender, instance, **kwargs):
    """Invalidate cache when a variant is deleted"""
    invalidate_variant_cache(instance)
