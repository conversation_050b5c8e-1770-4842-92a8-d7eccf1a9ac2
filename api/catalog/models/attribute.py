from django.core.validators import RegexValidator
from django.db import models, transaction
from wagtail.admin.edit_handlers import FieldPanel
from wagtail.core.models import TranslatableMixin
from wagtail_localize.fields import Translatable<PERSON>ield
from wagtailorderable.models import Orderable

from core import cache


def validate_attribute_code(value):
    validator = RegexValidator(
        r"(__)|\,",
        inverse_match=True,
        message=(
            'Double underscore "__" and comma "," characters are reserved '
            "for internal use."
        ),
    )
    validator(value)


class ProductAttributeMixin(Orderable):
    code = models.CharField(
        "Code",
        max_length=32,
        db_index=True,
        validators=[validate_attribute_code],
        help_text="A unique code to identify this attribute.",
    )
    name = models.CharField(
        "Name",
        max_length=128,
        blank=True,
        help_text="Display name for the attribute, defaults to 'code'.",
    )

    panels = [
        FieldPanel("code"),
        FieldPanel("name"),
    ]

    class Meta(Orderable.Meta):
        abstract = True

    def save(self, *args, **kwargs):
        if not self.name:
            self.name = self.code
        super().save(*args, **kwargs)
        self.invalidate_related_products()

    def __str__(self):
        # Must return `code` as str() is used for comparisons againt variant attributes
        return self.code

    @transaction.atomic
    def invalidate_related_products(self):
        """
        Invalidate all products/variants that could be affected by this attribute.
        """
        from .catalog import CatalogPage

        def _invalidated(obj):
            obj.combo_data = None
            return obj

        for rel in self._meta.related_objects:
            objs = [_invalidated(x) for x in rel.related_model.objects.all()]
            if len(objs):
                rel.related_model.objects.bulk_update(objs, ["combo_data"])

        cache.invalidate_cached_responses_for_pages(CatalogPage.objects.all())


class ProductColor(ProductAttributeMixin, TranslatableMixin):
    translatable_fields = [
        TranslatableField("name"),
    ]

    panels = ProductAttributeMixin.panels

    class Meta(ProductAttributeMixin.Meta, TranslatableMixin.Meta):
        verbose_name = "Color"


class ProductSize(ProductAttributeMixin):
    panels = ProductAttributeMixin.panels

    class Meta(ProductAttributeMixin.Meta):
        verbose_name = "Size"


class ProductNumericSize(ProductAttributeMixin):
    panels = ProductAttributeMixin.panels

    class Meta(ProductAttributeMixin.Meta):
        verbose_name = "Numeric size"
