from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Optional

from django.db import OperationalError, models, transaction
from django.utils import timezone
from django.utils.safestring import mark_safe
from salesman.basket.models import Basket
from wagtail.admin.edit_handlers import FieldPanel, MultiFieldPanel

from extra.edit_handlers import ReadOnlyPanel

if TYPE_CHECKING:
    from catalog.models.product import ProductVariant

logger = logging.getLogger(__name__)


class InventoryMixin(models.Model):
    quantity = models.PositiveIntegerField(
        "Quantity",
        blank=True,
        null=True,
        help_text="Leave empty to set to unlimited.",
    )
    reserved_quantity = models.IntegerField(default=0, editable=False)

    panels = [
        MultiFieldPanel(
            [
                FieldPanel("quantity"),
                ReadOnlyPanel("admin_reserved_quantity", heading="Reserved"),
                ReadOnlyPanel("admin_available_quantity", heading="Available"),
            ],
            heading="Inventory",
        )
    ]

    class Meta:
        abstract = True

    @property
    def available_quantity(self):
        if self.quantity is None:
            return 99999
        return max(self.quantity - self.reserved_quantity, 0)

    @property
    def admin_reserved_quantity(self):
        value = self.reserved_quantity
        extraclass = " primary" if self.reserved_quantity > 0 else ""
        return mark_safe(f'<span class="status-tag{extraclass}">{value}</span>')

    @property
    def admin_available_quantity(self):
        value = str(self.available_quantity) if self.quantity is not None else "max"
        extraclass = " primary" if value != "0" else ""
        return mark_safe(f'<span class="status-tag{extraclass}">{value}</span>')

    def is_available_quantity(self, quantity: int = 1) -> bool:
        return self.available_quantity >= quantity

    def increase_stock(self, quantity: int, commit: bool = True):
        if self.quantity is not None:
            self.quantity = self.quantity + quantity
            if commit:
                self.save(update_fields=["quantity"])

    def decrease_stock(self, quantity: int, commit: bool = True):
        if self.quantity is not None:
            self.quantity = max(self.quantity - quantity, 0)
            if commit:
                self.save(update_fields=["quantity"])

    def reserve_stock(self, quantity: int, commit: bool = True):
        self.reserved_quantity = self.reserved_quantity + quantity
        if commit:
            self.save(update_fields=["reserved_quantity"])

    def unreserve_stock(self, quantity: int, commit: bool = True):
        self.reserved_quantity = max(self.reserved_quantity - quantity, 0)
        if commit:
            self.save(update_fields=["reserved_quantity"])

    def commit_reserved_stock(self, commit: bool = True) -> list[str]:
        self.decrease_stock(self.reserved_quantity, commit=False)
        self.reserved_quantity = 0
        update_fields = ["quantity", "reserved_quantity"]
        if commit:
            self.save(update_fields=update_fields)
        return update_fields

    @classmethod
    def commit_all_reserved_stock(cls) -> int:
        """
        Applies reserved stock to the quantity for all instances of class.
        """
        objs = []
        fields = []
        for obj in cls.objects.filter(reserved_quantity__gt=0):
            fields = obj.commit_reserved_stock(commit=False)
            objs.append(obj)
        count = len(objs)
        if count == 1:
            objs[0].save(update_fields=fields)
        elif count > 1:
            cls.objects.bulk_update(objs, fields)
        return count


class BasketStockReservation(models.Model):
    basket = models.OneToOneField(
        Basket,
        on_delete=models.SET_NULL,
        null=True,
        related_name="stock_reservation",
    )
    reserved_products = models.JSONField(null=True, editable=False)
    reserved_at = models.DateTimeField(null=True)

    RESERVED_SECONDS = 300  # 5 min

    @property
    def expires_at(self):
        return self.reserved_at + timezone.timedelta(seconds=self.RESERVED_SECONDS)

    def reserve_products(
        self,
        commit: bool = True,
        seen_products: Optional[dict[int, ProductVariant]] = None,
    ) -> list[ProductVariant]:
        """
        Reserve basket products.
        Pass in `seen_products` map to reuse products and skip fetching.
        """
        products: list[ProductVariant] = []
        seen_products = seen_products or {}

        if self.reserved_products is not None:
            # Products already reserved, exit
            return products

        self.reserved_at = timezone.now()
        self.reserved_products = []

        for item in self.basket.get_items():
            product = seen_products.get(item.product_id, item.product)
            product.reserve_stock(item.quantity, commit=False)
            products.append(product)
            self.reserved_products.append((product.id, item.quantity))

        if commit and len(products):
            self.save_reserved_quantity_for_products(products)
            self.save(update_fields=["reserved_at", "reserved_products"])

        return products

    def unreserve_products(
        self,
        commit: bool = True,
        seen_products: Optional[dict[int, ProductVariant]] = None,
    ) -> list[ProductVariant]:
        """
        Un-reserve basket products.
        Pass in `seen_products` map to reuse products and skip fetching.
        """
        from catalog.models.product import ProductVariant

        products: list[ProductVariant] = []

        if not self.reserved_products:
            # No reserved products, exit
            return products

        product_ids = [x[0] for x in self.reserved_products]
        product_quantities = dict(self.reserved_products)

        def _process_product(product):
            quantity = product_quantities.get(product.id, 0)
            if quantity > 0:
                product.unreserve_stock(quantity, commit=False)
                products.append(product)

        # Process given seen_products removing product id's from the list
        if seen_products is not None:
            for product_id in tuple(product_ids):
                product = seen_products.get(product_id, None)
                if product:
                    _process_product(product)
                    product_ids.remove(product_id)

        for product in ProductVariant.objects.filter(id__in=product_ids):
            _process_product(product)

        self.reserved_products = None

        if commit and len(products):
            self.save_reserved_quantity_for_products(products)
            self.save(update_fields=["reserved_products"])

        return products

    def commit(self):
        """
        Commit and delete the reservation.
        Has to be called explicitly to avoid un-reservation of expired reservations.
        """
        self.delete()

    def unreserve_and_delete(self):
        self.unreserve_products()
        self.delete()

    @classmethod
    def save_reserved_quantity_for_products(
        cls,
        products: list[ProductVariant],
        retry: int = 0,
    ):
        from catalog.models.product import ProductVariant

        # Save only products that have quantity set. If quantity is `None` on a product
        # we can skip saving/updateing the product since it's always available.
        products = [x for x in products if x.quantity is not None]
        if not products:
            return None

        try:
            ProductVariant.objects.bulk_update(products, ["reserved_quantity"])
        except OperationalError as exc:
            if retry >= 5:
                logger.error("Max deadlock retries (5), skipping transaction.")
                raise exc

            elif exc.args[0].startswith("deadlock detected"):
                # Deadlock, retry the transaction.
                logger.warning(f"Deadlock detected, retry. ({retry + 1})")
                cls.save_reserved_quantity_for_products(products, retry + 1)

    @classmethod
    def create_and_reserve(cls, basket: Basket) -> BasketStockReservation:
        obj = cls.objects.filter(basket_id=basket.id).first()
        if obj:
            products = obj.unreserve_products(commit=False)
            seen_products = {x.id: x for x in products}
            products = obj.reserve_products(commit=False, seen_products=seen_products)
            seen_products.update({x.id: x for x in products})
            cls.save_reserved_quantity_for_products(list(seen_products.values()))
            obj.save()
        else:
            obj = cls(basket=basket)
            products = obj.reserve_products(commit=False)
            cls.save_reserved_quantity_for_products(products)
            obj.save()
        return obj

    @classmethod
    def reserve_and_commit(cls, basket: Basket):
        obj = cls(basket=basket)
        products = obj.reserve_products(commit=False)
        cls.save_reserved_quantity_for_products(products)

    @classmethod
    @transaction.atomic
    def unreserve_and_delete_expired(cls) -> int:
        expires_at = timezone.now() - timezone.timedelta(seconds=cls.RESERVED_SECONDS)
        expired = cls.objects.filter(reserved_at__lt=expires_at)
        if expired.exists():
            seen_products: dict[int, ProductVariant] = {}
            for obj in expired:
                products = obj.unreserve_products(
                    commit=False, seen_products=seen_products
                )
                seen_products.update({x.id: x for x in products})
            cls.save_reserved_quantity_for_products(list(seen_products.values()))
            return expired.delete()[0]
        return 0
