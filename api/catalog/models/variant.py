from __future__ import annotations

from dataclasses import dataclass, field
from typing import TYPE_CHECKING, Optional

from django.contrib.admin.utils import quote, unquote
from django.db import models

if TYPE_CHECKING:
    from .attribute import ProductAttributeMixin
    from .product import Product, ProductVariant


@dataclass
class VariantAttributeValue:
    value: str
    label: str = ""

    def __post_init__(self):
        if not self.label:
            self.label = str(self.value).capitalize()

    @classmethod
    def from_instance(cls, instance: ProductAttributeMixin) -> VariantAttributeValue:
        return cls(instance.code, instance.name)


@dataclass
class VariantAttribute:
    code: str
    values: Optional[tuple[VariantAttributeValue, ...]] = None
    queryset: Optional[models.QuerySet[ProductAttributeMixin]] = None
    label: str = ""

    def __post_init__(self):
        if not self.values and self.queryset is None:
            raise ValueError("Must set either `values` or `queryset`")

        # Set values from queryset
        if not self.values and self.queryset is not None:
            self.values = tuple(
                VariantAttributeValue.from_instance(x) for x in self.queryset
            )

        if not self.label:
            self.label = self.code.capitalize()

    def __iter__(self):
        return iter(self.values)


@dataclass
class VariantCombination:
    product: Product
    values: tuple[VariantAttributeValue, ...]
    variant: Optional[ProductVariant] = field(init=False, default=None)
    label: str = ""

    def __post_init__(self):
        self.label = f"{self.product} ({self.values_label})"

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, VariantCombination):
            raise NotImplementedError
        return self.identifier == other.identifier

    @property
    def attrs(self) -> tuple[VariantAttribute, ...]:
        return tuple(
            self.product.get_variant_model().get_variant_attributes(
                self.product.locale_id
            )
        )

    @property
    def attrs_label(self) -> str:
        return " / ".join([x.label for x in self.attrs])

    @property
    def values_label(self) -> str:
        return " / ".join([x.label for x in self.values])

    @property
    def values_slug(self) -> str:
        return "-".join([str(x.value) for x in self.values])

    @property
    def identifier(self):
        values = ",".join([str(x.value) for x in self.values])
        return quote(f"{self.product.id}__{values}")

    def set_variant(self, variant):
        self.variant = variant
        self.variant.set_language(self.product.locale.language_code)

    def get_or_create_variant(self, **defaults):
        created = False
        if not self.variant:
            variant_model = self.product.get_variant_model()
            self.variant = variant_model.from_variant_combination(self, **defaults)
            self.variant.save()
            created = True
        return self.variant, created

    def to_dict(self):
        data = {
            "attrs_label": self.attrs_label,
            "values_label": self.values_label,
            "values_slug": self.values_slug,
            "identifier": self.identifier,
            "attrs": {},
        }
        for idx, value in enumerate(self.values):
            attr = self.attrs[idx]
            data["attrs"][attr.code] = {
                "code": attr.code,
                "label": attr.label,
                "value": str(value.value),
                "value_label": value.label,
            }
        return data

    @classmethod
    def from_identifier(
        cls,
        product: Product,
        identifier: str,
    ) -> VariantCombination:

        variant_model = product.get_variant_model()
        values = unquote(identifier).split("__")[1].split(",")
        values = tuple(VariantAttributeValue(x) for x in values)
        combo = VariantCombination(product, values)

        try:
            variant = product.get_variants().get(combo_id=identifier)
            combo.set_variant(variant)
        except variant_model.DoesNotExist:
            pass

        return combo
