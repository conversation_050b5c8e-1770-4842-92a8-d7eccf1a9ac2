from typing import Iterator

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.dispatch.dispatcher import receiver
from django.urls import reverse
from wagtail.admin.edit_handlers import (
    FieldPanel,
    InlinePanel,
    MultiFieldPanel,
    PageChooserPanel,
)
from wagtail.core.signals import page_published

from basic.models import FeaturedImageMixin
from core.models import CorePage, NotRoutablePageMixin
from core.serializers import ImageRend<PERSON>Field, force_dict
from extra.edit_handlers import ReadOnlyPanel
from home.models import HomePage

from .attribute import ProductColor, ProductNumericSize, ProductSize
from .product import InvalidateProductsMixin, Product, ProductVariant
from .variant import VariantAttribute, VariantAttributeValue


class CatalogPage(InvalidateProductsMixin, NotRoutablePageMixin, CorePage):
    parent_page_types = ["home.HomePage", "basic.BasicPage"]

    def get_menu_pages(self, request) -> list[CorePage]:
        return ProductCollection.objects.filter(
            locale_id=self.locale_id,
            show_in_menus=True,
        ).live()

    @classmethod
    def find_for_request(cls, request):
        return cls.objects.filter(locale__language_code=request.LANGUAGE_CODE).first()


class ProductCollection(InvalidateProductsMixin, FeaturedImageMixin, CorePage):
    caption = models.CharField("Caption", max_length=255, blank=True)
    is_featured = models.BooleanField(default=False, verbose_name="Featured Collection")
    is_homepage_collection = models.BooleanField(
        default=False,
        verbose_name="Homepage Collection",
        help_text="Display this collection on the homepage.",
    )

    class Template(models.TextChoices):
        COL4 = "4-columns", "4 columns"
        COL3 = "3-columns", "3 columns"
        COL2 = "2-columns", "2 columns"
        COL1 = "1-columns", "1 columns"

    template = models.CharField(
        "Template",
        max_length=32,
        choices=Template.choices,
        default=Template.COL4,
        help_text="Select a template for displaying this collection.",
    )

    class FeaturedImageSize(models.TextChoices):
        SMALL = "200x233", "small"
        MEDIUM = "380x505", "medium"
        LARGE = "700x930", "large"

    featured_image_data = models.JSONField(null=True, editable=False)
    alt_featured_image_data = models.JSONField(null=True, editable=False)

    panels = [
        FieldPanel("caption"),
        FieldPanel("template"),
        FieldPanel("is_featured"),
        FieldPanel("is_homepage_collection"),
    ]
    debug_panels = [
        ReadOnlyPanel("featured_image_data"),
        ReadOnlyPanel("alt_featured_image_data"),
    ]

    if settings.DEBUG:
        panels = panels + debug_panels

    content_panels = CorePage.content_panels + FeaturedImageMixin.panels + panels

    parent_page_types = ["catalog.CatalogPage"]
    page_serializer = "catalog.serializers.ProductCollectionSerializer"
    page_summary_serializer = "catalog.serializers.ProductCollectionSummarySerializer"

    class Meta:
        verbose_name = "Collection"

    def clean(self):
        super().clean()
        if self.is_featured:
            all_featured_collections = ProductCollection.objects.filter(
                locale_id=self.locale_id, is_featured=True
            )
            if self.pk:
                all_featured_collections = all_featured_collections.exclude(pk=self.pk)
            if all_featured_collections.count() >= 3:
                raise ValidationError(
                    "Only three collections can be featured at a time. "
                    "Please uncheck the 'Featured Collection' option on the other collection."
                )

    def get_featured_image_data(self):
        if self.featured_image_data is None:
            self.save_featured_image_data()
        return self.featured_image_data

    def _compute_alt_featured_image_data(self):
        """Compute alt featured image data without saving to database"""
        if self.alt_featured_image:
            data = {}
            for size, name in self.FeaturedImageSize.choices:
                field = ImageRenditionField(f"fill-{size}-c100")
                data[name] = force_dict(
                    field.to_representation(self.alt_featured_image)
                )
            return data
        return None

    def get_alt_featured_image_data(self):
        """Get alt featured image data, computing on-the-fly if not cached"""
        if self.alt_featured_image_data is None:
            return self._compute_alt_featured_image_data()
        return self.alt_featured_image_data

    def save_featured_image_data(self, commit: bool = True):
        if self.featured_image:
            data = {}
            for size, name in self.FeaturedImageSize.choices:
                field = ImageRenditionField(f"fill-{size}-c100")
                data[name] = force_dict(field.to_representation(self.featured_image))
            self.featured_image_data = data
        else:
            self.featured_image_data = None
        if commit:
            self.save(update_fields=["featured_image_data"])

    def get_cached_api_urls(self) -> list[str]:
        urls = super().get_cached_api_urls()
        try:
            home = HomePage.objects.get(locale_id=self.locale_id)
            pages_url = reverse("page-list")
            urls.append(f"{pages_url}{home.id}/")
            urls.append(f"{pages_url}find{home.url_path}")
        except HomePage.DoesNotExist:
            pass
        return urls

    def save(self, *args, **kwargs):
        # Populate featured image data during save
        self.save_featured_image_data(commit=False)
        # Populate alt_featured_image_data during save
        self.alt_featured_image_data = self._compute_alt_featured_image_data()
        super().save(*args, **kwargs)


@receiver(page_published, sender=ProductCollection)
def on_product_collection_published(sender, instance, **kwargs):
    instance.specific.save_featured_image_data()
    # alt_featured_image_data is now computed automatically in save() method


# ======================================================================================
# Single Product
# ======================================================================================


class SingleProduct(Product):
    parent_page_types = ["catalog.ProductCollection"]

    class Meta:
        verbose_name = "Single product"

    def get_variant_model(self) -> type[ProductVariant]:
        return SingleProductVariant


class SingleProductVariant(ProductVariant):
    variant = "default"

    class Meta(ProductVariant.Meta):
        verbose_name = "Single Variant"

    @classmethod
    def get_variant_attributes(cls, locale_id: int) -> Iterator[VariantAttribute]:
        yield VariantAttribute(
            code="variant",
            values=(VariantAttributeValue(cls.variant),),
        )


# ======================================================================================
# Color Product
# ======================================================================================


class ColorProduct(Product):
    parent_page_types = ["catalog.ProductCollection"]

    class Meta:
        verbose_name = "Product with variations (colors)"

    def get_variant_model(self) -> type[ProductVariant]:
        return ColorProductVariant

    def get_variants(self) -> models.QuerySet[ProductVariant]:
        return super().get_variants().select_related("color")


class ColorProductVariant(ProductVariant):
    color = models.ForeignKey(ProductColor, on_delete=models.PROTECT)

    class Meta(ProductVariant.Meta):
        verbose_name = "Variant (color)"

    @classmethod
    def get_variant_attributes(cls, locale_id: int) -> Iterator[VariantAttribute]:
        yield VariantAttribute(
            code="color",
            queryset=ProductColor.objects.filter(locale_id=locale_id),
        )


# ======================================================================================
# Size Product
# ======================================================================================


class SizeProduct(Product):
    parent_page_types = ["catalog.ProductCollection"]

    class Meta:
        verbose_name = "Product with variations (sizes)"

    def get_variant_model(self) -> type[ProductVariant]:
        return SizeProductVariant

    def get_variants(self) -> models.QuerySet[ProductVariant]:
        return super().get_variants().select_related("size")


class SizeProductVariant(ProductVariant):
    size = models.ForeignKey(ProductSize, on_delete=models.PROTECT)

    class Meta(ProductVariant.Meta):
        verbose_name = "Variant (size)"

    @classmethod
    def get_variant_attributes(cls, locale_id: int) -> Iterator[VariantAttribute]:
        yield VariantAttribute(code="size", queryset=ProductSize.objects.all())


# ======================================================================================
# Size Color Product
# ======================================================================================


class SizeColorProduct(Product):
    parent_page_types = ["catalog.ProductCollection"]

    class Meta:
        verbose_name = "Product with variations (sizes + colors)"

    def get_variant_model(self) -> type[ProductVariant]:
        return SizeColorProductVariant

    def get_variants(self) -> models.QuerySet[ProductVariant]:
        return super().get_variants().select_related("color", "size")


class SizeColorProductVariant(ProductVariant):
    color = models.ForeignKey(ProductColor, on_delete=models.PROTECT)
    size = models.ForeignKey(ProductSize, on_delete=models.PROTECT)

    class Meta(ProductVariant.Meta):
        verbose_name = "Variant (size + color)"

    @classmethod
    def get_variant_attributes(cls, locale_id: int) -> Iterator[VariantAttribute]:
        yield VariantAttribute(
            code="color",
            queryset=ProductColor.objects.filter(locale_id=locale_id),
        )
        yield VariantAttribute(code="size", queryset=ProductSize.objects.all())


# ======================================================================================
# Numeric Size Product
# ======================================================================================


class NumericSizeProduct(Product):
    parent_page_types = ["catalog.ProductCollection"]

    class Meta:
        verbose_name = "Product with variations (numeric sizes)"

    def get_variant_model(self) -> type[ProductVariant]:
        return NumericSizeProductVariant

    def get_variants(self) -> models.QuerySet[ProductVariant]:
        return super().get_variants().select_related("size")


class NumericSizeProductVariant(ProductVariant):
    size = models.ForeignKey(ProductNumericSize, on_delete=models.PROTECT)

    class Meta(ProductVariant.Meta):
        verbose_name = "Variant (size)"

    @classmethod
    def get_variant_attributes(cls, locale_id: int) -> Iterator[VariantAttribute]:
        yield VariantAttribute(code="size", queryset=ProductNumericSize.objects.all())


# ======================================================================================
# Numeric Size Color Product
# ======================================================================================


class NumericSizeColorProduct(Product):
    parent_page_types = ["catalog.ProductCollection"]

    class Meta:
        verbose_name = "Product with variations (numeric sizes + colors)"

    def get_variant_model(self) -> type[ProductVariant]:
        return NumericSizeColorProductVariant

    def get_variants(self) -> models.QuerySet[ProductVariant]:
        return super().get_variants().select_related("color", "size")


class NumericSizeColorProductVariant(ProductVariant):
    color = models.ForeignKey(ProductColor, on_delete=models.PROTECT)
    size = models.ForeignKey(ProductNumericSize, on_delete=models.PROTECT)

    class Meta(ProductVariant.Meta):
        verbose_name = "Product Variant (numeric size + color)"

    @classmethod
    def get_variant_attributes(cls, locale_id: int) -> Iterator[VariantAttribute]:
        yield VariantAttribute(
            code="color",
            queryset=ProductColor.objects.filter(locale_id=locale_id),
        )
        yield VariantAttribute(code="size", queryset=ProductNumericSize.objects.all())


# ======================================================================================
# Alias product
# ======================================================================================


class AliasProduct(NotRoutablePageMixin, Product):
    product_alias = models.ForeignKey(
        Product,
        on_delete=models.SET_NULL,
        null=True,
        related_name="product_aliases",
        verbose_name="Alias for product",
    )

    override_title = models.BooleanField(
        "Title",
        default=False,
        help_text="Override product title",
    )
    override_media = models.BooleanField(
        "Media",
        default=False,
        help_text="Override product media",
    )

    selected_variant_code = models.CharField(
        "Variant code",
        max_length=255,
        blank=True,
        help_text="Set which variant code should be preselected on navigation.",
    )

    panels = [
        PageChooserPanel("product_alias"),
        MultiFieldPanel(
            [
                FieldPanel("override_title"),
                FieldPanel("override_media"),
                FieldPanel("selected_variant_code"),
            ],
            heading="Alias configuration",
        ),
        InlinePanel("media", heading="Media"),
    ]

    content_panels = CorePage.content_panels + panels

    parent_page_types = ["catalog.ProductCollection"]

    class Meta:
        verbose_name = "Alias product"

    def save(self, *args, **kwargs):
        self.origin_country = "ES"  # placeholder value
        return super().save(*args, **kwargs)

    def get_variant_model(self) -> type[ProductVariant]:
        """
        Delegate to the aliased product's variant model.
        """
        if self.product_alias:
            return self.product_alias.get_variant_model()
        # Fallback to SingleProductVariant if no alias is set
        return SingleProductVariant

    def get_variants(self) -> models.QuerySet[ProductVariant]:
        """
        Delegate to the aliased product's variants.
        """
        if self.product_alias:
            return self.product_alias.get_variants()
        # Return empty queryset if no alias is set
        return self.get_variant_model().objects.none()

    def update_variants(self):
        pass
