from django.core.management.base import BaseCommand

from catalog.models import BasketStockReservation


class Command(BaseCommand):
    help = "Delete expired basket reservations"

    def handle(self, *args, **options):
        count = BasketStockReservation.unreserve_and_delete_expired()
        msg = "Deleted '%d' expired basket reservations" % count
        self.stdout.write(self.style.SUCCESS(msg))
