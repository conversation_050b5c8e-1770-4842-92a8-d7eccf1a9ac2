from wagtail.admin.edit_handlers import FieldPanel

from .widgets import (
    AttributeChooser,
    ProductColorChooser,
    ProductNumericSizeChooser,
    ProductSizeChooser,
)


class AttributeChooserPanel(FieldPanel):
    widget_class: type[AttributeChooser]

    def on_instance_bound(self):
        self.widget = self.widget_class(locale=self.instance.locale)


class ProductColorChooserPanel(AttributeChooserPanel):
    widget_class = ProductColorChooser


class ProductSizeChooserPanel(AttributeChooserPanel):
    widget_class = ProductSizeChooser


class ProductNumericSizeChooserPanel(AttributeChooserPanel):
    widget_class = ProductNumericSizeChooser
