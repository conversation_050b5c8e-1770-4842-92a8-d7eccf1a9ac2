import csv
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from io import StringIO

from django.contrib import messages
from django.db.models import <PERSON>, <PERSON>, Su<PERSON>, <PERSON>, <PERSON>, Avg
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.utils import translation
from wagtail.admin.menu import MenuItem
from wagtail.core import hooks


def reports_index(request):
    """Main reports page with form"""
    from datetime import datetime, timedelta

    # Default date range: last 30 days to now
    now = datetime.now()
    thirty_days_ago = now - timedelta(days=30)

    # Format for datetime-local input (YYYY-MM-DDTHH:MM)
    default_from_date = thirty_days_ago.strftime("%Y-%m-%dT%H:%M")
    default_to_date = now.strftime("%Y-%m-%dT%H:%M")

    return render(
        request,
        "catalog/report_index.html",
        {
            "title": "Catalog Reports",
            "default_from_date": default_from_date,
            "default_to_date": default_to_date,
        },
    )


def get_variants_for_product(product):
    """Helper function to safely get variants for any product type"""
    try:
        return product.get_variants()
    except NotImplementedError:
        return []


def generate_catalog_report(request):
    if request.method != "POST":
        return redirect("wagtailadmin_home")

    translation.activate(request.LANGUAGE_CODE)

    try:
        date_from = datetime.strptime(request.POST["datetime_from"], "%Y-%m-%dT%H:%M")
        date_to = (
            datetime.strptime(request.POST["datetime_to"], "%Y-%m-%dT%H:%M")
            if request.POST.get("datetime_to") and request.POST["datetime_to"].strip()
            else None
        )
    except ValueError as e:
        messages.error(
            request,
            f"Invalid date format. Expected YYYY-MM-DDTHH:MM format. Received: {request.POST.get('datetime_from', 'None')}",
        )
        return redirect("catalog_reports")

    # Get all products and variants
    from catalog.models import Product

    products = Product.objects.live().specific()
    # if date_to:
    #     products = products.filter(first_published_at__lte=date_to)

    # Generate CSV
    stream = StringIO()
    writer = csv.writer(stream)

    # Write headers
    headers = [
        "Product ID",
        "Product Name",
        "Product Type",
        "Base Price",
        "Old Price",
        "Status",
        "Creation Date",
        "Last Modified",
        "Units Sold",
        "Total Sales",
        "Variant ID",
        "Variant Name",
        "Variant SKU",
        "Variant EAN-13",
        "Variant Price",
        "Variant Old Price",
        "Available Stock",
        "Variant Units Sold",
        "Variant Total Sales",
    ]
    writer.writerow(headers)

    # Import stats utility
    from stats.utils import get_stats_for_product, get_stats_for_variant

    # Write data
    for product in products:
        try:
            product_stats = get_stats_for_product(product)
            variants = get_variants_for_product(product)

            if not variants:
                # Product without variants
                writer.writerow(
                    [
                        product.id,
                        product.title,
                        product.__class__.__name__,
                        product.base_price,
                        product.old_price or "",
                        "Published" if product.live else "Draft",
                        (
                            product.first_published_at.strftime("%Y-%m-%d %H:%M")
                            if product.first_published_at
                            else ""
                        ),
                        (
                            product.last_published_at.strftime("%Y-%m-%d %H:%M")
                            if product.last_published_at
                            else ""
                        ),
                        product_stats.sold_quantity,
                        product_stats.sold_total,
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",  # Empty variant fields
                    ]
                )
            else:
                for variant in variants:
                    try:
                        variant_stats = get_stats_for_variant(variant)
                        writer.writerow(
                            [
                                product.id,
                                product.title,
                                product.__class__.__name__,
                                product.base_price,
                                product.old_price or "",
                                "Published" if product.live else "Draft",
                                (
                                    product.first_published_at.strftime(
                                        "%Y-%m-%d %H:%M"
                                    )
                                    if product.first_published_at
                                    else ""
                                ),
                                (
                                    product.last_published_at.strftime("%Y-%m-%d %H:%M")
                                    if product.last_published_at
                                    else ""
                                ),
                                product_stats.sold_quantity,
                                product_stats.sold_total,
                                variant.id,
                                variant.name,
                                variant.code,
                                variant.ean13,
                                variant.price or product.base_price,
                                variant.old_price or product.old_price or "",
                                variant.available_quantity,
                                variant_stats.sold_quantity,
                                variant_stats.sold_total,
                            ]
                        )
                    except AttributeError as e:
                        print(
                            f"Warning: Skipping variant {variant.id} due to: {str(e)}"
                        )
                        continue
        except Exception as e:
            print(f"Warning: Skipping product {product.id} due to: {str(e)}")
            continue

    # Generate response
    response = HttpResponse(stream.getvalue(), content_type="text/csv")
    filename = f'catalog_report_{date_from.strftime("%Y-%m-%dT%H-%M")}'
    if date_to:
        filename += f'_{date_to.strftime("%Y-%m-%dT%H-%M")}'
    filename += ".csv"
    response["Content-Disposition"] = f'attachment; filename="{filename}"'

    return response


def generate_analytics_report(request):
    if not request.user.is_superuser:
        messages.error(request, "Permission denied")
        return redirect("catalog_reports")

    if request.method != "POST":
        return redirect("catalog_reports")

    try:
        date_from = datetime.strptime(request.POST["datetime_from"], "%Y-%m-%dT%H:%M")
        date_to = (
            datetime.strptime(request.POST["datetime_to"], "%Y-%m-%dT%H:%M")
            if request.POST.get("datetime_to") and request.POST["datetime_to"].strip()
            else None
        )
    except ValueError as e:
        messages.error(
            request,
            f"Invalid date format. Expected YYYY-MM-DDTHH:MM format. Received: {request.POST.get('datetime_from', 'None')}",
        )
        return redirect("catalog_reports")

    # Import necessary models
    from collections import defaultdict

    from django.db.models import Avg, Count, Max, Sum
    from django.db.models.functions import TruncHour, TruncMonth
    from salesman.orders.models import Order

    from catalog.models import Product
    from stats.utils import get_stats_for_product, get_stats_for_variant

    output = []

    # Get orders within date range
    total_orders = Order.objects.filter(
        date_created__gte=date_from,
        date_created__lte=date_to if date_to else datetime.now(),
    ).exclude(status="NEW")

    # 1. Overall Business Analytics
    total_revenue = sum(order.total for order in total_orders)
    order_count = total_orders.count()
    avg_order_value = total_revenue / order_count if order_count > 0 else 0

    output.append(["Business Overview", ""])
    output.append(["Total Orders", str(order_count)])
    output.append(["Total Revenue", f"€{total_revenue:.2f}"])
    output.append(["Average Order Value", f"€{avg_order_value:.2f}"])
    output.append(
        [
            "Period",
            f"{date_from.strftime('%Y-%m-%d')} to {date_to.strftime('%Y-%m-%d') if date_to else 'now'}",
        ]
    )
    output.append(["", ""])

    # 2. Best Selling Products
    output.append(["Top Performing Products", ""])
    output.append(
        ["Product ID", "Name (Units)", "Units Sold", "Revenue", "Profit Margin"]
    )

    top_products = []
    for product in Product.objects.live().specific():
        try:
            stats = get_stats_for_product(product)
            if stats.sold_quantity > 0:
                revenue = stats.sold_total
                margin = (
                    (revenue - (product.base_price * stats.sold_quantity))
                    / revenue
                    * 100
                    if revenue > 0
                    else 0
                )
                top_products.append(
                    {
                        "id": product.id,
                        "name": product.title,
                        "units": stats.sold_quantity,
                        "revenue": revenue,
                        "margin": margin,
                    }
                )
        except Exception as e:
            continue

    # Show top 10 by revenue with unit count in brackets
    for product in sorted(top_products, key=lambda x: x["revenue"], reverse=True)[:10]:
        output.append(
            [
                str(product["id"]),
                f"{product['name']} ({product['units']})",  # Added unit count in brackets
                str(product["units"]),
                f"€{product['revenue']:.2f}",
                f"{product['margin']:.1f}%",
            ]
        )
    output.append(["", ""])

    # 3. Customer Insights
    output.append(["Customer Analysis", ""])
    output.append(["Metric", "Value"])

    # Get customer metrics
    customer_orders = defaultdict(list)
    for order in total_orders:
        if order.email:  # Group by email as customer identifier
            customer_orders[order.email].append(order)

    # Calculate customer metrics
    total_customers = len(customer_orders)
    repeat_customers = sum(1 for orders in customer_orders.values() if len(orders) > 1)
    avg_orders_per_customer = (
        order_count / total_customers if total_customers > 0 else 0
    )

    # Find top customer
    top_customer = max(
        customer_orders.items(),
        key=lambda x: sum(o.total for o in x[1]),
        default=(None, []),
    )
    if top_customer[0]:
        top_customer_total = sum(o.total for o in top_customer[1])
        top_customer_orders = len(top_customer[1])
    else:
        top_customer_total = 0
        top_customer_orders = 0

    output.append(["Total Customers", str(total_customers)])
    output.append(
        [
            "Repeat Customers",
            (
                f"{repeat_customers} ({(repeat_customers/total_customers*100):.1f}% of total)"
                if total_customers > 0
                else "0"
            ),
        ]
    )
    output.append(["Avg Orders per Customer", f"{avg_orders_per_customer:.2f}"])
    if top_customer[0]:
        output.append(
            [
                "Top Customer",
                f"{top_customer[0]} (€{top_customer_total:.2f} in {top_customer_orders} orders)",
            ]
        )
    output.append(["", ""])

    # 4. Order Analysis
    output.append(["Order Analysis", ""])
    output.append(["Metric", "Value"])

    # Find largest and smallest orders
    largest_order = max(total_orders, key=lambda x: x.total, default=None)
    smallest_order = min(
        (o for o in total_orders if o.total > 0), key=lambda x: x.total, default=None
    )

    if largest_order:
        output.append(
            ["Largest Order", f"€{largest_order.total:.2f} (ID: {largest_order.ref})"]
        )
    if smallest_order:
        output.append(
            [
                "Smallest Order",
                f"€{smallest_order.total:.2f} (ID: {smallest_order.ref})",
            ]
        )
    output.append(["", ""])

    # 5. Time Analysis
    output.append(["Sales by Hour", ""])
    output.append(["Hour", "Orders", "Revenue"])

    # Group orders by hour
    hour_stats = defaultdict(lambda: {"orders": 0, "revenue": 0})
    for order in total_orders:
        hour = order.date_created.hour
        hour_stats[hour]["orders"] += 1
        hour_stats[hour]["revenue"] += order.total

    # Show hourly distribution
    for hour in sorted(hour_stats.keys()):
        stats = hour_stats[hour]
        output.append(
            [f"{hour:02d}:00", str(stats["orders"]), f"€{stats['revenue']:.2f}"]
        )
    output.append(["", ""])

    # 6. Inventory Insights
    output.append(["Inventory Status", ""])
    output.append(["Metric", "Value"])

    low_stock_count = 0
    out_of_stock_count = 0
    # total_stock_value = 0

    for product in Product.objects.live().specific():
        try:
            variants = get_variants_for_product(product)
            for variant in variants:
                stock = getattr(variant, "available_quantity", 0)
                if stock == 0:
                    out_of_stock_count += 1
                elif stock < 10:
                    low_stock_count += 1
                # total_stock_value += stock * (variant.price or product.base_price)
        except Exception as e:
            continue

    output.append(["Low Stock Items (<10)", str(low_stock_count)])
    output.append(["Out of Stock Items", str(out_of_stock_count)])
    # output.append(["Total Stock Value", f"€{total_stock_value:.2f}"])
    output.append(["", ""])

    # Generate CSV
    stream = StringIO()
    writer = csv.writer(stream)
    writer.writerows(output)

    # Generate response
    response = HttpResponse(stream.getvalue(), content_type="text/csv")
    filename = f'business_analytics_{date_from.strftime("%Y-%m-%dT%H-%M")}.csv'
    response["Content-Disposition"] = f'attachment; filename="{filename}"'

    return response


def export_customers_csv(request):
    """
    Export customers/users with order analytics to CSV.
    Handles both registered users and guest customers.
    """
    from salesman.orders.models import Order

    User = get_user_model()

    # Get parameters from request
    include_order_data = request.GET.get("include_orders", "true").lower() == "true"
    date_from_str = request.GET.get("date_from")
    date_to_str = request.GET.get("date_to")

    # Parse date filters with timezone awareness
    date_from = None
    date_to = None
    if date_from_str:
        try:
            from django.utils import timezone

            naive_date = datetime.strptime(date_from_str, "%Y-%m-%d")
            date_from = timezone.make_aware(naive_date)
        except ValueError:
            pass
    if date_to_str:
        try:
            from django.utils import timezone

            naive_date = datetime.strptime(date_to_str, "%Y-%m-%d")
            date_to = timezone.make_aware(naive_date)
        except ValueError:
            pass

    # Validate date range
    if date_from and date_to and date_from > date_to:
        date_from, date_to = date_to, date_from  # Swap if reversed

    # Generate CSV
    stream = StringIO()
    writer = csv.writer(stream)

    # Define headers based on whether order data is included
    base_headers = [
        "Customer Type",
        "Email",
        "First Name",
        "Last Name",
        "Full Name",
        "Registration Date",
        "Is Activated",
        "Address",
    ]

    order_headers = (
        [
            "Total Orders",
            "Total Spent",
            "Average Order Value",
            "First Order Date",
            "Last Order Date",
            "Last Order Amount",
            "Highest Order Amount",
            "Lowest Order Amount",
            "Days Since Last Order",
            "Customer Lifetime (Days)",
        ]
        if include_order_data
        else []
    )

    headers = base_headers + order_headers
    writer.writerow(headers)

    # Track processed emails to avoid duplicates
    processed_emails = set()

    # 1. Export registered users with their order data
    users = User.objects.all().order_by("date_joined")

    # Apply date filtering to users based on their registration date
    if date_from:
        users = users.filter(date_joined__gte=date_from)
    if date_to:
        # Add one day to include the entire end date
        from datetime import timedelta

        date_to_end = date_to + timedelta(days=1)
        users = users.filter(date_joined__lt=date_to_end)

    for user in users:
        if user.email in processed_emails:
            continue
        processed_emails.add(user.email)

        row_data = [
            "Registered User",
            user.email,
            user.first_name or "",
            user.last_name or "",
            user.get_full_name() or "",
            user.date_joined.strftime("%Y-%m-%d %H:%M") if user.date_joined else "",
            "Yes" if getattr(user, "is_activated", True) else "No",
            getattr(user, "address", "") or "",
        ]

        if include_order_data:
            # Get order analytics for this user
            user_orders = Order.objects.filter(user=user).exclude(status="NEW")
            if date_from:
                user_orders = user_orders.filter(date_created__gte=date_from)
            if date_to:
                user_orders = user_orders.filter(date_created__lte=date_to)

            order_analytics = calculate_order_analytics(user_orders, user.date_joined)
            row_data.extend(order_analytics)

        writer.writerow(row_data)

    # 2. Export guest customers (orders without user accounts)
    guest_orders = Order.objects.filter(user__isnull=True).exclude(status="NEW")
    if date_from:
        guest_orders = guest_orders.filter(date_created__gte=date_from)
    if date_to:
        guest_orders = guest_orders.filter(date_created__lte=date_to)

    # Group guest orders by email
    guest_emails = defaultdict(list)
    for order in guest_orders:
        if order.email and order.email not in processed_emails:
            guest_emails[order.email].append(order)

    for email, orders in guest_emails.items():
        processed_emails.add(email)

        # Try to extract name from first order's shipping address
        first_name, last_name = extract_name_from_address(orders[0])

        row_data = [
            "Guest Customer",
            email,
            first_name,
            last_name,
            f"{first_name} {last_name}".strip(),
            orders[0].date_created.strftime(
                "%Y-%m-%d %H:%M"
            ),  # First order as "registration"
            "N/A",  # Guest customers don't have activation status
            orders[0].shipping_address or "",
        ]

        if include_order_data:
            # Calculate analytics for guest orders
            order_analytics = calculate_order_analytics(orders, orders[0].date_created)
            row_data.extend(order_analytics)

        writer.writerow(row_data)

    # Generate response
    response = HttpResponse(stream.getvalue(), content_type="text/csv")

    # Create filename with current timestamp and filters
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M")
    filename_parts = ["customers_export", timestamp]

    if date_from or date_to:
        date_range = []
        if date_from:
            date_range.append(f"from_{date_from.strftime('%Y-%m-%d')}")
        if date_to:
            date_range.append(f"to_{date_to.strftime('%Y-%m-%d')}")
        filename_parts.append("_".join(date_range))

    if include_order_data:
        filename_parts.append("with_orders")
    else:
        filename_parts.append("basic")

    filename = "_".join(filename_parts) + ".csv"
    response["Content-Disposition"] = f'attachment; filename="{filename}"'

    return response


def calculate_order_analytics(orders, registration_date):
    """
    Calculate order analytics for a customer.

    Args:
        orders: QuerySet or list of Order objects
        registration_date: Date when customer first registered/ordered

    Returns:
        List of analytics values in the order expected by CSV headers
    """
    if not orders:
        return ["0", "€0.00", "€0.00", "", "", "", "", "", "", ""]

    # Convert to list if it's a QuerySet for easier manipulation
    if hasattr(orders, "aggregate"):
        # It's a QuerySet, get aggregated data efficiently
        order_stats = orders.aggregate(
            total_orders=Count("id"),
            total_spent=Sum("total"),
            avg_order_value=Avg("total"),
            max_order_value=Max("total"),
            min_order_value=Min("total"),
            first_order_date=Min("date_created"),
            last_order_date=Max("date_created"),
        )

        # Get last order amount separately
        last_order = orders.order_by("-date_created").first()
        last_order_amount = last_order.total if last_order else Decimal("0.00")

        total_orders = order_stats["total_orders"] or 0
        total_spent = order_stats["total_spent"] or Decimal("0.00")
        avg_order_value = order_stats["avg_order_value"] or Decimal("0.00")
        max_order_value = order_stats["max_order_value"] or Decimal("0.00")
        min_order_value = order_stats["min_order_value"] or Decimal("0.00")
        first_order_date = order_stats["first_order_date"]
        last_order_date = order_stats["last_order_date"]

    else:
        # It's a list, calculate manually
        total_orders = len(orders)
        total_spent = sum(order.total for order in orders)
        avg_order_value = (
            total_spent / total_orders if total_orders > 0 else Decimal("0.00")
        )

        order_amounts = [order.total for order in orders]
        max_order_value = max(order_amounts) if order_amounts else Decimal("0.00")
        min_order_value = min(order_amounts) if order_amounts else Decimal("0.00")

        order_dates = [order.date_created for order in orders]
        first_order_date = min(order_dates) if order_dates else None
        last_order_date = max(order_dates) if order_dates else None

        # Get last order amount
        last_order = max(orders, key=lambda x: x.date_created) if orders else None
        last_order_amount = last_order.total if last_order else Decimal("0.00")

    # Calculate days since last order
    days_since_last_order = ""
    if last_order_date:
        days_since = (datetime.now().date() - last_order_date.date()).days
        days_since_last_order = str(days_since)

    # Calculate customer lifetime in days
    customer_lifetime = ""
    if registration_date:
        if hasattr(registration_date, "date"):
            reg_date = registration_date.date()
        else:
            reg_date = registration_date
        lifetime_days = (datetime.now().date() - reg_date).days
        customer_lifetime = str(lifetime_days)

    return [
        str(total_orders),
        f"€{total_spent:.2f}",
        f"€{avg_order_value:.2f}",
        first_order_date.strftime("%Y-%m-%d %H:%M") if first_order_date else "",
        last_order_date.strftime("%Y-%m-%d %H:%M") if last_order_date else "",
        f"€{last_order_amount:.2f}",
        f"€{max_order_value:.2f}",
        f"€{min_order_value:.2f}",
        days_since_last_order,
        customer_lifetime,
    ]


def extract_name_from_address(order):
    """
    Try to extract first and last name from order's shipping address.

    Args:
        order: Order object

    Returns:
        Tuple of (first_name, last_name)
    """
    first_name = ""
    last_name = ""

    if not order.shipping_address:
        return first_name, last_name

    try:
        # Parse the address - assuming it's in JSON format or structured text
        import json
        import re

        # Try to parse as JSON first
        try:
            addr_data = json.loads(order.shipping_address)
            if isinstance(addr_data, dict):
                first_name = addr_data.get("first_name", "")
                last_name = addr_data.get("last_name", "")
                if first_name or last_name:
                    return first_name, last_name
        except (json.JSONDecodeError, TypeError):
            pass

        # If JSON parsing fails, try to extract from text
        # Look for patterns like "First Last" at the beginning of address
        lines = order.shipping_address.strip().split("\n")
        if lines:
            first_line = lines[0].strip()

            # Remove common prefixes
            prefixes = ["Mr.", "Mrs.", "Ms.", "Dr.", "Prof."]
            for prefix in prefixes:
                if first_line.startswith(prefix):
                    first_line = first_line[len(prefix) :].strip()

            # Split by spaces and take first two words as name
            name_parts = first_line.split()
            if len(name_parts) >= 2:
                # Check if it looks like a name (not an address)
                if not any(
                    char.isdigit() for char in first_line[:20]
                ):  # No numbers in first 20 chars
                    first_name = name_parts[0]
                    last_name = " ".join(name_parts[1:])

    except Exception:
        # If anything fails, return empty strings
        pass

    return first_name, last_name


def export_customers_view(request):
    """
    Admin view for exporting customers to CSV.
    Provides a form interface for the customer export functionality.
    """
    if request.method == "GET":
        # Show the form
        context = {
            "title": "Export Customers to CSV",
            "description": "Export customer data with optional order analytics for business insights.",
        }
        return render(request, "catalog/export_customers.html", context)

    elif request.method == "POST":
        # Process the form and generate CSV
        try:
            # Get form parameters
            include_orders = request.POST.get("include_orders") == "on"
            date_from = request.POST.get("date_from")
            date_to = request.POST.get("date_to")

            # Build query parameters for the export function
            params = {}
            if include_orders:
                params["include_orders"] = "true"
            else:
                params["include_orders"] = "false"

            if date_from:
                params["date_from"] = date_from
            if date_to:
                params["date_to"] = date_to

            # Create a new request object with GET parameters
            from django.http import QueryDict

            query_dict = QueryDict("", mutable=True)
            query_dict.update(params)

            # Create a mock request for the export function
            export_request = type(
                "MockRequest", (), {"GET": query_dict, "method": "GET"}
            )()

            # Call the export function
            response = export_customers_csv(export_request)

            # Add success message for next request
            messages.success(
                request,
                f"Successfully exported customer data. "
                f"{'Order analytics included.' if include_orders else 'Basic customer data only.'}",
            )

            return response

        except Exception as e:
            messages.error(request, f"Export failed: {str(e)}")
            return redirect("catalog_customers_export")

    # If neither GET nor POST, redirect to GET
    return redirect("catalog_customers_export")
