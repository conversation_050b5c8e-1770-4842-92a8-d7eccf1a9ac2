from django.forms import widgets

from .utils import get_country_choices


class CountrySelect(widgets.Select):
    def __init__(self, attrs=None, choices=()):
        choices = get_country_choices()
        super().__init__(attrs=attrs, choices=choices)


class CountrySelectMultiple(widgets.SelectMultiple):
    def __init__(self, attrs=None, choices=(), shipping=False):
        choices = get_country_choices(shipping=shipping)
        super().__init__(attrs=attrs, choices=choices)

    def format_value(self, value):
        if not isinstance(value, (list, tuple)):
            value = value.split(",")
        return super().format_value(value)
