from django import forms
from django.contrib.postgres.forms import SimpleArrayField

from .widgets import CountrySelect, CountrySelectMultiple


class CountryField(forms.CharField):
    def __init__(self, *args, **kwargs):
        kwargs["widget"] = CountrySelect()
        super().__init__(*args, **kwargs)


class CountriesField(SimpleArrayField):
    def __init__(self, *args, **kwargs):
        shipping = kwargs.pop("shipping", False)
        kwargs["widget"] = CountrySelectMultiple(
            shipping=shipping,
            attrs={"style": "min-height: 295px"},
        )
        super().__init__(*args, **kwargs)
