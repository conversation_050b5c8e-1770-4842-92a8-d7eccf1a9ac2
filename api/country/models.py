from django.contrib.postgres.fields import ArrayField
from django.db import models

from . import forms
from .utils import get_country_choices


class CountryField(models.CharField):
    def __init__(self, *args, shipping=False, **kwargs):
        self.shipping = shipping
        kwargs["choices"] = get_country_choices(shipping=shipping)
        kwargs["max_length"] = 5
        super().__init__(*args, **kwargs)

    def formfield(self, **kwargs):
        kwargs["form_class"] = forms.CountryField
        kwargs["shipping"] = self.shipping
        return super().formfield(**kwargs)


class CountriesField(ArrayField):
    def __init__(self, base_field=None, size=None, shipping=False, **kwargs):
        base_field = models.CharField(max_length=10)
        self.shipping = shipping
        kwargs["default"] = list
        kwargs["help_text"] = (
            'Hold down "Control" or "Command" on a Mac, to select more than one.'
        )
        super().__init__(base_field, **kwargs)

    def formfield(self, **kwargs):
        kwargs["form_class"] = forms.CountriesField
        kwargs["shipping"] = self.shipping
        return super().formfield(**kwargs)
