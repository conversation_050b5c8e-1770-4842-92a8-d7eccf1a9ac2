from .data import (
    CANADA_PROVINCES,
    COUNTRIES,
    ITALY_PROVINCES,
    SHIPPING_COUNTRIES,
    US_STATES,
)


def get_country_choices(shipping=False):
    """
    Returns countries formated as choices.
    """
    countries = SHIPPING_COUNTRIES if shipping else COUNTRIES
    return [(code, label) for code, label in countries.items()]


def get_country_label(code):
    """
    Returns label for given country code.
    """
    return COUNTRIES.get(code, SHIPPING_COUNTRIES.get(code, None))


def get_state_choices(country_code):
    """
    Returns state choices for given country code.
    """
    if country_code == "US":
        data = US_STATES
    elif country_code == "CA":
        data = CANADA_PROVINCES
    elif country_code == "IT":
        data = ITALY_PROVINCES
    else:
        return []
    return [(code, label) for code, label in data.items()]
