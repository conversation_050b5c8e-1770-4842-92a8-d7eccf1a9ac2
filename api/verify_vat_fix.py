#!/usr/bin/env python
"""
Quick verification that the VAT calculation fix is working
"""

import os
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings.dev')
django.setup()

from unittest.mock import Mock
from django.test import RequestFactory
from shop.modifiers import TaxModifier

def verify_vat_fix():
    """Verify the VAT calculation fix"""
    
    print("🧮 Verifying VAT Calculation Fix...")
    print("=" * 50)
    
    # Create test data
    factory = RequestFactory()
    
    # Create mock basket
    basket = Mock()
    basket.subtotal = Decimal("100.00")
    basket.total = Decimal("100.00")
    basket.extra = {}
    basket.extra_rows = {}
    basket.id = "test-basket"
    
    # Create mock request
    request = factory.post("/")
    request.user = Mock()
    setattr(request, 'data', {"extra": {}})
    
    # Apply tax modifier
    tax_modifier = TaxModifier()
    tax_modifier.process_basket(basket, request)
    
    # Check results
    if "tax" in basket.extra_rows:
        tax_row = basket.extra_rows["tax"]
        vat_amount = tax_row.instance["amount"]
        
        # Expected VAT calculation: €100 * (21/121) = €17.36
        expected_vat = Decimal("100.00") * Decimal("21") / Decimal("121")
        
        print(f"✅ Test Results:")
        print(f"   Total amount: €100.00")
        print(f"   Actual VAT: €{vat_amount:.2f}")
        print(f"   Expected VAT: €{expected_vat:.2f}")
        print(f"   Difference: €{abs(vat_amount - expected_vat):.4f}")
        
        # Check calculation method
        calc_method = tax_row.instance['extra'].get('calculation_method', 'UNKNOWN')
        print(f"   Calculation method: {calc_method}")
        
        # Verify the calculation is correct (within 1 cent)
        if abs(vat_amount - expected_vat) < Decimal("0.01"):
            print(f"\n🎉 SUCCESS! VAT calculation is CORRECT!")
            print(f"   ✅ Using VAT-included method")
            print(f"   ✅ VAT amount is accurate")
            
            # Show the improvement
            old_wrong_tax = Decimal("100.00") * Decimal("21") / Decimal("100")  # €21.00
            savings = old_wrong_tax - vat_amount
            print(f"\n💰 Customer Benefits:")
            print(f"   Old (wrong): €{old_wrong_tax} tax added on top")
            print(f"   New (correct): €{vat_amount:.2f} VAT included")
            print(f"   Customer saves: €{savings:.2f} per €100 order!")
            
            return True
        else:
            print(f"\n❌ FAILED! VAT calculation is still incorrect")
            print(f"   Expected: €{expected_vat:.2f}")
            print(f"   Got: €{vat_amount:.2f}")
            return False
    else:
        print("❌ FAILED! No tax row found")
        return False

def verify_vat_with_discount():
    """Verify VAT calculation with discount"""
    
    print("\n🧮 Verifying VAT with Discount...")
    print("=" * 50)
    
    # Create test data
    factory = RequestFactory()
    
    # Create mock basket with discount
    basket = Mock()
    basket.subtotal = Decimal("100.00")
    basket.total = Decimal("100.00")
    basket.extra = {}
    basket.extra_rows = {
        "discount_code": Mock()
    }
    basket.id = "test-basket-discount"
    
    # Mock discount row (20% off)
    basket.extra_rows["discount_code"].instance = {
        "amount": Decimal("-20.00"),
        "extra": {
            "discount_type": "PERCENT_SUBTOTAL"
        }
    }
    
    # Create mock request
    request = factory.post("/")
    request.user = Mock()
    setattr(request, 'data', {"extra": {}})
    
    # Apply tax modifier
    tax_modifier = TaxModifier()
    tax_modifier.process_basket(basket, request)
    
    # Check results
    if "tax" in basket.extra_rows:
        tax_row = basket.extra_rows["tax"]
        vat_amount = tax_row.instance["amount"]
        
        # Expected: €80 after discount, VAT = €80 * (21/121) = €13.88
        expected_total_after_discount = Decimal("80.00")
        expected_vat = expected_total_after_discount * Decimal("21") / Decimal("121")
        
        print(f"✅ Test Results:")
        print(f"   Original: €100.00")
        print(f"   Discount: -€20.00")
        print(f"   After discount: €80.00")
        print(f"   Actual VAT: €{vat_amount:.2f}")
        print(f"   Expected VAT: €{expected_vat:.2f}")
        print(f"   Difference: €{abs(vat_amount - expected_vat):.4f}")
        
        # Verify the calculation is correct
        if abs(vat_amount - expected_vat) < Decimal("0.01"):
            print(f"\n🎉 SUCCESS! VAT with discount is CORRECT!")
            print(f"   ✅ Discount applied before VAT calculation")
            print(f"   ✅ VAT calculated on discounted amount")
            
            # Show the improvement
            old_wrong_tax = Decimal("100.00") * Decimal("21") / Decimal("100")  # €21.00 on full amount
            savings = old_wrong_tax - vat_amount
            print(f"\n💰 Customer Benefits with Discount:")
            print(f"   Old (wrong): €{old_wrong_tax} VAT on full €100")
            print(f"   New (correct): €{vat_amount:.2f} VAT on discounted €80")
            print(f"   Customer saves: €{savings:.2f} in tax alone!")
            
            return True
        else:
            print(f"\n❌ FAILED! VAT with discount is incorrect")
            return False
    else:
        print("❌ FAILED! No tax row found")
        return False

if __name__ == "__main__":
    print("🚀 VAT Calculation Fix Verification")
    print("🔧 Testing the implemented changes...")
    print()
    
    try:
        # Test basic VAT calculation
        basic_ok = verify_vat_fix()
        
        # Test VAT with discount
        discount_ok = verify_vat_with_discount()
        
        print("\n" + "=" * 50)
        if basic_ok and discount_ok:
            print("🎉 ALL TESTS PASSED!")
            print("✅ VAT calculation fix is working correctly!")
            print("✅ Customers will save money on every order!")
            print("✅ Tax compliance achieved!")
        else:
            print("❌ SOME TESTS FAILED!")
            print("The VAT calculation fix needs more work.")
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
