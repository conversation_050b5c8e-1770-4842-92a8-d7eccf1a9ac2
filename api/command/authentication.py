from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from rest_framework.exceptions import AuthenticationFailed

from user.authentication import TokenAuthentication


class CommandTokenAuthentication(TokenAuthentication):
    def authenticate_credentials(self, key):
        token = getattr(settings, "COMMAND_TOKEN", None)
        if not token or key != token:
            raise AuthenticationFailed("Invalid command token.")
        user = AnonymousUser()
        user.username = token
        return (user, token)
