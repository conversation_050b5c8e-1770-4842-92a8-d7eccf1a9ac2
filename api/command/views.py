import io

from django.conf import settings
from django.core.management import call_command
from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.views import APIView

from .authentication import CommandTokenAuthentication
from .permissions import IsCommandUser


class CommandView(APIView):
    """
    Execute Django command via API.
    """

    authentication_classes = [CommandTokenAuthentication]
    permission_classes = [IsCommandUser]

    def post(self, request, command):
        if command not in getattr(settings, "COMMAND_ALLOWED_COMMADS", []):
            raise NotFound("Command not found.")

        options = request.query_params.dict()
        args = [x for x in options.pop("args", "").split(",") if x]
        out = io.StringIO()

        # Cast args and options to int if value prefixed with `:int`
        for key, value in options.items():
            if value.startswith("int:"):
                try:
                    options[key] = int(value[4:])
                except ValueError:
                    pass
        for index, arg in enumerate(args):
            if arg.startswith("int:"):
                try:
                    args[index] = int(arg[4:])
                except ValueError:
                    pass

        try:
            call_command(command, *args, **options, stdout=out)
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"detail": out.getvalue()})
