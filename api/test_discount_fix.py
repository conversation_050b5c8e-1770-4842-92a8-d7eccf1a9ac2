#!/usr/bin/env python
"""
Quick test script to verify the TokenUser discount fix is working.
Run this script to ensure the discount system handles TokenUser objects properly.
"""

import os
import sys
import django
import pytest
from decimal import Decimal

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project.settings")
django.setup()

from django.contrib.auth import get_user_model
from shop.models.discount import Discount, DiscountUsage
from shop.discount.validators import DiscountValidator
from user.models import TokenUser

User = get_user_model()


class MockTokenUser:
    """Mock TokenUser for testing"""

    def __init__(self, user_id, email="<EMAIL>"):
        self.id = user_id
        self.email = email
        self.is_authenticated = True
        self.token = {
            "user_id": user_id,
            "email": email,
            "first_name": "Test",
            "last_name": "User",
        }

    def to_user(self):
        """Convert to actual User instance"""
        try:
            return User.objects.get(id=self.id)
        except User.DoesNotExist:
            return None


@pytest.mark.django_db
def test_discount_fix():
    """Test that the discount TokenUser fix is working"""

    print("🧪 Testing Discount TokenUser Fix...")
    print("=" * 50)

    # Clean up any existing test data
    User.objects.filter(email="<EMAIL>").delete()
    Discount.objects.filter(code="TOKENTEST").delete()

    try:
        # 1. Create test user
        print("1️⃣ Creating test user...")
        user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        print(f"   ✅ Created user: {user.email} (ID: {user.id})")

        # 2. Create test discount
        print("\n2️⃣ Creating test discount...")
        discount = Discount.objects.create(
            code="TOKENTEST",
            description="Test discount for TokenUser fix",
            discount_type="percentage",
            value=Decimal("10.00"),
            max_uses=5,
            max_uses_per_user=2,
            min_basket_value=Decimal("50.00"),
            is_active=True,
        )
        print(f"   ✅ Created discount: {discount.code}")

        # 3. Create mock TokenUser
        print("\n3️⃣ Creating mock TokenUser...")
        token_user = MockTokenUser(user.id, user.email)
        print(f"   ✅ Created TokenUser for user ID: {token_user.id}")

        # 4. Test discount validation with TokenUser
        print("\n4️⃣ Testing discount validation with TokenUser...")
        is_valid, message = discount.is_valid(
            basket_value=Decimal("100.00"), user=token_user
        )
        print(f"   ✅ Validation result: {is_valid} - {message}")

        if not is_valid:
            print("   ❌ FAILED: Discount should be valid")
            return False

        # 5. Test DiscountValidator with TokenUser
        print("\n5️⃣ Testing DiscountValidator with TokenUser...")
        basket_id = "test_basket_123"

        # Check availability
        available, msg = DiscountValidator.check_discount_availability(
            discount.code, basket_id
        )
        print(f"   ✅ Availability check: {available} - {msg}")

        if not available:
            print("   ❌ FAILED: Discount should be available")
            return False

        # Confirm usage
        success = DiscountValidator.confirm_discount_usage(
            discount.code, basket_id, user=token_user
        )
        print(f"   ✅ Usage confirmation: {success}")

        if not success:
            print("   ❌ FAILED: Usage confirmation should succeed")
            return False

        # 6. Check that usage was recorded properly
        print("\n6️⃣ Checking usage records...")
        usage_count = DiscountUsage.objects.filter(
            discount=discount, user=user  # Should be the actual User, not TokenUser
        ).count()
        print(f"   ✅ Usage records created: {usage_count}")

        if usage_count != 1:
            print("   ❌ FAILED: Should have exactly 1 usage record")
            return False

        # 7. Test per-user limit with TokenUser
        print("\n7️⃣ Testing per-user limits...")

        # Create another usage record to reach the limit
        DiscountUsage.objects.create(
            discount=discount, user=user, order_id="test_order_2", amount="10.00"
        )

        # Now test should fail due to per-user limit
        is_valid, message = discount.is_valid(
            basket_value=Decimal("100.00"), user=token_user
        )
        print(f"   ✅ Per-user limit test: {is_valid} - {message}")

        if is_valid:
            print("   ❌ FAILED: Should fail due to per-user limit")
            return False

        # 8. Test with TokenUser that fails conversion
        print("\n8️⃣ Testing TokenUser conversion failure...")

        class FailingTokenUser:
            def __init__(self):
                self.id = 999999  # Non-existent user
                self.is_authenticated = True

            def to_user(self):
                return None  # Simulate conversion failure

        failing_token_user = FailingTokenUser()

        # Create a new discount for this test
        discount2 = Discount.objects.create(
            code="TOKENTEST2",
            description="Test discount 2",
            discount_type="percentage",
            value=Decimal("15.00"),
            max_uses_per_user=1,
            is_active=True,
        )

        is_valid, message = discount2.is_valid(
            basket_value=Decimal("100.00"), user=failing_token_user
        )
        print(f"   ✅ Conversion failure test: {is_valid} - {message}")

        if not is_valid:
            print("   ❌ FAILED: Should gracefully handle conversion failure")
            return False

        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The TokenUser discount fix is working correctly!")

    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback

        traceback.print_exc()
        pytest.fail(f"Test failed: {e}")

    finally:
        # Clean up test data
        print("\n🧹 Cleaning up test data...")
        User.objects.filter(email="<EMAIL>").delete()
        Discount.objects.filter(code__startswith="TOKENTEST").delete()
        print("   ✅ Cleanup complete")


@pytest.mark.django_db
def test_original_error_scenario():
    """Test the exact scenario that caused the original error"""

    print("\n🔍 Testing Original Error Scenario...")
    print("=" * 50)

    try:
        # Create the exact conditions that caused the original error
        user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        discount = Discount.objects.create(
            code="ORIGINALERROR",
            description="Test original error",
            discount_type="percentage",
            value=Decimal("10.00"),
            max_uses_per_user=1,
            is_active=True,
        )

        # Create a TokenUser-like object that would cause the original error
        class ProblematicTokenUser:
            def __init__(self, user_obj):
                self.id = (
                    user_obj  # This was the problem - passing User object instead of ID
                )
                self.is_authenticated = True

            def to_user(self):
                return user

        problematic_token_user = ProblematicTokenUser(user)  # Pass User object as ID

        # This should NOT raise the original error anymore
        print("   Testing problematic TokenUser scenario...")
        is_valid, message = discount.is_valid(user=problematic_token_user)
        print(f"   ✅ Result: {is_valid} - {message}")

        print("   ✅ Original error scenario handled successfully!")

    except Exception as e:
        print(f"   ❌ Original error scenario still fails: {e}")
        pytest.fail(f"Original error scenario test failed: {e}")

    finally:
        # Clean up
        User.objects.filter(email="<EMAIL>").delete()
        Discount.objects.filter(code="ORIGINALERROR").delete()


if __name__ == "__main__":
    print("🚀 Starting Discount TokenUser Fix Tests")
    print("=" * 60)

    success1 = test_discount_fix()
    success2 = test_original_error_scenario()

    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED! The fix is working correctly.")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
        sys.exit(1)
