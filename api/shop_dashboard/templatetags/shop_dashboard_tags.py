from decimal import Decimal

from django.db.models import Sum
from django.template import Library
from django.utils import timezone
from salesman.orders.models import Order
from wagtail.core.models import Locale

from catalog.models import Product
from shop.formatters import price_format
from stats.models import ProductStats

register = Library()


@register.simple_tag(takes_context=True)
def get_site_summary(context):
    today = timezone.now()
    Status = Order.get_statuses()
    paid_statuses = [
        Status.PROCESSING,
        Status.SHIPPED,
        Status.COMPLETED,
    ]
    orders_today = Order.objects.filter(
        date_created__year=today.year,
        date_created__month=today.month,
        date_created__day=today.day,
        status__in=paid_statuses,
    )

    total_amount = orders_today.aggregate(total_amount=Sum("total"))["total_amount"]
    total_amount = price_format(Decimal(total_amount or 0), context)

    return {
        "total_products": Product.objects.live().count(),
        "total_orders_today": orders_today.count(),
        "sales_amount_today": total_amount,
    }


@register.simple_tag(takes_context=True)
def get_top_products(context, count=5) -> list[Product]:
    locale = Locale.get_active()
    top_stats = list(
        ProductStats.objects.all()
        .select_related("variant")
        .order_by("-sold_quantity")[:count]
    )
    top_products = []

    for stat in top_stats:
        product = stat.variant.get_products(locale_id=locale.id).first()
        if product:
            product.stats = stat
            top_products.append(product)

    return top_products
