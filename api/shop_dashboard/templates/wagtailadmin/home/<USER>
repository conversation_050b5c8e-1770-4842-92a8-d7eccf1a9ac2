{% load i18n wagtailadmin_tags shop_dashboard_tags %}
<section class="panel summary nice-padding">
  <h2 class="visuallyhidden">Site summary</h2>
  <ul class="stats">
    {% get_site_summary as site_summary %}
    {% url 'salesman_order_modeladmin_index' as orders_url %}

    <li class="icon icon-tag">
      <a href="{% url 'catalog_product_modeladmin_index' %}">
        {% with total=site_summary.total_products|intcomma %}
          <span>{{ total }}</span> {% if site_summary.total_products == 1 %}Product{% else %}Products{% endif %}
        {% endwith %}
      </a>
    </li>

    <li class="icon icon-form">
      <a href="{{ orders_url }}">
        {% with total=site_summary.total_orders_today|intcomma %}
          <span>{{ total }}</span> {% if site_summary.total_orders_today == 1 %}Order{% else %}Orders{% endif %} today
        {% endwith %}
      </a>
    </li>

    <li class="icon icon-tick">
      <a href="{{ orders_url }}">
        <span>{{ site_summary.sales_amount_today }}</span> Sales today
      </a>
    </li>
  </ul>
</section>
