{% load wagtailadmin_tags shop_dashboard_tags %}

{% get_top_products as top_products %}
{% if top_products %}
  <section class="object collapsible">
    <h2 class="title-wrapper">Top selling products</h2>
    <div class="object-layout">
      <table class="listing listing-page">
        <col />
        <col width="30%"/>
        <col width="15%"/>
        <thead>{# Note: the header is visually hidden behind .title-wrapper #}
          <tr>
            <th class="title">Title</th>
            <th>Status</th>
            <th>Date</th>
          </tr>
        </thead>
        <tbody>
          {% for product in top_products %}
            <tr>
              <td class="title" valign="top">
                <div class="title-wrapper">
                  <a href="{% url 'wagtailadmin_pages:edit' product.id %}" title="Edit this product">{{ product.get_admin_display_title }}</a>

                  {% include "wagtailadmin/pages/listing/_privacy_indicator.html" with page=product %}
                  {% include "wagtailadmin/pages/listing/_locked_indicator.html" with page=product %}
                </div>
                <ul class="actions">
                  <li><a href="{% url 'wagtailadmin_pages:edit' product.id %}" class="button button-small button-secondary">Edit</a></li>
                  {% if product.has_unpublished_changes and product.is_previewable %}
                    <li><a href="{% url 'wagtailadmin_pages:view_draft' product.id %}" class="button button-small button-secondary" target="_blank" rel="noopener noreferrer">Draft</a></li>
                  {% endif %}
                  {% if product.live %}
                    {% with page_url=product.url %}
                      {% if page_url is not None %}
                        <li><a href="{{ page_url }}" class="button button-small button-secondary" target="_blank" rel="noopener noreferrer">Live</a></li>
                      {% endif %}
                    {% endwith %}
                  {% endif %}
                </ul>
              </td>
              <td valign="top">
                {% include "wagtailadmin/shared/page_status_tag.html" with page=product %}
              </td>
              <td valign="top">{{ product.stats.sold_quantity }} sold</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </section>
{% endif %}

{% if last_edits %}
<section class="object collapsible">
  <h2 class="title-wrapper">Your most recent edits</h2>
  <div class="object-layout">
    <table class="listing listing-page">
      <col />
      <col width="30%"/>
      <col width="15%"/>
      <thead>{# Note: the header is visually hidden behind .title-wrapper #}
        <tr>
          <th class="title">Title</th>
          <th>Status</th>
          <th>Date</th>
        </tr>
      </thead>
      <tbody>
        {% for revision, page in last_edits %}
          <tr>
            <td class="title" valign="top">
              <div class="title-wrapper">
                <a href="{% url 'wagtailadmin_pages:edit' page.id %}" title="Edit this page">{{ page.get_admin_display_title }}</a>

                {% include "wagtailadmin/pages/listing/_privacy_indicator.html" with page=page %}
                {% include "wagtailadmin/pages/listing/_locked_indicator.html" with page=page %}
              </div>
              <ul class="actions">
                <li><a href="{% url 'wagtailadmin_pages:edit' page.id %}" class="button button-small button-secondary">Edit</a></li>
                {% if page.has_unpublished_changes and page.is_previewable %}
                  <li><a href="{% url 'wagtailadmin_pages:view_draft' page.id %}" class="button button-small button-secondary" target="_blank" rel="noopener noreferrer">Draft</a></li>
                {% endif %}
                {% if page.live %}
                  {% with page_url=page.url %}
                    {% if page_url is not None %}
                      <li><a href="{{ page_url }}" class="button button-small button-secondary" target="_blank" rel="noopener noreferrer">Live</a></li>
                    {% endif %}
                  {% endwith %}
                {% endif %}
              </ul>
            </td>
            <td valign="top">
              {% include "wagtailadmin/shared/page_status_tag.html" with page=page %}
            </td>
            <td valign="top">
              <div class="human-readable-date" title="{{ revision.created_at|date:"DATETIME_FORMAT" }}">
                {% with time_period=revision.created_at|timesince_simple %}{{ time_period }}{% endwith %}
              </div>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</section>
{% endif %}
