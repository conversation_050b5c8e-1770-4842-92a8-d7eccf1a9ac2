name: API Test

on:
  push:
    branches:
      - master
      - dev
    paths:
      - "**.py"
  pull_request:
    paths:
      - "**.py"

jobs:
  api-test:
    name: Test
    runs-on: ubuntu-latest
    container: python:3.9-slim

    services:
      db:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: pass
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v2

      - name: Install server dependencies
        run: apt-get update && apt-get install -y libpq-dev gcc

      - uses: Gr1N/setup-poetry@v8
        with:
          poetry-version: "1.4.0"

      - name: Install python dependencies
        working-directory: ./api
        run: |
          poetry config virtualenvs.create false
          poetry install -E tests
      - name: Check code style
        working-directory: ./api
        continue-on-error: true
        run: |
          isort . --check
          black --check .
          flake8 .
          mypy .
      - name: Run tests
        working-directory: ./api
        env:
          BASE_URL: ""
          SECRET_KEY: secret
          DB_NAME: postgres
          DB_USER: postgres
          DB_PASS: pass
          DB_HOST: db
        run: poetry run pytest
