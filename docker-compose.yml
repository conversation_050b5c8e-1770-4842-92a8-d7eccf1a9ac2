services:
  db:
    image: postgres:14.1
    restart: always
    environment:
      POSTGRES_PASSWORD: dbpass
      POSTGRES_DB: leeosmerch
    volumes:
      - dbdata:/var/lib/postgresql/data

  redis:
    image: redis:6.2

  api:
    build: ./api
    image: leeosmerch-api
    env_file: api/.env
    environment:
      DJANGO_SETTINGS_MODULE: project.settings.dev
      BASE_URL: http://localhost:8080
      DB_USER: postgres
      DB_PASS: dbpass
      DB_NAME: leeosmerch
      DB_HOST: db
      CACHE_URL: redis://redis:6379?key_prefix=leeosmerch&timeout=3600
    command: bash -c '
      python manage.py migrate &&
      python manage.py createcachetable &&
      python manage.py update_index &&
      poetry run gunicorn --reload -c python:gunicorn_config project.wsgi:application'
    volumes:
      - apidata:/usr/src/app/public
      - ./api:/usr/src/app
    ports:
      - "8080:8080"
    depends_on:
      - db

  client:
    build: ./client
    image: leeosmerch-client
    env_file: client/.env
    environment:
      BASE_URL: http://localhost:3000
      API_URL: https://www.madkat.store
    volumes:
      - ./client:/usr/src/app
    ports:
      - "3000:3000"
    depends_on:
      - api

volumes:
  dbdata:
  apidata:
