apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgbouncer-replica1
  namespace: default
  labels:
    app: pgbouncer-replica1
spec:
  replicas: 1
  revisionHistoryLimit: 10  # removes old replicasets for deployment rollbacks
  strategy:
    rollingUpdate:
      maxUnavailable: 0  # Avoid Terminating and ContainerCreating at the same time
  selector:
    matchLabels:
      app: pgbouncer-replica1
  template:
    metadata:
      labels:
        app: pgbouncer-replica1
    spec:
      containers:
      - name: pgbouncer-replica1
        image: edoburu/pgbouncer:1.15.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5432
        envFrom:
        - secretRef:
            name: pgbouncer-replica1-secret
        env:
        - name: POOL_MODE
          value: transaction
        - name: SERVER_RESET_QUERY
          value: DISCARD ALL
        - name: MAX_CLIENT_CONN
          value: "100000"
        - name: DEFAULT_POOL_SIZE
          value: "3000"
        - name: MAX_DB_CONNECTIONS
          value: "3000"
        - name: MAX_USER_CONNECTIONS
          value: "3000"
        livenessProbe:
          tcpSocket:
            port: 5432
          periodSeconds: 60
        lifecycle:
          preStop:
            exec:
              # Allow existing queries clients to complete within 120 seconds
              command: ["/bin/sh", "-c", "killall -INT pgbouncer && sleep 120"]
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: ['all']
---
apiVersion: v1
kind: Service
metadata:
  name: pgbouncer-replica1
  namespace: default
  annotations:
    networking.gke.io/load-balancer-type: "Internal"
  labels:
    app: pgbouncer-replica1
spec:
  type: LoadBalancer
  ports:
    - port: 5432
      targetPort: 5432
      protocol: TCP
      name: postgres
  selector:
    app: pgbouncer-replica1
