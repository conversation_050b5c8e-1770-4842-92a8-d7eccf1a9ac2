# PgBouncer

PgBouncer implentation for Kubernetes cluster

### Deploy onto a Kubernetes cluster

1. Make sure you're connect to the right kubernetes cluster
2. Go into `k8s` directory
3. Copy `pgbouncer.secret.example` file to `pgbouncer.secret` and `pgbouncer-replica1.secret`
4. Edit `secrets.env` and replace with "real" values
5. Run `kubectl create secret generic pgbouncer-secret --from-env-file=pgbouncer.secret`
5. Run `kubectl create secret generic pgbouncer-replica1-secret --from-env-file=pgbouncer-replica1.secret`
6. Run `kubectl apply -f .`
