#!/usr/bin/env python3
"""
Specialized test runner for discount and race condition testing.
This script provides different test modes for comprehensive discount testing.
"""

import argparse
import subprocess
import sys
import time
from typing import List, Dict

from discount_test_config import LOAD_TEST_CONFIG, SETUP_INSTRUCTIONS


class DiscountTestRunner:
    """Manages different types of discount load tests"""
    
    def __init__(self, host: str, port: int = 8089):
        self.host = host
        self.port = port
        self.base_cmd = [
            "locust",
            "-f", "tasks.py",
            "--host", host,
            "--web-port", str(port),
            "--html", "discount_test_report.html"
        ]
    
    def run_race_condition_test(self, headless: bool = False):
        """Run intensive race condition testing"""
        config = LOAD_TEST_CONFIG["race_condition_intensity"]
        
        print("🏁 Starting Race Condition Test")
        print(f"   Users: {config['users']}")
        print(f"   Spawn Rate: {config['spawn_rate']}/sec")
        print(f"   Duration: {config['duration']}")
        print(f"   Target Discount: {config['target_discount']}")
        print()
        
        cmd = self.base_cmd.copy()
        cmd.extend([
            "--users", str(config["users"]),
            "--spawn-rate", str(config["spawn_rate"]),
            "--run-time", config["duration"],
            "--only-summary"
        ])
        
        if headless:
            cmd.append("--headless")
        
        # Focus on RaceConditionTester class
        cmd.extend(["--tags", "race_condition"])
        
        return self._run_test(cmd, "Race Condition Test")
    
    def run_shipping_discount_test(self, headless: bool = False):
        """Run automatic shipping discount validation"""
        config = LOAD_TEST_CONFIG["shipping_discount_validation"]
        
        print("🚢 Starting Shipping Discount Test")
        print(f"   Users: {config['users']}")
        print(f"   Spawn Rate: {config['spawn_rate']}/sec")
        print(f"   Duration: {config['duration']}")
        print(f"   Scenarios: {len(config['scenarios'])}")
        print()
        
        cmd = self.base_cmd.copy()
        cmd.extend([
            "--users", str(config["users"]),
            "--spawn-rate", str(config["spawn_rate"]),
            "--run-time", config["duration"],
            "--only-summary"
        ])
        
        if headless:
            cmd.append("--headless")
        
        return self._run_test(cmd, "Shipping Discount Test")
    
    def run_mixed_scenario_test(self, headless: bool = False):
        """Run mixed automatic + manual discount testing"""
        config = LOAD_TEST_CONFIG["mixed_scenario_testing"]
        
        print("🔀 Starting Mixed Scenario Test")
        print(f"   Users: {config['users']}")
        print(f"   Spawn Rate: {config['spawn_rate']}/sec")
        print(f"   Duration: {config['duration']}")
        print(f"   Automatic Threshold: €{config['automatic_threshold']}")
        print()
        
        cmd = self.base_cmd.copy()
        cmd.extend([
            "--users", str(config["users"]),
            "--spawn-rate", str(config["spawn_rate"]),
            "--run-time", config["duration"],
            "--only-summary"
        ])
        
        if headless:
            cmd.append("--headless")
        
        return self._run_test(cmd, "Mixed Scenario Test")
    
    def run_stress_test(self, headless: bool = False):
        """Run high-load stress testing"""
        config = LOAD_TEST_CONFIG["stress_testing"]
        
        print("💪 Starting Stress Test")
        print(f"   Users: {config['users']}")
        print(f"   Spawn Rate: {config['spawn_rate']}/sec")
        print(f"   Duration: {config['duration']}")
        print(f"   Description: {config['description']}")
        print()
        
        cmd = self.base_cmd.copy()
        cmd.extend([
            "--users", str(config["users"]),
            "--spawn-rate", str(config["spawn_rate"]),
            "--run-time", config["duration"],
            "--only-summary"
        ])
        
        if headless:
            cmd.append("--headless")
        
        return self._run_test(cmd, "Stress Test")
    
    def run_full_test_suite(self, headless: bool = True):
        """Run all discount tests in sequence"""
        print("🚀 Starting Full Discount Test Suite")
        print("=" * 60)
        
        tests = [
            ("Shipping Discount Validation", self.run_shipping_discount_test),
            ("Race Condition Testing", self.run_race_condition_test),
            ("Mixed Scenario Testing", self.run_mixed_scenario_test),
            ("Stress Testing", self.run_stress_test)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            print(f"\n📋 Running {test_name}...")
            start_time = time.time()
            
            try:
                result = test_func(headless=headless)
                duration = time.time() - start_time
                results[test_name] = {
                    "success": result,
                    "duration": duration
                }
                
                if result:
                    print(f"✅ {test_name} completed successfully in {duration:.1f}s")
                else:
                    print(f"❌ {test_name} failed after {duration:.1f}s")
                    
            except Exception as e:
                duration = time.time() - start_time
                results[test_name] = {
                    "success": False,
                    "duration": duration,
                    "error": str(e)
                }
                print(f"💥 {test_name} crashed after {duration:.1f}s: {e}")
            
            # Wait between tests
            if test_name != tests[-1][0]:  # Not the last test
                print("⏳ Waiting 30 seconds before next test...")
                time.sleep(30)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 FULL TEST SUITE RESULTS")
        print("=" * 60)
        
        total_duration = sum(r["duration"] for r in results.values())
        successful_tests = sum(1 for r in results.values() if r["success"])
        
        for test_name, result in results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            duration = result["duration"]
            print(f"{status} {test_name:<30} ({duration:.1f}s)")
            
            if "error" in result:
                print(f"     Error: {result['error']}")
        
        print("-" * 60)
        print(f"Total Tests: {len(tests)}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {len(tests) - successful_tests}")
        print(f"Total Duration: {total_duration:.1f}s")
        print("=" * 60)
        
        return successful_tests == len(tests)
    
    def _run_test(self, cmd: List[str], test_name: str) -> bool:
        """Execute a locust test command"""
        try:
            print(f"🔧 Command: {' '.join(cmd)}")
            print()
            
            process = subprocess.run(
                cmd,
                capture_output=False,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            success = process.returncode == 0
            
            if success:
                print(f"✅ {test_name} completed successfully")
            else:
                print(f"❌ {test_name} failed with return code {process.returncode}")
            
            return success
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_name} timed out after 1 hour")
            return False
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(
        description="Run discount and race condition load tests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=SETUP_INSTRUCTIONS
    )
    
    parser.add_argument(
        "host",
        help="Target host URL (e.g., https://your-site.com)"
    )
    
    parser.add_argument(
        "--test-type",
        choices=["race", "shipping", "mixed", "stress", "full"],
        default="full",
        help="Type of test to run (default: full)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8089,
        help="Locust web interface port (default: 8089)"
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        help="Run tests in headless mode (no web UI)"
    )
    
    parser.add_argument(
        "--setup-only",
        action="store_true",
        help="Only show setup instructions"
    )
    
    args = parser.parse_args()
    
    if args.setup_only:
        print(SETUP_INSTRUCTIONS)
        return 0
    
    runner = DiscountTestRunner(args.host, args.port)
    
    test_functions = {
        "race": runner.run_race_condition_test,
        "shipping": runner.run_shipping_discount_test,
        "mixed": runner.run_mixed_scenario_test,
        "stress": runner.run_stress_test,
        "full": runner.run_full_test_suite
    }
    
    test_func = test_functions[args.test_type]
    
    print(f"🎯 Starting {args.test_type} test against {args.host}")
    print(f"🌐 Web UI available at: http://localhost:{args.port}")
    print()
    
    try:
        success = test_func(headless=args.headless)
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        return 130
    except Exception as e:
        print(f"\n💥 Test runner crashed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
