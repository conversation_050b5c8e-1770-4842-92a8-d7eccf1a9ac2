"""
Configuration for discount and race condition load testing.
This file contains test scenarios and parameters for comprehensive discount testing.
"""

# Test discount codes that should be created in the system before running tests
TEST_DISCOUNT_CODES = {
    # Limited discount codes for race condition testing
    "FLASH50": {
        "description": "Flash Sale 50% Off",
        "discount_type": "PERCENT_TOTAL",
        "value": 50.0,
        "max_uses": 100,
        "is_active": True,
        "purpose": "Race condition testing with moderate limit"
    },
    "SAVE20": {
        "description": "Save 20% Off",
        "discount_type": "PERCENT_TOTAL", 
        "value": 20.0,
        "max_uses": 50,
        "is_active": True,
        "purpose": "Race condition testing with lower limit"
    },
    "RACE50": {
        "description": "Race Condition Test Discount",
        "discount_type": "PERCENT_TOTAL",
        "value": 50.0,
        "max_uses": 10,  # Very limited for intensive race testing
        "is_active": True,
        "purpose": "Intensive race condition testing"
    },
    "LIMITED25": {
        "description": "Very Limited 25% Off",
        "discount_type": "PERCENT_TOTAL",
        "value": 25.0,
        "max_uses": 5,  # Extremely limited
        "is_active": True,
        "purpose": "Extreme race condition testing"
    },
    
    # Unlimited discount codes
    "UNLIMITED10": {
        "description": "Unlimited 10% Off",
        "discount_type": "PERCENT_TOTAL",
        "value": 10.0,
        "max_uses": None,  # Unlimited
        "is_active": True,
        "purpose": "Baseline testing without limits"
    },
    "STUDENT5": {
        "description": "Student 5% Discount",
        "discount_type": "PERCENT_TOTAL",
        "value": 5.0,
        "max_uses": None,  # Unlimited
        "is_active": True,
        "purpose": "Low-value unlimited testing"
    },
    
    # Test codes for error scenarios
    "EXPIRED": {
        "description": "Expired Test Discount",
        "discount_type": "PERCENT_TOTAL",
        "value": 15.0,
        "max_uses": 100,
        "is_active": False,  # Disabled
        "purpose": "Error handling testing"
    }
}

# Automatic shipping discount test scenarios
SHIPPING_DISCOUNT_SCENARIOS = [
    {
        "name": "Below Threshold",
        "target_value": 50.00,
        "expected_discount": None,
        "description": "Should not trigger any automatic shipping discount"
    },
    {
        "name": "20% Shipping Discount",
        "target_value": 75.00,
        "expected_discount": "20%",
        "description": "Should trigger 20% shipping discount"
    },
    {
        "name": "Free Shipping Threshold",
        "target_value": 150.00,
        "expected_discount": "FREE",
        "description": "Should trigger free shipping"
    },
    {
        "name": "Above Free Shipping",
        "target_value": 200.00,
        "expected_discount": "FREE",
        "description": "Should maintain free shipping for high values"
    },
    {
        "name": "Edge Case - Just Below 20%",
        "target_value": 74.99,
        "expected_discount": None,
        "description": "Edge case testing just below 20% threshold"
    },
    {
        "name": "Edge Case - Just Below Free",
        "target_value": 149.99,
        "expected_discount": "20%",
        "description": "Edge case testing just below free shipping threshold"
    }
]

# Load testing configuration
LOAD_TEST_CONFIG = {
    "race_condition_intensity": {
        "users": 50,  # Number of concurrent users for race testing
        "spawn_rate": 10,  # Users spawned per second
        "duration": "5m",  # Test duration
        "target_discount": "RACE50"  # Primary discount for race testing
    },
    
    "shipping_discount_validation": {
        "users": 20,
        "spawn_rate": 5,
        "duration": "10m",
        "scenarios": SHIPPING_DISCOUNT_SCENARIOS
    },
    
    "mixed_scenario_testing": {
        "users": 30,
        "spawn_rate": 5,
        "duration": "15m",
        "automatic_threshold": 150.0,  # Value to trigger automatic discounts
        "manual_codes": ["UNLIMITED10", "STUDENT5"]
    },
    
    "stress_testing": {
        "users": 100,
        "spawn_rate": 20,
        "duration": "30m",
        "description": "High-load testing of all discount features"
    }
}

# Expected API endpoints for discount testing
API_ENDPOINTS = {
    "basket": "/api/v1/basket/",
    "basket_add": "/api/v1/basket/?basket",
    "basket_discount": "/api/v1/basket/discount/",
    "checkout": "/api/v1/checkout/",
    "product_find": "/api/v1/pages/find",
    "basket_clear": "/api/v1/basket/"  # DELETE request
}

# Performance thresholds for monitoring
PERFORMANCE_THRESHOLDS = {
    "discount_application_time": 500,  # ms - Max time to apply discount
    "basket_update_time": 300,  # ms - Max time to update basket
    "race_condition_detection_time": 100,  # ms - Max time to detect race condition
    "automatic_discount_calculation_time": 200,  # ms - Max time for automatic discount
    
    "success_rate_minimum": 95.0,  # % - Minimum success rate for unlimited discounts
    "race_condition_prevention_rate": 99.0,  # % - Rate at which race conditions should be prevented
    "automatic_discount_accuracy": 100.0  # % - Accuracy of automatic discount application
}

# Monitoring and alerting configuration
MONITORING_CONFIG = {
    "log_levels": {
        "discount_events": "INFO",
        "race_conditions": "WARNING", 
        "errors": "ERROR",
        "performance": "INFO"
    },
    
    "metrics_to_track": [
        "discount_application_success_rate",
        "race_condition_detection_rate",
        "automatic_discount_accuracy",
        "average_response_time",
        "error_rate",
        "concurrent_user_peak"
    ],
    
    "alert_conditions": {
        "high_error_rate": 5.0,  # % - Alert if error rate exceeds this
        "slow_response_time": 1000,  # ms - Alert if response time exceeds this
        "race_condition_failure": 1.0,  # % - Alert if race conditions aren't prevented
        "automatic_discount_failure": 0.1  # % - Alert if automatic discounts fail
    }
}

# Test data validation
VALIDATION_RULES = {
    "basket_value_tolerance": 0.20,  # 20% tolerance for basket value targeting
    "discount_amount_precision": 0.01,  # Precision for discount amount validation
    "response_time_samples": 100,  # Number of samples for response time analysis
    "concurrent_request_batches": 10  # Number of batches for race condition testing
}

# Setup instructions for test environment
SETUP_INSTRUCTIONS = """
Before running discount load tests, ensure the following:

1. Create test discount codes in the admin:
   - Navigate to /admin/shop/discount/
   - Create each discount code from TEST_DISCOUNT_CODES
   - Verify max_uses limits are set correctly

2. Configure automatic shipping discounts:
   - Ensure AutomaticShippingDiscount objects exist for:
     * 20% discount at €75 threshold
     * Free shipping at €150 threshold
   - Verify geographic restrictions if applicable

3. Set up monitoring:
   - Enable Redis monitoring for reservation tracking
   - Configure logging for discount events
   - Set up performance monitoring dashboards

4. Prepare test environment:
   - Ensure sufficient product inventory
   - Configure dummy payment method for load testing
   - Set up load balancing if testing at scale

5. Run tests in sequence:
   a) Shipping discount validation (low load)
   b) Race condition testing (medium load)  
   c) Mixed scenario testing (medium load)
   d) Stress testing (high load)

6. Monitor during tests:
   - Watch Redis memory usage
   - Monitor database connection pool
   - Track discount usage statistics
   - Observe race condition prevention logs
"""
