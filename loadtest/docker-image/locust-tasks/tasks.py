import random
import time
import json
from typing import Dict, List, Optional

from locust import FastHttpUser, between, task, events
from locust.exception import RescheduleTask

LOADTEST_DATA = {
    "product_urls": [
        "/en/collections/urban-collection/madkat-mosaic-t-shirt/",
        "/en/collections/urban-collection/madkat-mosaic-hoodie/",
        "/en/collections/urban-collection/madkat-shadow-t-shirt/",
        "/en/collections/urban-collection/madkat-shadow-hoodie/",
        "/en/collections/urban-collection/madkat-urban-mosaic-kata-t-shirt/",
        "/en/collections/urban-collection/madkat-hoodie-mosaic-kata/",
        "/en/collections/urban-collection/madkat-shadow-kata-t-shirt/",
        "/en/collections/urban-collection/madkat-shadow-kata-hoodie/",
        "/en/collections/urban-collection/madkat-bright-shadow-camiseta/",
        "/en/collections/urban-collection/urban-cap/",
        "/en/collections/urban-collection/urban-white-cap/",
        "/en/collections/original-collection/mad-kat-originals-t-shirt/",
        "/en/collections/original-collection/madkat-originals-hoodie/",
        "/en/collections/original-collection/madkat-originals-embroidered-t-shirt/",
        "/en/collections/original-collection/madkat-originals-embroidered-hoodie/",
        "/en/collections/original-collection/madkat-beanie/",
        "/en/collections/original-collection/madkat-originals-bottle/",
        "/en/collections/original-collection/madkat-originals-backpack/",
        "/en/collections/original-collection/madkat-pins/",
        "/en/collections/jackets-hoodies/madkat-originals-hoodie/",
        "/en/collections/jackets-hoodies/madkat-originals-embroidered-hoodie/",
        "/en/collections/jackets-hoodies/madkat-mosaic-hoodies/",
        "/en/collections/jackets-hoodies/madkat-shadow-hoodie/",
        "/en/collections/jackets-hoodies/madkat-mosaic-kata-hoodie/",
        "/en/collections/jackets-hoodies/madkat-shadow-kata-hoodie/",
        "/en/collections/t-shirts/madkat-originals-t-shirt/",
        "/en/collections/t-shirts/madkat-originals-embroidered-t-shirt/",
        "/en/collections/t-shirts/madkat-mosaic-t-shirt/",
        "/en/collections/t-shirts/madkat-shadow-t-shirt/",
        "/en/collections/t-shirts/madkat-mosaic-kata-t-shirt/",
        "/en/collections/t-shirts/madkat-shadow-kata-t-shirt/",
        "/en/collections/t-shirts/madkat-bright-shadow-t-shirt/",
        "/en/collections/accessories/madkat-beanie/",
        "/en/collections/accessories/madkat-originals-backpack/",
        "/en/collections/accessories/madkat-originals-bottle/",
        "/en/collections/accessories/madkat-pin/",
        "/en/collections/accessories/urban-cap/",
        "/en/collections/accessories/urban-white-cap/",
        "/en/collections/womans-173523/madkat-originals-embroidered-hoodie/",
        "/en/collections/womans-173523/madkat-originals-bottle/",
        "/es/colecciones/urban-collection/madkat-mosaic-t-shirt/",
        "/es/colecciones/urban-collection/madkat-mosaic-hoodie-1/",
        "/es/colecciones/urban-collection/madkat-shadow-t-shirt/",
        "/es/colecciones/urban-collection/madkat-shadow-hoodie/",
        "/es/colecciones/urban-collection/madkat-urban-mosaic-kata-t-shirt/",
        "/es/colecciones/urban-collection/madkat-hoodie-mosaic-kata/",
        "/es/colecciones/urban-collection/madkat-shadow-kata-t-shirt/",
        "/es/colecciones/urban-collection/madkat-shadow-kata-hoodie/",
        "/es/colecciones/urban-collection/madkat-bright-shadow-camiseta/",
        "/es/colecciones/urban-collection/urban-cap-1/",
        "/es/colecciones/urban-collection/urban-white-cap/",
        "/es/colecciones/original-collection/madkat-originals-camiseta/",
        "/es/colecciones/original-collection/madkat-originals-camiseta-bordada/",
        "/es/colecciones/original-collection/madkat-originals-sudadera/",
        "/es/colecciones/original-collection/madkat-originals-sudadera-bordada/",
        "/es/colecciones/original-collection/madkat-beanie/",
        "/es/colecciones/original-collection/madkat-originals-bottle/",
        "/es/colecciones/original-collection/madkat-originals-backpack/",
        "/es/colecciones/original-collection/madkat-pins/",
        "/es/colecciones/jackets-hoodies/madkat-originals-hoodie/",
        "/es/colecciones/jackets-hoodies/madkat-originals-embroidered-hoodie/",
        "/es/colecciones/jackets-hoodies/madkat-mosaic-hoodies/",
        "/es/colecciones/jackets-hoodies/madkat-shadow-hoodie/",
        "/es/colecciones/jackets-hoodies/madkat-mosaic-kata-hoodie/",
        "/es/colecciones/jackets-hoodies/madkat-shadow-kata-hoodie/",
        "/es/colecciones/t-shirts/madkat-originals-camiseta/",
        "/es/colecciones/t-shirts/madkat-originals-camiseta-bordada/",
        "/es/colecciones/t-shirts/madkat-mosaic-t-shirt/",
        "/es/colecciones/t-shirts/madkat-shadow-t-shirt/",
        "/es/colecciones/t-shirts/madkat-mosaic-kata-t-shirt/",
        "/es/colecciones/t-shirts/madkat-shadow-kata-t-shirt/",
        "/es/colecciones/t-shirts/madkat-bright-shadow-camiseta/",
        "/es/colecciones/accessories/madkat-beanie/",
        "/es/colecciones/accessories/madkat-backpack/",
        "/es/colecciones/accessories/madkat-bottle/",
        "/es/colecciones/accessories/madkat-pin/",
        "/es/colecciones/accessories/urban-cap/",
        "/es/colecciones/accessories/urban-white-cap/",
    ],
    "collection_urls": [
        "/en/collections/tactical-collection/",
        "/en/collections/urban-collection/",
        "/en/collections/original-collection/",
        "/en/collections/jackets-hoodies/",
        "/en/collections/t-shirts/",
        "/en/collections/accessories/",
        "/es/colecciones/tactical-collection/",
        "/es/colecciones/urban-collection/",
        "/es/colecciones/original-collection/",
        "/es/colecciones/jackets-hoodies/",
        "/es/colecciones/t-shirts/",
        "/es/colecciones/accessories/",
    ],
    "discount_codes": [
        # Limited discount codes for race condition testing
        "FLASH50",  # 50% off, limited uses
        "SAVE20",  # 20% off, limited uses
        "WELCOME10",  # 10% off, limited uses
        "RACE50",  # Test discount for race conditions
        "LIMITED25",  # 25% off, very limited uses
        # Unlimited discount codes
        "UNLIMITED10",  # 10% off, unlimited
        "STUDENT5",  # 5% off, unlimited
        # Invalid codes for error testing
        "EXPIRED",  # Expired discount
        "INVALID123",  # Non-existent code
        "DISABLED",  # Disabled discount
    ],
    "shipping_test_scenarios": [
        # Test automatic shipping discounts
        {"target_value": 50.00, "expected_discount": None},  # Below threshold
        {"target_value": 75.00, "expected_discount": "20%"},  # 20% shipping discount
        {"target_value": 150.00, "expected_discount": "FREE"},  # Free shipping
        {
            "target_value": 200.00,
            "expected_discount": "FREE",
        },  # Free shipping (above threshold)
    ],
}


def get_random_data(property_name: str):
    return random.choice(LOADTEST_DATA.get(property_name, []))


def get_sample_data(property_name: str, count: int):
    return random.sample(LOADTEST_DATA.get(property_name, []), count)


# Global statistics for tracking discount testing
discount_stats = {
    "successful_applications": 0,
    "failed_applications": 0,
    "race_condition_detected": 0,
    "automatic_discounts_applied": 0,
    "shipping_discounts_applied": 0,
}


def log_discount_event(event_type: str, discount_code: str = None, details: str = None):
    """Log discount-related events for analysis"""
    timestamp = time.time()
    message = f"[DISCOUNT] {event_type}"
    if discount_code:
        message += f" - Code: {discount_code}"
    if details:
        message += f" - {details}"
    print(f"{timestamp}: {message}")


def get_limited_discount_code():
    """Get a limited discount code for race condition testing"""
    limited_codes = ["FLASH50", "SAVE20", "WELCOME10", "RACE50", "LIMITED25"]
    return random.choice(limited_codes)


def get_unlimited_discount_code():
    """Get an unlimited discount code"""
    unlimited_codes = ["UNLIMITED10", "STUDENT5"]
    return random.choice(unlimited_codes)


def get_invalid_discount_code():
    """Get an invalid discount code for error testing"""
    invalid_codes = ["EXPIRED", "INVALID123", "DISABLED"]
    return random.choice(invalid_codes)


def build_basket_to_value(client, target_value: float) -> List[Dict]:
    """Build a basket to reach approximately the target value"""
    basket_items = []
    current_value = 0.0
    attempts = 0
    max_attempts = 10

    while current_value < target_value and attempts < max_attempts:
        # Get a random product
        product_url = get_random_data("product_urls")
        response = client.get("/api/v1/pages/find" + product_url)

        if response.status_code == 200:
            try:
                data = response.json()
                variants = data.get("variants", [])
                if variants:
                    variant = random.choice(variants)
                    price = float(variant.get("price", 0))

                    # Add item if it doesn't exceed target too much
                    if current_value + price <= target_value * 1.2:  # Allow 20% overage
                        item_data = {
                            "product_type": variant["product_type"],
                            "product_id": variant["id"],
                        }
                        add_response = client.post(
                            "/api/v1/basket/?basket", json=item_data
                        )

                        if add_response.status_code in [200, 201]:
                            basket_items.append(variant)
                            current_value += price

            except (ValueError, KeyError, json.JSONDecodeError):
                pass

        attempts += 1

    return basket_items


class Customer(FastHttpUser):
    """
    Enhanced Customer user with discount and race condition testing.
    """

    wait_time = between(1, 7)

    def on_start(self):
        """Initialize user session"""
        self.basket_id = None
        self.applied_discounts = []

    @task(8)
    def view_collection(self):
        self.client.get("/api/v1/pages/find" + get_random_data("collection_urls"))

    @task(5)
    def view_and_add_product(self):
        response = self.client.get(
            "/api/v1/pages/find" + get_random_data("product_urls")
        )

        # 25% chance to add product to basket
        if response.status_code == 200 and random.random() < 0.25:
            variants = response.json()["variants"]
            if len(variants):
                obj = random.choice(variants)
                data = {"product_type": obj["product_type"], "product_id": obj["id"]}
                self.client.post("/api/v1/basket/?basket", json=data)

                # 50% chance to purchase the product
                if random.random() < 0.50:
                    self._create_checkout()

    @task(2)
    def view_basket(self):
        self.client.get("/api/v1/basket/")

    @task(1)
    def view_checkout(self):
        self.client.get("/api/v1/checkout/")

    @task(3)
    def test_automatic_shipping_discount(self):
        """Test automatic shipping discounts based on basket value"""
        scenario = random.choice(LOADTEST_DATA["shipping_test_scenarios"])
        target_value = scenario["target_value"]
        expected_discount = scenario["expected_discount"]

        log_discount_event(
            "TESTING_AUTOMATIC_SHIPPING",
            details=f"Target: €{target_value}, Expected: {expected_discount}",
        )

        # Clear basket first
        self.client.delete("/api/v1/basket/")

        # Build basket to target value
        basket_items = build_basket_to_value(self.client, target_value)

        if basket_items:
            # Check basket for automatic discounts
            basket_response = self.client.get("/api/v1/basket/")
            if basket_response.status_code == 200:
                try:
                    basket_data = basket_response.json()
                    total_value = float(basket_data.get("total", 0))
                    extra_rows = basket_data.get("extra_rows", [])

                    # Check for shipping discounts
                    shipping_discount_found = False
                    for row in extra_rows:
                        if row.get("modifier") == "automatic_shipping_discount":
                            shipping_discount_found = True
                            discount_stats["automatic_discounts_applied"] += 1
                            discount_stats["shipping_discounts_applied"] += 1
                            log_discount_event(
                                "AUTOMATIC_SHIPPING_APPLIED",
                                details=f"Value: €{total_value}, Discount: {row.get('amount')}",
                            )
                            break

                    # Validate expected behavior
                    if expected_discount and not shipping_discount_found:
                        log_discount_event(
                            "AUTOMATIC_SHIPPING_MISSING",
                            details=f"Expected {expected_discount} but not found",
                        )
                    elif not expected_discount and shipping_discount_found:
                        log_discount_event(
                            "UNEXPECTED_SHIPPING_DISCOUNT",
                            details=f"Unexpected discount applied",
                        )

                except (ValueError, KeyError, json.JSONDecodeError) as e:
                    log_discount_event("AUTOMATIC_SHIPPING_ERROR", details=str(e))

    @task(2)
    def test_limited_discount_code_race_condition(self):
        """Test limited discount codes for race conditions"""
        discount_code = get_limited_discount_code()

        log_discount_event("TESTING_RACE_CONDITION", discount_code)

        # Clear basket and add some items
        self.client.delete("/api/v1/basket/")

        # Add a few random items
        for _ in range(random.randint(1, 3)):
            product_url = get_random_data("product_urls")
            response = self.client.get("/api/v1/pages/find" + product_url)

            if response.status_code == 200:
                try:
                    variants = response.json().get("variants", [])
                    if variants:
                        variant = random.choice(variants)
                        item_data = {
                            "product_type": variant["product_type"],
                            "product_id": variant["id"],
                        }
                        self.client.post("/api/v1/basket/?basket", json=item_data)
                except (ValueError, KeyError, json.JSONDecodeError):
                    continue

        # Try to apply the limited discount code
        discount_data = {"discount_code": discount_code}
        discount_response = self.client.post(
            "/api/v1/basket/discount/", json=discount_data
        )

        if discount_response.status_code == 200:
            discount_stats["successful_applications"] += 1
            log_discount_event("DISCOUNT_APPLIED_SUCCESS", discount_code)
            self.applied_discounts.append(discount_code)
        elif discount_response.status_code == 400:
            try:
                error_data = discount_response.json()
                error_message = error_data.get("error", "Unknown error")

                if "no longer available" in error_message.lower():
                    discount_stats["race_condition_detected"] += 1
                    log_discount_event(
                        "RACE_CONDITION_DETECTED", discount_code, error_message
                    )
                else:
                    discount_stats["failed_applications"] += 1
                    log_discount_event("DISCOUNT_FAILED", discount_code, error_message)
            except json.JSONDecodeError:
                discount_stats["failed_applications"] += 1
                log_discount_event(
                    "DISCOUNT_FAILED", discount_code, "JSON decode error"
                )
        else:
            discount_stats["failed_applications"] += 1
            log_discount_event(
                "DISCOUNT_FAILED",
                discount_code,
                f"HTTP {discount_response.status_code}",
            )

    @task(1)
    def test_unlimited_discount_code(self):
        """Test unlimited discount codes (should always work)"""
        discount_code = get_unlimited_discount_code()

        log_discount_event("TESTING_UNLIMITED", discount_code)

        # Clear basket and add items
        self.client.delete("/api/v1/basket/")

        # Add at least one item
        product_url = get_random_data("product_urls")
        response = self.client.get("/api/v1/pages/find" + product_url)

        if response.status_code == 200:
            try:
                variants = response.json().get("variants", [])
                if variants:
                    variant = random.choice(variants)
                    item_data = {
                        "product_type": variant["product_type"],
                        "product_id": variant["id"],
                    }
                    add_response = self.client.post(
                        "/api/v1/basket/?basket", json=item_data
                    )

                    if add_response.status_code in [200, 201]:
                        # Apply unlimited discount
                        discount_data = {"discount_code": discount_code}
                        discount_response = self.client.post(
                            "/api/v1/basket/discount/", json=discount_data
                        )

                        if discount_response.status_code == 200:
                            discount_stats["successful_applications"] += 1
                            log_discount_event(
                                "UNLIMITED_DISCOUNT_SUCCESS", discount_code
                            )
                        else:
                            log_discount_event(
                                "UNLIMITED_DISCOUNT_FAILED",
                                discount_code,
                                f"HTTP {discount_response.status_code}",
                            )

            except (ValueError, KeyError, json.JSONDecodeError) as e:
                log_discount_event("UNLIMITED_DISCOUNT_ERROR", discount_code, str(e))

    def _create_checkout(self):
        """
        Test checkout using dummy payment, available only for load-testing.
        """
        data = {
            "email": "<EMAIL>",
            "shipping_address": "Load\nTest\nKorzo 35\n51000\nRijeka\nHR\n\n+385922332183",
            "billing_address": "Load\nTest\nKorzo 35\n51000\nRijeka\nHR\n\n+385922332183",
            "payment_method": "dummy",
        }
        self.client.post("/api/v1/checkout/", json=data)


class RaceConditionTester(FastHttpUser):
    """
    Specialized user for intensive race condition testing.
    Focuses on limited discount codes with high concurrency.
    """

    wait_time = between(0.1, 1)  # Very fast requests to create race conditions
    weight = 2  # Higher weight for more aggressive testing

    def on_start(self):
        """Initialize race condition tester"""
        self.target_discount = "RACE50"  # Focus on one discount for maximum contention
        self.successful_applications = 0

    @task(10)
    def rapid_discount_application(self):
        """Rapidly try to apply the same limited discount code"""
        # Clear basket
        self.client.delete("/api/v1/basket/")

        # Add one item quickly
        product_url = get_random_data("product_urls")
        response = self.client.get("/api/v1/pages/find" + product_url)

        if response.status_code == 200:
            try:
                variants = response.json().get("variants", [])
                if variants:
                    variant = random.choice(variants)
                    item_data = {
                        "product_type": variant["product_type"],
                        "product_id": variant["id"],
                    }
                    add_response = self.client.post(
                        "/api/v1/basket/?basket", json=item_data
                    )

                    if add_response.status_code in [200, 201]:
                        # Immediately try to apply discount
                        discount_data = {"discount_code": self.target_discount}
                        discount_response = self.client.post(
                            "/api/v1/basket/discount/", json=discount_data
                        )

                        if discount_response.status_code == 200:
                            self.successful_applications += 1
                            log_discount_event(
                                "RACE_TESTER_SUCCESS",
                                self.target_discount,
                                f"Total successes: {self.successful_applications}",
                            )
                        elif discount_response.status_code == 400:
                            try:
                                error_data = discount_response.json()
                                error_message = error_data.get("error", "")
                                if "no longer available" in error_message.lower():
                                    log_discount_event(
                                        "RACE_TESTER_BLOCKED",
                                        self.target_discount,
                                        "Correctly blocked by race condition prevention",
                                    )
                            except json.JSONDecodeError:
                                pass

            except (ValueError, KeyError, json.JSONDecodeError):
                pass


class ShippingDiscountTester(FastHttpUser):
    """
    Specialized user for testing automatic shipping discounts.
    Builds baskets to specific values to trigger different discount tiers.
    """

    wait_time = between(2, 5)
    weight = 1

    @task(5)
    def test_shipping_discount_thresholds(self):
        """Test all shipping discount thresholds systematically"""
        for scenario in LOADTEST_DATA["shipping_test_scenarios"]:
            target_value = scenario["target_value"]
            expected_discount = scenario["expected_discount"]

            log_discount_event(
                "SHIPPING_THRESHOLD_TEST", details=f"Testing €{target_value} threshold"
            )

            # Clear basket
            self.client.delete("/api/v1/basket/")

            # Build basket to exact value
            basket_items = build_basket_to_value(self.client, target_value)

            if basket_items:
                # Check for automatic shipping discount
                basket_response = self.client.get("/api/v1/basket/")
                if basket_response.status_code == 200:
                    try:
                        basket_data = basket_response.json()
                        extra_rows = basket_data.get("extra_rows", [])

                        shipping_discount_found = any(
                            row.get("modifier") == "automatic_shipping_discount"
                            for row in extra_rows
                        )

                        # Validate behavior
                        if expected_discount and shipping_discount_found:
                            log_discount_event(
                                "SHIPPING_THRESHOLD_CORRECT",
                                details=f"€{target_value} correctly triggered {expected_discount}",
                            )
                        elif expected_discount and not shipping_discount_found:
                            log_discount_event(
                                "SHIPPING_THRESHOLD_MISSING",
                                details=f"€{target_value} should have triggered {expected_discount}",
                            )
                        elif not expected_discount and not shipping_discount_found:
                            log_discount_event(
                                "SHIPPING_THRESHOLD_CORRECT",
                                details=f"€{target_value} correctly had no discount",
                            )
                        else:
                            log_discount_event(
                                "SHIPPING_THRESHOLD_UNEXPECTED",
                                details=f"€{target_value} unexpectedly triggered discount",
                            )

                    except (ValueError, KeyError, json.JSONDecodeError) as e:
                        log_discount_event("SHIPPING_THRESHOLD_ERROR", details=str(e))

            # Small delay between tests
            time.sleep(1)


class MixedScenarioTester(FastHttpUser):
    """
    Tests complex scenarios with both automatic and manual discounts.
    """

    wait_time = between(3, 8)
    weight = 1

    @task(3)
    def test_automatic_plus_manual_discount(self):
        """Test applying manual discount codes on top of automatic shipping discounts"""
        # Build basket to trigger automatic shipping discount
        target_value = 150.0  # Should trigger free shipping

        log_discount_event(
            "MIXED_SCENARIO_TEST", details=f"Building €{target_value} basket"
        )

        # Clear basket
        self.client.delete("/api/v1/basket/")

        # Build basket
        basket_items = build_basket_to_value(self.client, target_value)

        if basket_items:
            # Check for automatic discount first
            basket_response = self.client.get("/api/v1/basket/")
            if basket_response.status_code == 200:
                try:
                    basket_data = basket_response.json()
                    extra_rows = basket_data.get("extra_rows", [])

                    automatic_discount_found = any(
                        row.get("modifier") == "automatic_shipping_discount"
                        for row in extra_rows
                    )

                    if automatic_discount_found:
                        log_discount_event(
                            "MIXED_AUTOMATIC_FOUND",
                            details="Automatic shipping discount applied",
                        )

                        # Now try to apply a manual discount code
                        manual_discount = get_unlimited_discount_code()
                        discount_data = {"discount_code": manual_discount}
                        discount_response = self.client.post(
                            "/api/v1/basket/discount/", json=discount_data
                        )

                        if discount_response.status_code == 200:
                            log_discount_event(
                                "MIXED_MANUAL_SUCCESS",
                                manual_discount,
                                "Manual discount applied with automatic",
                            )

                            # Check final basket state
                            final_basket = self.client.get("/api/v1/basket/")
                            if final_basket.status_code == 200:
                                final_data = final_basket.json()
                                final_rows = final_data.get("extra_rows", [])

                                discount_count = sum(
                                    1
                                    for row in final_rows
                                    if "discount" in row.get("modifier", "")
                                )

                                log_discount_event(
                                    "MIXED_FINAL_STATE",
                                    details=f"Total discounts applied: {discount_count}",
                                )
                        else:
                            log_discount_event(
                                "MIXED_MANUAL_FAILED",
                                manual_discount,
                                f"HTTP {discount_response.status_code}",
                            )
                    else:
                        log_discount_event(
                            "MIXED_NO_AUTOMATIC", details="No automatic discount found"
                        )

                except (ValueError, KeyError, json.JSONDecodeError) as e:
                    log_discount_event("MIXED_SCENARIO_ERROR", details=str(e))


# Event listeners for statistics reporting
@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Print discount testing statistics when test stops"""
    print("\n" + "=" * 60)
    print("DISCOUNT TESTING STATISTICS")
    print("=" * 60)
    print(f"Successful Applications: {discount_stats['successful_applications']}")
    print(f"Failed Applications: {discount_stats['failed_applications']}")
    print(f"Race Conditions Detected: {discount_stats['race_condition_detected']}")
    print(
        f"Automatic Discounts Applied: {discount_stats['automatic_discounts_applied']}"
    )
    print(f"Shipping Discounts Applied: {discount_stats['shipping_discounts_applied']}")

    total_attempts = (
        discount_stats["successful_applications"]
        + discount_stats["failed_applications"]
    )
    if total_attempts > 0:
        success_rate = (
            discount_stats["successful_applications"] / total_attempts
        ) * 100
        race_condition_rate = (
            discount_stats["race_condition_detected"] / total_attempts
        ) * 100
        print(f"Success Rate: {success_rate:.2f}%")
        print(f"Race Condition Rate: {race_condition_rate:.2f}%")

    print("=" * 60)
