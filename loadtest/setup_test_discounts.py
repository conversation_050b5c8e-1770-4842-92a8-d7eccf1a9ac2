#!/usr/bin/env python3
"""
<PERSON><PERSON>t to automatically create test discount codes for load testing.
Run this script to set up all required discount codes before running load tests.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedel<PERSON>

# Add the API directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'api'))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings.development')
django.setup()

from shop.models import Discount, AutomaticShippingDiscount


def create_test_discount_codes():
    """Create all test discount codes required for load testing"""
    
    test_codes = {
        "FLASH50": {
            "description": "Flash Sale 50% Off - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("50.00"),
            "max_uses": 100,
            "is_active": True,
        },
        "SAVE20": {
            "description": "Save 20% Off - Load Test",
            "discount_type": "PERCENT_TOTAL", 
            "value": Decimal("20.00"),
            "max_uses": 50,
            "is_active": True,
        },
        "RACE50": {
            "description": "Race Condition Test Discount",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("50.00"),
            "max_uses": 10,  # Very limited for intensive race testing
            "is_active": True,
        },
        "LIMITED25": {
            "description": "Very Limited 25% Off - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("25.00"),
            "max_uses": 5,  # Extremely limited
            "is_active": True,
        },
        "UNLIMITED10": {
            "description": "Unlimited 10% Off - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("10.00"),
            "max_uses": None,  # Unlimited
            "is_active": True,
        },
        "STUDENT5": {
            "description": "Student 5% Discount - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("5.00"),
            "max_uses": None,  # Unlimited
            "is_active": True,
        },
        "EXPIRED": {
            "description": "Expired Test Discount - Load Test",
            "discount_type": "PERCENT_TOTAL",
            "value": Decimal("15.00"),
            "max_uses": 100,
            "is_active": False,  # Disabled for error testing
        }
    }
    
    created_codes = []
    updated_codes = []
    
    for code, config in test_codes.items():
        try:
            # Try to get existing discount
            discount = Discount.objects.get(code=code)
            
            # Update existing discount
            for field, value in config.items():
                setattr(discount, field, value)
            
            # Reset usage counter for testing
            discount.times_used = 0
            discount.save()
            
            updated_codes.append(code)
            print(f"✅ Updated existing discount code: {code}")
            
        except Discount.DoesNotExist:
            # Create new discount
            discount = Discount.objects.create(
                code=code,
                times_used=0,
                **config
            )
            created_codes.append(code)
            print(f"🆕 Created new discount code: {code}")
    
    return created_codes, updated_codes


def create_automatic_shipping_discounts():
    """Create automatic shipping discount configurations"""
    
    shipping_discounts = [
        {
            "name": "20% Shipping Discount - Load Test",
            "discount_type": "PERCENT_SHIPPING",
            "value": Decimal("20.00"),
            "min_basket_value": Decimal("75.00"),
            "max_basket_value": Decimal("149.99"),
            "is_active": True,
            "priority": 1,
        },
        {
            "name": "Free Shipping - Load Test", 
            "discount_type": "FREE_SHIPPING",
            "value": Decimal("100.00"),
            "min_basket_value": Decimal("150.00"),
            "max_basket_value": None,
            "is_active": True,
            "priority": 2,
        }
    ]
    
    created_discounts = []
    updated_discounts = []
    
    for config in shipping_discounts:
        try:
            # Try to find existing discount by name
            discount = AutomaticShippingDiscount.objects.get(name=config["name"])
            
            # Update existing
            for field, value in config.items():
                setattr(discount, field, value)
            discount.save()
            
            updated_discounts.append(config["name"])
            print(f"✅ Updated automatic shipping discount: {config['name']}")
            
        except AutomaticShippingDiscount.DoesNotExist:
            # Create new
            discount = AutomaticShippingDiscount.objects.create(**config)
            created_discounts.append(config["name"])
            print(f"🆕 Created automatic shipping discount: {config['name']}")
    
    return created_discounts, updated_discounts


def verify_setup():
    """Verify that all required discounts are properly configured"""
    
    print("\n🔍 Verifying discount setup...")
    
    # Check manual discount codes
    required_codes = ["FLASH50", "SAVE20", "RACE50", "LIMITED25", "UNLIMITED10", "STUDENT5", "EXPIRED"]
    missing_codes = []
    
    for code in required_codes:
        try:
            discount = Discount.objects.get(code=code)
            status = "✅ ACTIVE" if discount.is_active else "❌ INACTIVE"
            limit = f"(limit: {discount.max_uses})" if discount.max_uses else "(unlimited)"
            print(f"   {code}: {status} {limit}")
        except Discount.DoesNotExist:
            missing_codes.append(code)
            print(f"   {code}: ❌ MISSING")
    
    # Check automatic shipping discounts
    shipping_count = AutomaticShippingDiscount.objects.filter(is_active=True).count()
    print(f"\n📦 Automatic shipping discounts: {shipping_count} active")
    
    for discount in AutomaticShippingDiscount.objects.filter(is_active=True):
        print(f"   {discount.name}: €{discount.min_basket_value}+ → {discount.value}% off")
    
    # Summary
    if missing_codes:
        print(f"\n❌ Setup incomplete. Missing codes: {', '.join(missing_codes)}")
        return False
    else:
        print(f"\n✅ Setup complete! All {len(required_codes)} discount codes configured.")
        return True


def reset_usage_counters():
    """Reset usage counters for all test discount codes"""
    
    print("\n🔄 Resetting usage counters...")
    
    test_codes = ["FLASH50", "SAVE20", "RACE50", "LIMITED25", "UNLIMITED10", "STUDENT5", "EXPIRED"]
    reset_count = 0
    
    for code in test_codes:
        try:
            discount = Discount.objects.get(code=code)
            if discount.times_used > 0:
                old_count = discount.times_used
                discount.times_used = 0
                discount.save()
                print(f"   {code}: {old_count} → 0")
                reset_count += 1
            else:
                print(f"   {code}: already at 0")
        except Discount.DoesNotExist:
            print(f"   {code}: not found")
    
    print(f"\n✅ Reset {reset_count} discount counters")


def main():
    """Main setup function"""
    
    print("🚀 Setting up discount codes for load testing")
    print("=" * 60)
    
    try:
        # Create/update discount codes
        created_codes, updated_codes = create_test_discount_codes()
        
        print(f"\n📊 Manual Discount Codes:")
        print(f"   Created: {len(created_codes)}")
        print(f"   Updated: {len(updated_codes)}")
        
        # Create/update automatic shipping discounts
        created_shipping, updated_shipping = create_automatic_shipping_discounts()
        
        print(f"\n📦 Automatic Shipping Discounts:")
        print(f"   Created: {len(created_shipping)}")
        print(f"   Updated: {len(updated_shipping)}")
        
        # Reset usage counters
        reset_usage_counters()
        
        # Verify setup
        success = verify_setup()
        
        if success:
            print("\n🎉 Load testing setup complete!")
            print("\nNext steps:")
            print("1. Run load tests: python run_discount_tests.py https://your-site.com")
            print("2. Monitor discount usage in admin: /admin/shop/discount/monitor/")
            print("3. Check race condition prevention in logs")
            
            return 0
        else:
            print("\n❌ Setup failed. Please check the errors above.")
            return 1
            
    except Exception as e:
        print(f"\n💥 Setup failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
