app = 'background-remover-api-cold-thunder-4727'
primary_region = 'lhr'

[build]

# This section tells Fly to mount the volume.
[mounts]
  source = 'bg_remover_cache'
  destination = '/data'

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 0

  [http_service.concurrency]
    type = "requests"
    hard_limit = 50
    soft_limit = 25

  processes = ['app']

[[vm]]
  memory = '512mb'
  cpu_kind = 'shared' 
  cpus = 1
