[tool.poetry]
name = "background-remover-api"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON> <PERSON> <ayo<PERSON><PERSON><EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.12, <3.14"
python-dotenv = "^1.1.1"
uvicorn = "^0.35.0"
llvmlite = "^0.44.0"
numba = "^0.61.2"
fastapi = "^0.115.14"
python-multipart = "^0.0.20"
requests = "^2.32.4"
rembg = "^2.0.66"
onnxruntime = "^1.22.0"
gunicorn = "^23.0.0"
redis = "^6.2.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
