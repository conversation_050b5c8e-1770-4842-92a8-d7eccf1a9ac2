import uvicorn
import requests
import io
import os
import hashlib
import redis

# Import web server and dependency injection tools
from fastapi import FastAPI, Form, Depends, HTTPException, Header
from fastapi.responses import StreamingResponse, FileResponse

# Import helper to load .env file
from dotenv import load_dotenv

# --- Configuration ---
load_dotenv()
app = FastAPI()

# Get the Redis connection URL from an environment variable provided by Fly.io
REDIS_URL = os.getenv("REDIS_URL")
if not REDIS_URL:
    # Fallback for local development if Redis isn't running
    print("Warning: REDIS_URL not found. Caching will be disabled.")
    redis_client = None
else:
    # Connect to the Redis database
    redis_client = redis.from_url(REDIS_URL)

# Read the secret API key from the environment
SECRET_KEY = os.getenv("SECRET_API_KEY")
if not SECRET_KEY:
    raise ValueError(
        "SECRET_API_KEY not found in environment variables. Please set it in the .env file."
    )


# --- Security: API Key Dependency ---
# This function will be run for every request to a protected endpoint
async def verify_api_key(x_api_key: str = Header(...)):
    if x_api_key != SECRET_KEY:
        raise HTTPException(status_code=401, detail="Invalid API Key")


# --- API Endpoint ---
@app.post("/remove", dependencies=[Depends(verify_api_key)])
async def remove_background_from_url(image_url: str = Form(...)):
    """
    Accepts an image URL. If cached, returns processed image and extends cache to 1 year.
    If not cached, returns original image without processing to reduce resource usage.
    """
    try:
        # Create a unique, safe cache key based on a hash of the URL
        url_hash = hashlib.sha256(image_url.encode()).hexdigest()

        # 1. --- Check Redis Cache ---
        if redis_client:
            cached_image = redis_client.get(url_hash)
            # If the image is found in the cache, extend expiration and return it
            if cached_image:
                print(f"Cache HIT for {image_url}")
                # Extend cache expiration to 1 year (31,536,000 seconds)
                redis_client.set(url_hash, cached_image, ex=31536000)

                # Create response with cache hit header
                response = StreamingResponse(
                    io.BytesIO(cached_image),
                    media_type="image/png",
                    headers={"X-Cache-Status": "HIT"},
                )
                return response

        print(f"Cache MISS for {image_url} - returning original image")

        # 2. --- For cache misses, return original image without processing ---
        # Download the original image from the provided URL
        image_response = requests.get(image_url, stream=True)
        image_response.raise_for_status()
        original_bytes = image_response.content

        # Return the original image without background removal to save resources
        response = StreamingResponse(
            io.BytesIO(original_bytes),
            media_type="image/png",
            headers={"X-Cache-Status": "MISS"},
        )
        return response

    except Exception as e:
        print(f"Error processing {image_url}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# This part is only for local development. The Dockerfile's CMD will override it.
if __name__ == "__main__":
    port = int(os.getenv("PORT", 5001))
    uvicorn.run(app, host="0.0.0.0", port=port)
