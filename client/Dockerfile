FROM node:17.0

WORKDIR /usr/src/app

# Install app requirements
COPY package.json yarn.lock ./
RUN yarn --pure-lockfile

ENV HOST=0.0.0.0
ENV PORT=3000

ARG BASE_URL
ARG API_URL
ARG DEMO_TOKEN

# Config
ENV BASE_URL=$BASE_URL
ENV API_URL=$API_URL
ENV DEMO_TOKEN=$DEMO_TOKEN

# Workaround for webpack (https://github.com/webpack/webpack/issues/14532)
ENV NODE_OPTIONS=--openssl-legacy-provider

COPY . .
RUN yarn build
RUN yarn cache clean

EXPOSE $PORT

CMD ["yarn", "start"]
