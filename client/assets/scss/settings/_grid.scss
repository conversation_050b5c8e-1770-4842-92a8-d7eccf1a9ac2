// Grid (https://medium.freecodecamp.org/the-100-correct-way-to-do-css-breakpoints-88d6a5ba1862)
$grids: (
  'mobile-large': 370px,
  'tablet': 660px,
  'tablet-large': 900px,
  'laptop': 1200px,
  'laptop-large': 1366px,
  'desktop': 1600px,
  'desktop-large': 1800px,
  'desktop-huge': 2200px,
);

$container-max: 1700px;
$container-huge: 1700px;
$container-large: 1300px;
$container-medium: 1050px;
$container-small: 380px;
