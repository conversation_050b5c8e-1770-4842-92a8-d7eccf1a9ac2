:root {
  // config.colors
  --white: #fff;
  --black: #000;

  --gray-333: #333;
  --gray-b3: #b3b3b3;
  --gray-ef: #efefef;
  --gray-f7: #f7f7f7;

  --page-bg-color: var(--white);
  --page-font-color: var(--black);
  --scrollbar-bg-color: var(--gray-f7);
  --scrollbar-color: var(--black);

  --backdrop-color: var(--white);
  --missing-image-bg-color: #f7f7f7;
  --error-color: #e63d3d;

  --button-bg-color: var(--page-font-color);
  --button-hocus-bg-color: var(--gray-333);
  --button-font-color: var(--white);

  --button-outline-color: var(--page-font-color);
  --button-outline-hocus-bg-color: var(--page-font-color);

  --button-outline-invert-color: var(--page-bg-color);
  --button-outline-invert-bg-color: var(--page-bg-color);
  --button-outline-invert-font-color: var(--page-bg-color);
  --button-outline-invert-hocus-bg-color: var(--button-hocus-bg-color);
  --button-outline-invert-hocus-font-color: var(--page-bg-color);

  --button-invert-font-color: var(--page-font-color);
  --button-invert-bg-color: var(--page-bg-color);
  --button-invert-hocus-font-color: var(--page-bg-color);
  --button-invert-hocus-bg-color: var(--page-font-color);

  --tag-bg-color: var(--black);
  --tag-font-color: var(--white);

  --cookies-bg-color: var(--page-font-color);
  --cookies-font-color: var(--page-bg-color);

  --order-success-color: #b6fb7c;
  --order-in-progress-color: #fbd37c;
  --order-failure-color: #ec6d6d;
  --order-not-available-color: var(--gray-b3);

  // config.font
  --font-family-title: 'termina';
  --title-medium: 500;
  --title-black: 900;

  --title-line-height: 1.25;

  --font-family: 'effra';
  --text-light: 300;
  // defaults to regular 400 when no weight is specified
  --text-medium: 500;
  --text-bold: 700;
  --text-heavy: 900;

  // config.transition
  --speed: 0.3s;
  --speed-fast: 0.15s;
  --easing: cubic-bezier(0.4, 0, 0, 1);

  // config.layout
  --layout-gap: 60px;

  // cms-driven veriables
  --collection-items-per-row: 4; // defaults to 4. can be switched to 3 in CMS. affects only largest media breakpoint.

  @include grid('tablet-large') {
    --layout-gap: 130px;
  }
  @include grid('desktop-large') {
    --layout-gap: 200px;
  }
}
