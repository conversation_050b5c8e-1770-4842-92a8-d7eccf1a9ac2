// ==========================================================================
// Generic
// ==========================================================================

html,
body {
  box-sizing: border-box;
  background-color: var(--page-bg-color);
  color: var(--page-font-color);
  font-family: var(--font-family), sans-serif;
  font-size: 1em;
  line-height: 1.5;

  // @media (min-width: 2400px) {
  //   font-size: 150%;
  // }

  // @media (min-width: 3600px) {
  //   font-size: 200%;
  // }
}

//
// Add the correct display in IE 10-.
// 1. Add the correct display in IE.
//
template, /* [1] */
[hidden] {
  display: none;
}

*,
:before,
:after {
  box-sizing: inherit;
}

address {
  font-style: inherit;
}

dfn,
cite,
em,
i {
  font-style: italic;
}

b,
strong {
  font-weight: var(--text-bold);
}

a {
  color: currentColor;
  text-decoration: none;

  svg {
    pointer-events: none;
  }

  // &:focus {
  //   color: currentColor;
  // }
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

p,
figure {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-size: 1em;
}

/**
 * 1. Single taps should be dispatched immediately on clickable elements
 */
a,
area,
button,
input,
label,
select,
textarea,
[tabindex] {
  -ms-touch-action: manipulation; /* [1] */
  touch-action: manipulation;
}

[hreflang] > abbr[title] {
  text-decoration: none;
}

table {
  border-spacing: 0;
  border-collapse: collapse;
}

hr {
  display: block;
  margin: 1em 0;
  padding: 0;
  height: 1px;
  border: 0;
  border-top: 1px solid #cccccc;
}

::selection {
  background-color: var(--page-font-color);
  color: var(--page-bg-color);
}

input[type='checkbox'],
input[type='radio'] {
  position: absolute;
  opacity: 0;
  left: -1000%;
  width: 1px;
  height: 1px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--speed) var(--easing);
  opacity: 1;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.scale-enter-active,
.scale-leave-active {
  transition: opacity var(--speed) var(--easing),
    transform var(--speed-fast) var(--easing);
  opacity: 1;
  transform: none;
}

.scale-enter,
.scale-leave-to {
  opacity: 0;
  transform: sclae(0.6667);
}
