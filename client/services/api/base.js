export default (context) => ({
  async get(url, options) {
    return await this.call('get', url, null, options)
  },
  async post(url, data, options) {
    return await this.call('post', url, data, options)
  },
  async put(url, data, options) {
    return await this.call('put', url, data, options)
  },
  async delete(url, options) {
    return await this.call('delete', url, null, options)
  },

  async call(method, url, data, options = {}) {
    // Set locale on every call
    if (!options.params) options.params = {}
    options.params.locale = context.app.i18n.locale
    options.params.currency = context.app.store.state.currency.currency

    const finalUrl = this.buildUrl(url)

    try {
      return await context.app.$axios({
        method,
        url: finalUrl,
        data,
        ...options,
      })
    } catch (error) {
      this.handleError(error)
    }
  },

  handleError(error) {
    const statusCode = error.response ? error.response.status : 500
    const data = error.response ? error.response.data : {}

    if (statusCode === 429) {
      this.showToastError(data.detail)
      context.error({ statusCode, message: 'Throttle Error', data })
    } else if (statusCode === 500) {
      context.error({ statusCode, message: 'API Error', data })
    } else {
      throw error
    }
  },

  showToastSuccess(message) {
    if (context.app.$toast) context.app.$toast.success(message)
  },
  showToastError(message) {
    if (context.app.$toast) context.app.$toast.error(message)
  },
  translate(value) {
    return context.app.i18n.t(value)
  },

  buildUrl(url) {
    return `/api/v1/${this.trimLeadingSlash(url)}`
  },
  trimLeadingSlash(url) {
    return url.replace(/(^\/)/g, '')
  },
})
