import BaseAPI from './base'

import CatalogAPI from './modules/catalog'
import BasketAPI from './modules/basket'
import CheckoutAPI from './modules/checkout'
import PagesAPI from './modules/pages'
import UserAPI from './modules/user'
import ConfigAPI from './modules/config'
import OrdersAPI from './modules/orders'

export default (context) => {
  const api = BaseAPI(context)

  return {
    ...api,
    catalog: CatalogAPI(api),
    basket: BasketAPI(api),
    checkout: CheckoutAPI(api),
    pages: PagesAPI(api),
    user: UserAPI(api),
    config: ConfigAPI(api),
    orders: OrdersAPI(api),
  }
}
