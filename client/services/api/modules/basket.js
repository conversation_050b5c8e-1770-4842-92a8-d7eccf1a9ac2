export default (api) => ({
  async getBasket() {
    return await api.get('basket/')
  },
  async clearBasket() {
    return await api.post('basket/clear/?basket')
  },
  async addToBasket(data) {
    return await api.post('basket/?basket', data)
  },
  async removeFromBasket(itemRef) {
    return await api.delete(`basket/${itemRef}/?basket`)
  },
  async updateExtra(data) {
    return await api.put('basket/extra/?basket', { extra: data })
  },
})
