export default (api) => ({
  async getOrders() {
    return await api.get('orders/')
  },
  async getOrder(ref, token) {
    const params = { token }
    return await api.get(`orders/${ref}/`, { params })
  },
  async getLastOrder(token) {
    const params = { token }
    return await api.get(`orders/last/`, { params })
  },
  async payOrder(ref, data, token) {
    const params = { token }
    return await api.post(`orders/${ref}/pay/`, data, { params })
  },
})
