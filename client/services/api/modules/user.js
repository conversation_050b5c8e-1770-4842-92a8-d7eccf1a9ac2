export default (api) => ({
  async getMe() {
    return await api.get('user/me/')
  },
  async updateMe(data) {
    return await api.put('user/me/', data)
  },
  async changePassword(data) {
    return await api.post('user/change-password/', data)
  },
  async logout(data) {
    return await api.post('user/logout/', data)
  },
  async login(data) {
    return await api.post('user/login/', data)
  },
  async refresh(data) {
    return await api.post('user/refresh/', data)
  },
  async register(data) {
    return await api.post('user/register/', data)
  },
  async activate(data) {
    return await api.post('user/activate/', data)
  },
  async resetPassword(data) {
    return await api.post('user/reset-password/', data)
  },
  async resetPasswordConfirm(data) {
    return await api.post('user/reset-password-confirm/', data)
  },
})
