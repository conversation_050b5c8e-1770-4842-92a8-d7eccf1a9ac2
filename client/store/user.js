import jwtDecode from 'jwt-decode'

export const state = () => ({
  fetched: false,
})

export const mutations = {
  setFetched(state, value) {
    state.fetched = value
  },
}

export const actions = {
  async setUserToken({ dispatch }, data) {
    if (data.access && data.refresh) {
      this.$auth.strategy.token.set(data.access)
      this.$auth.strategy.refreshToken.set(data.refresh)
      await dispatch('setUserFromToken')
    }
  },

  async setUserFromToken() {
    const token = this.$auth.strategy.token.get()
    if (token) {
      const decoded = jwtDecode(token)
      // Initialize user address as null
      decoded.address = null
      await this.$auth.setUser(decoded)
    }
  },

  async setUser(context, data) {
    const user = { ...this.$auth.user, ...data }
    await this.$auth.setUser(user)
  },

  async fetchUser({ state, dispatch, commit }) {
    if (!state.fetched) {
      try {
        const response = await this.$api.user.getMe()
        await dispatch('setUser', response.data)
        commit('setFetched', true)
      } catch (error) {
        commit('setFetched', false)
      }
    }
  },

  async updateUser({ dispatch }, data) {
    try {
      const response = await this.$api.user.updateMe(data)
      await dispatch('setUserToken', response.data)
      await dispatch('setUser', response.data)
    } catch (error) {}
  },
}
