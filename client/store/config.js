export const state = () => ({
  config: null,
  siteName: '',
})

export const mutations = {
  setConfig(state, configData) {
    state.config = configData
    state.siteName = configData.site_name
    state.rootUrl = configData.root_url
  },
}

export const actions = {
  async getConfig({ commit }) {
    try {
      const response = await this.$api.config.getConfig()
      commit('setConfig', response.data)
    } catch (error) {}
  },
}
