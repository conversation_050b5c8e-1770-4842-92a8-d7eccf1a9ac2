export const state = () => ({
  page: null,

  // extra
  pageKey: '',
  pageType: null,
  pageTitle: null,

  demoTokenIsEnabled: false,
  isLoading: false,
})

export const mutations = {
  setPage(state, page) {
    state.page = page
  },
  setExtra(state, data) {
    state.pageKey = String(data.key)
    state.pageType = data.type
    state.pageTitle = data.title
  },

  setDemoTokenStatus(state, value) {
    state.demoTokenIsEnabled = value
  },

  setIsLoading(state, value) {
    state.isLoading = value
  },
}

export const actions = {
  setPage({ commit }, page) {
    commit('setPage', page)
    commit('setExtra', {
      key: page.id,
      type: page.type,
      title: page.search_title,
    })
  },

  async fetchPageByPath({ dispatch }, urlPath) {
    const { data } = await this.$api.pages.findPage(urlPath)
    dispatch('setPage', data)
  },

  async refreshPage({ state, dispatch }) {
    if (state.page) {
      await dispatch('fetchPageByPath', state.page.url_path)
    }
  },

  setDemoTokenStatus({ commit }, value) {
    commit('setDemoTokenStatus', value)
  },
}
