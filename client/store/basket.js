export const state = () => ({
  basket: null,
  reservationStart: null, // Timestamp when discount/basket reservation started
})

export const mutations = {
  setBasket(state, basket) {
    state.basket = basket
    // Set reservation start time only when basket has a valid discount code (no error)
    if (
      basket &&
      basket.extra?.discount_code &&
      !basket.extra?.discount_error
    ) {
      if (!state.reservationStart) {
        state.reservationStart = Date.now()
      }
    } else {
      state.reservationStart = null
    }
  },
  setReservationStart(state, timestamp) {
    state.reservationStart = timestamp
  },
  clearReservation(state) {
    state.reservationStart = null
  },
}

export const actions = {
  async getBasket({ commit }) {
    const { data } = await this.$api.basket.getBasket()
    commit('setBasket', data)
  },

  async initBasket({ state, dispatch }) {
    if (!state.basket) await dispatch('getBasket')
  },

  async refreshBasket({ state, dispatch }) {
    if (state.basket) await dispatch('getBasket')
  },

  async addProductToBasket({ commit }, productVariant) {
    try {
      const { data } = await this.$api.basket.addToBasket({
        product_type: productVariant.product_type,
        product_id: productVariant.id,
      })
      commit('setBasket', data)

      // Show success notification
      if (this.$toast) {
        this.$toast.success(this.app.i18n.t('cart.added'))
      }

      return { success: true, data }
    } catch (error) {
      // Handle API errors
      let errorMessage = this.app.i18n.t('cart.addError.generic')

      if (error.response && error.response.data) {
        const errorData = error.response.data

        // Handle specific error messages
        if (
          errorData.non_field_errors &&
          errorData.non_field_errors.length > 0
        ) {
          const apiError = errorData.non_field_errors[0]

          if (apiError === 'Product is not available.') {
            errorMessage = this.app.i18n.t('cart.addError.notAvailable')
          } else if (
            apiError.includes('stock') ||
            apiError.includes('inventory')
          ) {
            errorMessage = this.app.i18n.t('cart.addError.outOfStock')
          } else {
            errorMessage = apiError
          }
        }
      }

      // Show error notification
      if (this.$toast) {
        this.$toast.error(errorMessage)
      }

      return { success: false, error: errorMessage }
    }
  },

  async removeProductFromBasket({ commit }, item) {
    const { data } = await this.$api.basket.removeFromBasket(item.ref)
    commit('setBasket', data)
  },

  async updateBasketExtra({ commit }, extra) {
    const { data } = await this.$api.basket.updateExtra(extra)
    commit('setBasket', data)
  },

  // Handle reservation expiration
  handleReservationExpired({ commit, dispatch }) {
    commit('clearReservation')
    // Refresh basket to get updated state from server
    return dispatch('refreshBasket')
  },

  // Start a new reservation timer
  startReservation({ commit }) {
    commit('setReservationStart', Date.now())
  },

  // Clear reservation
  clearReservation({ commit }) {
    commit('clearReservation')
  },
}
