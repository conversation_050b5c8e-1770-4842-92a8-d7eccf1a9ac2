export const state = () => ({
  cartOpen: false,
  menuOpen: false,
  homepageView: '3d',
})

export const mutations = {
  setCartOpen(state, value) {
    state.cartOpen = value
  },
  setMenuOpen(state, value) {
    state.menuOpen = value
  },
  setHomepageView(state, view) {
    state.homepageView = view
  },
}

export const actions = {
  // Action to change the view and save it to a cookie
  changeHomepageView({ commit }, view) {
    this.$cookies.set('homepage-view', view, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
    })
    commit('setHomepageView', view)
  },

  // Action to load the view from the cookie on startup
  initializeHomepageView({ commit }) {
    const savedView = this.$cookies.get('homepage-view')
    if (savedView && ['3d', 'grid', 'isometric'].includes(savedView)) {
      commit('setHomepageView', savedView)
    }
  },
}
