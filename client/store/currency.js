export const state = () => ({
  currency: 'EUR',
  currencyCookie: 'currency',
  currencies: {
    EUR: '€',
  },
})

export const mutations = {
  setCurrency(state, value) {
    state.currency = value
  },
  setCurrencies(state, value) {
    state.currencies = value
  },
}

export const actions = {
  initCurrencies({ commit, dispatch }, currencies) {
    if (currencies) {
      commit('setCurrencies', currencies)
      dispatch('setCurrencyFromCookie')
    }
  },

  setCurrency({ state, commit }, currency) {
    this.$cookies.set(state.currencyCookie, currency, {
      path: '/',
      maxAge: 60 * 60 * 24 * 30 * 6, // 6 months
    })
    commit('setCurrency', currency)
  },

  setCurrencyFromCookie({ state, dispatch }) {
    let currency = this.$cookies.get(state.currencyCookie)
    if (!currency || !state.currencies[currency]) {
      currency = 'EUR'
    }
    dispatch('setCurrency', currency)
  },
}
