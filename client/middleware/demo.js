export default async ({ app, route, redirect, store }) => {
  // Redirect user to demo page if no token
  // Run on both server and client to prevent back button bypass
  if (process.env.DEMO_TOKEN) {
    let token = app.$cookies.get('demotoken')

    if (route.query.demotoken) {
      token = route.query.demotoken
      app.$cookies.set('demotoken', token, {
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      })
    }

    const isValidToken = String(token) === String(process.env.DEMO_TOKEN)

    if (!isValidToken) {
      await store.dispatch('page/setDemoTokenStatus', true)
      // Define routes that DO NOT require a token (your whitelist)
      const whitelistedPaths = [
        '/es/auth',
        '/en/auth',
        '/demo',
        '/es/account',
        '/en/account',
      ]

      // Check if the current route path starts with any of the whitelisted paths.
      const isWhitelisted = whitelistedPaths.some((path) =>
        route.path.startsWith(path)
      )

      // If the current route is NOT in the whitelist, redirect to the demo page.
      if (!isWhitelisted) {
        // Add cache-busting headers to prevent back button access
        if (process.client) {
          // Set headers to prevent caching on client side
          if (
            typeof window !== 'undefined' &&
            window.history &&
            window.history.replaceState
          ) {
            // Replace current history state to prevent back button access
            window.history.replaceState(null, '', route.fullPath)

            // Clear any cached pages
            if (window.location && window.location.reload) {
              // Force a hard reload to clear cache
              setTimeout(() => {
                window.location.replace('/demo/')
              }, 100)
              return
            }
          }
        }
        return redirect('/demo/')
      }
    } else {
      // Valid token - ensure demo status is cleared
      await store.dispatch('page/setDemoTokenStatus', false)
    }
  }
}
