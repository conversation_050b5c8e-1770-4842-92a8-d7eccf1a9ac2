export default {
  // Environment variables
  env: {
    API_URL: process.env.API_URL || 'https://www.madkat.store',
  },

  // Runtime config for client-side access
  publicRuntimeConfig: {
    apiUrl: process.env.API_URL || 'https://www.madkat.store',
  },

  head: {
    title: 'MadKat',
    htmlAttrs: {
      lang: 'en',
    },
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: 'MadKat' },
      { name: 'format-detection', content: 'telephone=no' },
      {
        'http-equiv': 'Content-Security-Policy',
        content: 'upgrade-insecure-requests',
      },
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      {
        rel: 'icon',
        type: 'image/png',
        href: '/favicon-16x16.png',
        sizes: '16x16',
      },
      {
        rel: 'icon',
        type: 'image/png',
        href: '/favicon-32x32.png',
        sizes: '32x32',
      },
      { rel: 'author', href: 'https://www.brightdock.com/' },
    ],
  },

  styleResources: {
    scss: '~/assets/scss/_resources.scss',
    hoistUseStatements: true,
  },
  css: ['normalize.css/normalize.css', '@/assets/scss/main.scss'],

  plugins: [
    { src: '~/plugins/i18n.js' },
    { src: '~/plugins/api.js' },
    { src: '~/plugins/vee-validate.js' },
    { src: '~/plugins/vue-touch-events.js' },
    // { src: '~/plugins/vue-zoom-on-hover.js' },
    { src: '~/plugins/vue-lazyload.js', ssr: false },
    { src: '~/plugins/demo-security.client.js', mode: 'client' },
    { src: '~/plugins/payment-recovery.js', mode: 'client' },
    { src: '~/plugins/cookieConsent.js' },
  ],

  components: [{ path: '~/components', pathPrefix: false }],

  buildModules: [
    '@nuxtjs/eslint-module',
    '@nuxtjs/style-resources',
    '@nuxtjs/google-analytics',
    '@nuxtjs/device',
    // '@nuxtjs/pwa',
  ],

  modules: [
    '@nuxtjs/axios',
    '@nuxtjs/auth-next',
    '@nuxtjs/toast',
    '@nuxtjs/dayjs',
    'nuxt-webfontloader',
    'cookie-universal-nuxt',
    [
      '@nuxtjs/i18n',
      {
        baseUrl: process.env.BASE_URL || '',
        locales: [
          { code: 'en', iso: 'en', name_local: 'English', file: 'en.json' },
          { code: 'es', iso: 'es', name_local: 'Español', file: 'es.json' },
        ],
        lazy: true,
        langDir: 'lang/',
        defaultLocale: 'es',
        strategy: 'prefix',
        seo: false, // use $nuxtI18nHead method in layout instead
      },
    ],
  ],

  googleAnalytics: {
    id: 'UA-219165524-2',
  },

  toast: {
    position: 'bottom-center',
    duration: 2500,
    theme: 'bubble',
  },

  dayjs: {
    locales: ['en', 'es'],
    defaultLocale: 'es',
    plugins: [
      'relativeTime', // import 'dayjs/plugin/relativeTime'
    ],
  },

  auth: {
    strategies: {
      local: {
        scheme: 'refresh',
        token: {
          property: 'access',
          maxAge: 1800, // 30 min
          global: true,
        },
        refreshToken: {
          property: 'refresh',
          data: 'refresh',
          maxAge: 60 * 60 * 24, // 1 day
        },
        user: {
          property: '',
          autoFetch: false,
        },
        endpoints: {
          login: { url: '/api/v1/user/login/', method: 'post' },
          refresh: { url: '/api/v1/user/refresh/', method: 'post' },
          logout: false,
          user: false,
        },
      },
    },
    redirect: {
      login: 'auth-login',
      logout: false,
      callback: false,
      home: false,
    },
    plugins: ['@/plugins/auth.js'],
  },

  axios: {
    proxy: true,
    credentials: true,
  },

  proxy: {
    '/api/': process.env.API_URL,
    '/media/': process.env.API_URL,
  },

  build: {
    transpile: ['vee-validate/dist/rules', 'gsap'],
  },

  webfontloader: {
    // https://github.com/Developmint/nuxt-webfontloader#adding-google-fonts-with-font-display-option
    custom: {
      urls: ['https://use.typekit.net/rsl3gdm.css'],
    },
  },

  server: {
    host: '0.0.0.0',
  },

  router: {
    middleware: ['demo'],
    trailingSlash: true,
  },

  pwa: {
    meta: {
      name: 'MadKat',
      theme_color: '#ffffff',
    },
    manifest: {
      name: 'MadKat',
      short_name: 'RUB',
    },
  },
}
