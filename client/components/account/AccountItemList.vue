<template>
  <div class="accountItems">
    <ul>
      <li>
        <AccountItemLink
          :item="{
            url: localePath('account-orders'),
            title: $t('account.myOrders.title'),
            description: $t('account.myOrders.description'),
          }"
        />
      </li>
      <li>
        <AccountItemLink
          :item="{
            url: localePath('account-personal-details'),
            title: $t('account.myDetails.title'),
            description: $t('account.myDetails.description'),
          }"
        />
      </li>
      <li>
        <AccountItemLink
          :item="{
            url: localePath('account-change-password'),
            title: $t('account.changePassword.title'),
            description: $t('account.changePassword.description'),
          }"
        />
      </li>
    </ul>
    <Button class="button--primary margin-top" @click="logout">
      {{ $t('account.logout') }}
    </Button>
  </div>
</template>

<script>
export default {
  methods: {
    async logout() {
      const data = { refresh: this.$auth.strategy.refreshToken.get() }
      await this.$api.user.logout(data)
      await this.$auth.logout()
      this.$router.push(this.localePath('index'))
    },
  },
}
</script>

<style lang="scss">
.accountItems {
  margin-top: em(45);
  margin-bottom: em(45);

  @include grid('laptop') {
    width: 33.3333%;
  }

  @include grid('desktop-large') {
    width: 25%;
  }
}
.margin-top {
  margin-top: em(28);
}
</style>
