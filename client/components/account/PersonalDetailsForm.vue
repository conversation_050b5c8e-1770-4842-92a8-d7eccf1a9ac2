<template>
  <div class="wrap">
    <Form
      ref="form"
      :action="$api.user.updateMe"
      :payload="payload"
      @success="onSuccess"
    >
      <div class="name">
        <FormField
          v-model="formData.first_name"
          vid="firstName"
          :name="$t('form.firstName')"
          rules="required"
        />
        <FormField
          v-model="formData.last_name"
          vid="lastName"
          :name="$t('form.lastName')"
          rules="required"
        />
      </div>

      <div class="address">
        <TitleH3>{{ $t('account.personalDetails.address.title') }}</TitleH3>

        <CheckoutFormAddress
          ref="addressForm"
          v-model="formData.address"
          :initial="addressInitial"
          :required="addressRequired"
        />

        <div class="controls">
          <span
            class="button--primary button--outline"
            tabindex="0"
            role="button"
            @click="clearAddress"
          >
            <ButtonLabel>
              {{ $t('account.personalDetails.clearAddress.buttonLabel') }}
            </ButtonLabel>
          </span>

          <FormButton class="button--primary">
            {{ $t('account.personalDetails.saveChanges.buttonLabel') }}
          </FormButton>
        </div>
      </div>
    </Form>
  </div>
</template>

<script>
export default {
  data: () => ({
    formData: {
      first_name: '',
      last_name: '',
      address: '',
    },
    addressInitial: '',
  }),
  computed: {
    payload() {
      return this.formData
    },
    addressRequired() {
      return this.formData.address.replace('\n', '').trim() !== ''
    },
  },
  async mounted() {
    await this.$store.dispatch('user/fetchUser')
    this.addressInitial = this.$auth.user.address || ''
    this.formData.first_name = this.$auth.user.first_name || ''
    this.formData.last_name = this.$auth.user.last_name || ''
  },
  methods: {
    clearAddress() {
      this.$refs.addressForm.clear()
    },
    async onSuccess(data) {
      this.$toast.success(data.detail)
      await this.$store.dispatch('user/updateUser', data)
    },
  },
}
</script>

<style lang="scss" scoped>
.wrap {
  margin-bottom: em(40);
}

.name {
  display: grid;
  row-gap: em(32);
}

.address {
  @include grid('laptop', $down: true) {
    margin-top: em(80);
  }
  @include grid('laptop') {
    margin-top: em(120);
  }
}

.controls {
  margin-top: em(60);
  display: grid;
  row-gap: em(32);
}
</style>
