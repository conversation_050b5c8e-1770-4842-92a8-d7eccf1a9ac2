<template>
  <NLink :to="localePath(item.url)" class="accountItemLink">
    <article>
      <h1 class="accountItemLink__title">{{ item.title }}</h1>
      <p class="accountItemLink__description">{{ item.description }}</p>
    </article>

    <HR :margins="32" />
  </NLink>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.accountItemLink__title {
  font-size: em(21);
  font-weight: var(--text-bold);
  margin-bottom: em(8, 21);
}
.accountItemLink__description {
  font-size: em(14);
}
</style>
