<template>
  <article class="accountOrdersItem">
    <div class="accountOrdersItem__info-rows">
      <div class="accountOrdersItem__row">
        <div class="accountOrdersItem__row-item">
          <h1 class="accountOrdersItem__code">
            {{ $t('account.ordersList.code.title') }} {{ item.ref }}
          </h1>
          <p>{{ formattedDate }}</p>
        </div>
        <div class="accountOrdersItem__row-item isTextAlignRight">
          <p class="accountOrdersItem__price">{{ item.total }}</p>
        </div>
      </div>

      <div class="accountOrdersItem__row">
        <div class="accountOrdersItem__row-item">
          <p>{{ $t('account.ordersList.status.title') }}</p>
          <span class="status-aligner">
            <span class="accountOrdersItem__price">
              {{ item.status_display }}
            </span>

            <Icon :size="11">
              <OrderStatusIndicator :order="item" />
            </Icon>
          </span>
        </div>
        <div class="accountOrdersItem__row-item isTextAlignRight">
          <NLink
            :to="
              localePath({
                name: 'account-orders-id',
                params: { id: item.ref },
              })
            "
            class="button--primary button--small"
          >
            <ButtonLabel>
              {{ $t('account.ordersList.buttonLabel') }}
            </ButtonLabel>
          </NLink>
        </div>
      </div>
    </div>
  </article>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  computed: {
    formattedDate() {
      const date = this.$dayjs(this.item.date_updated)
      const detailedDate =
        this.$dayjs(date).format('dddd') +
        ', ' +
        this.$dayjs(date).format('DD/MM/YYYY') +
        ', ' +
        this.$t('account.ordersList.date.at') +
        ' ' +
        this.$dayjs(date).format('HH:mm')
      return detailedDate
    },
  },
}
</script>

<style lang="scss" scoped>
.accountOrdersItem__info-rows {
  display: grid;
  row-gap: em(20);
}
.accountOrdersItem__row {
  display: grid;
  grid-auto-flow: column;
  column-gap: em(20);
}
.accountOrdersItem__row-item {
  &.isTextAlignRight {
    text-align: right;
  }
}

.accountOrdersItem__code {
  font-size: em(16);
  font-weight: var(--text-bold);
}
.accountOrdersItem__price {
  font-weight: var(--text-bold);
}

.status-aligner {
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto 1fr;
  column-gap: em(8);
  align-items: center;
}
</style>
