<template>
  <div class="accountOrders">
    <ul v-if="result.length" class="accountOrders__list">
      <li
        v-for="(item, index) in result"
        :key="`${item + index}`"
        class="accountOrders__list-item"
      >
        <AccountOrdersItem :item="item" class="item" />
      </li>
    </ul>

    <div v-else>
      <p>{{ $t('generic.noResults') }}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    items: {
      type: Object,
      required: true,
    },
  },
  computed: {
    result() {
      return this.items.results || []
    },
  },
}
</script>

<style lang="scss" scoped>
.accountOrders {
  margin-top: em(30);
  margin-bottom: em(22);
}
.accountOrders__list {
  @include grid('laptop') {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: em(240);
    row-gap: em(22);
  }
}
.item {
  font-size: em(14);
  position: relative;
  padding-bottom: em(36);

  li + li & {
    @include grid('laptop', $down: true) {
      padding-top: em(36);
    }
  }

  &:before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    border-bottom: 1px solid var(--page-font-color);
    opacity: 0.15;
  }
}
</style>
