<template>
  <transition name="fade-slide">
    <div v-if="countdown" class="homeBannerCountdown">
      <span
        v-if="banner.countdown_bg_image"
        class="bg"
        :style="{ backgroundImage: `url(${banner.countdown_bg_image.url})` }"
      ></span>
      <p
        class="text"
        :style="{
          color: countdownColor,
          backgroundColor: countdownBgColor,
        }"
      >
        <span class="text1">{{ banner.countdown_text1 }}</span>
        <span v-if="countdown" class="text-timer">
          {{ countdown.days }}d {{ countdown.hours }}h {{ countdown.minutes }}m
          {{ countdown.seconds }}s
        </span>
        <span class="text2">{{ banner.countdown_text2 }}</span>
      </p>
    </div>
  </transition>
</template>

<script>
export default {
  props: {
    banner: { type: Object, default: () => ({}) },
  },
  data: () => ({
    countdown: undefined,
    countdownDate: null,
    countdownInterval: null,
  }),
  computed: {
    countdownColor() {
      return this.banner.countdown_text_color
        ? this.banner.countdown_text_color
        : '#000000'
    },
    countdownBgColor() {
      if (this.banner.countdown_bg_image) return null
      return this.banner.countdown_bg_color
        ? this.banner.countdown_bg_color
        : '#ffffff'
    },
  },
  mounted() {
    this.countdownDate = new Date(this.banner.countdown_to).getTime()
    this.updateCountdown()
    this.countdownInterval = setInterval(this.updateCountdown, 1000)
  },
  methods: {
    updateCountdown() {
      const now = new Date().getTime()
      const distance = this.countdownDate - now
      const days = Math.floor(distance / (1000 * 60 * 60 * 24))
      const hours = Math.floor(
        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((distance % (1000 * 60)) / 1000)

      if (distance < 0) {
        clearTimeout(this.countdownInterval)
        this.$set(this, 'countdown', null)
        this.$emit('done')
      } else {
        this.$set(this, 'countdown', {
          days,
          hours,
          minutes,
          seconds,
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.homeBannerCountdown {
  position: relative;
}

.bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
}

.text {
  position: relative;
  padding: em(18) em(18) em(16) em(18);
  text-align: center;

  @include grid('laptop', $down: true) {
    font-size: em(14);
    font-weight: var(--title-medium);
  }

  @include grid('laptop') {
    font-size: em(18);
    font-weight: var(--title-medium);
  }
}

.text-timer {
  font-weight: bold;
  margin: 0 em(8);
}
</style>
