<template>
  <div v-if="bannerVisible" class="homeBanner" :class="`homeBanner--${size}`">
    <transition name="fade-slide">
      <div v-if="loaded" class="wrapper">
        <HomePageBannerCountdown :banner="banner" @done="handleCountdownDone" />
        <nuxt-link v-if="!isExternalLink" :to="bannerLink">
          <div v-if="banner.image" class="image">
            <img :src="banner.image.url" alt="Banner" />
            <div v-if="banner.title" class="collectionListItem__info">
              <div class="collectionListItem__inner">
                <div class="collectionListItem__padding">
                  <h1 class="collectionListItem__name">{{ banner.title }}</h1>
                </div>
              </div>
            </div>
          </div>
        </nuxt-link>

        <a v-else :href="bannerLink" target="_blank" rel="noopener noreferrer">
          <div v-if="banner.image" class="image">
            <img :src="banner.image.url" alt="Banner" />
            <div v-if="banner.title" class="collectionListItem__info">
              <div class="collectionListItem__inner">
                <div class="collectionListItem__padding">
                  <h1 class="collectionListItem__name">{{ banner.title }}</h1>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    banner: { type: Object, default: () => ({}) },
    // ✅ New 'size' prop added
    size: {
      type: String,
      default: 'large', // Can be 'large' or 'small'
    },
  },
  data: () => ({
    loaded: false,
    countdownDone: false,
  }),
  computed: {
    isExternalLink() {
      return this.banner?.external_link
    },

    bannerLink() {
      if (this.banner?.external_link) {
        return this.banner.external_link
      }
      return this.banner.link ? this.banner.link : '#'
    },
    bannerVisible() {
      // if there is a banner image show it
      if (this.banner.image) {
        return this.banner.image
      }

      if (this.banner.hide_on_countdown_done) {
        return !this.countdownDone
      }
      return false
    },
  },
  mounted() {
    setTimeout(() => {
      this.loaded = true
    }, 10)
  },
  methods: {
    handleCountdownDone() {
      this.countdownDone = true
      this.$emit('countdown-done')
    },
  },
}
</script>

<style lang="scss" scoped>
// ✅ New style rule to handle the small size
.homeBanner--small {
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
}

.image {
  position: relative;
}

img {
  width: 100%;
  min-height: 140px;
}

.collectionListItem__info {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.collectionListItem__inner {
  display: table;
  width: auto;
  margin: auto;
  padding: em(30) em(30) em(28) em(30);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: var(--page-bg-color);
    opacity: 0.8;
  }
}
.collectionListItem__padding {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.collectionListItem__name {
  font-family: var(--font-family-title);
  line-height: var(--title-line-height);

  @include grid('laptop', $down: true) {
    font-size: em(18);
    font-weight: var(--title-medium);
  }

  @include grid('laptop') {
    font-size: em(26);
    font-weight: var(--title-medium);
  }
}
</style>
