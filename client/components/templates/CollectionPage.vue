<template>
  <div id="page-collection">
    <!-- Collection Grid Layout - extends to viewport edges -->
    <div v-if="items.length" class="collection-grid-container">
      <!-- Collection Title Header - sticky with blur effect -->
      <div
        class="collection-title-header"
        :class="{ 'is-scrolled': isScrolled }"
      >
        <div v-if="imageTitle" class="image-title">
          <img :src="imageTitle" alt="" />
        </div>
        <div v-else class="text-title">
          <h1 class="collection-title">{{ page.title }}</h1>
          <p v-if="page.caption" class="collection-caption">
            {{ page.caption }}
          </p>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="collection-products-grid">
        <ul class="product-grid-list" :class="`template-${pageLayout}`">
          <li
            v-for="(product, index) in items"
            :key="product.id + '-' + index"
            class="product-grid-item"
          >
            <NLink :to="product.url_path" class="product-grid-link">
              <article class="product-grid-article">
                <div
                  class="product-image-section"
                  @mouseenter.stop="startImageCycle(product.id, index)"
                  @mouseleave.stop="stopImageCycle(product.id, index)"
                >
                  <TransparentImage
                    v-if="product.images && product.images.length > 0"
                    :src="getProductImageUrl(product, index)"
                    :alt="product.title"
                    class="product-image"
                  />
                  <ImageWrapper v-else image="undefined" ratio-size="615x718" />
                </div>
                <div class="product-info-section">
                  <h2 class="product-title">{{ product.title }}</h2>
                  <div class="product-price">
                    <span v-if="hasOldPrice(product)" class="old-price">{{
                      product.old_price
                    }}</span>
                    <span class="current-price">{{ product.price }}</span>
                  </div>
                </div>
              </article>
            </NLink>
          </li>
        </ul>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMoreItems" class="load-more-section">
        <FetchingButton
          class="button--primary"
          :async-action="loadMoreItems"
          :action-success="loadMoreItemsSuccess"
          :action-error="loadMoreItemsError"
        >
          {{ $t('generic.loadMore') }}
        </FetchingButton>
      </div>
    </div>

    <!-- No Results -->
    <LayoutContainer v-if="!items.length">
      <div class="no-results">
        <div v-if="imageTitle" class="image-title">
          <img :src="imageTitle" alt="" />
        </div>
        <Header
          v-else
          :title="page.title"
          :is-viewport-height="false"
          :is-text-centered="true"
        >
          {{ page.caption }}
        </Header>
        <p>{{ $t('generic.noResults') }}</p>
      </div>
    </LayoutContainer>

    <!-- Related Collections -->
    <LayoutContainer v-if="relatedProducts.length">
      <MoreCollections
        :title="$t('collectionPage.relatedProducts.title')"
        :items="relatedProducts"
      />
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  data: () => ({
    loadedItems: [],
    loadedItemsPage: 1,
    perPage: 9,
    hasMoreItems: false,
    isScrolled: false,
    imageIntervals: {},
    currentImageIndexes: {},
  }),
  computed: {
    items() {
      return [...this.page.products.results, ...this.loadedItems]
    },
    page() {
      return this.$store.state.page.page
    },
    pageLayout() {
      return this.page.template
    },
    relatedProducts() {
      return this.page.related_products || []
    },

    imageTitle() {
      const x = this.page?.title_as_image?.url ?? null
      const y = this.page?.title_mobile_as_image?.url ?? null
      if (!x && !y) return null

      if (this.$device.isMobile) {
        return y
      }
      return x
    },
  },

  mounted() {
    this.hasMoreItems = this.page.products.next !== null

    // Add scroll listener for sticky header blur effect
    window.addEventListener('scroll', this.handleScroll)

    // Initialize image indexes for all products
    this.initializeImageIndexes()
  },

  beforeDestroy() {
    // Clean up scroll listener and intervals
    window.removeEventListener('scroll', this.handleScroll)
    Object.values(this.imageIntervals).forEach((interval) => {
      clearInterval(interval)
    })
  },
  methods: {
    hasOldPrice(product) {
      return product?.old_price && !product.old_price.startsWith('0.00')
    },
    async loadMoreItems() {
      const response = await this.$api.catalog.getProducts({
        collection: this.page.slug,
        offset: this.loadedItemsPage * this.perPage,
      })
      return response.data
    },
    loadMoreItemsSuccess(response) {
      this.loadedItems = [...this.loadedItems, ...response.results]
      this.loadedItemsPage += 1
      this.hasMoreItems = response.next !== null

      // Initialize image indexes for newly loaded items
      response.results.forEach((product, index) => {
        if (product && product.id) {
          // Create unique key using product ID and index (offset by existing items)
          const uniqueKey = `${product.id}-${
            this.page.products.results.length +
            this.loadedItems.length -
            response.results.length +
            index
          }`
          this.$set(this.currentImageIndexes, uniqueKey, 0)
        }
      })
    },
    loadMoreItemsError(error) {
      // handle error
      console.log(error)
    },

    // Initialize image indexes for products
    initializeImageIndexes() {
      this.items.forEach((product, index) => {
        if (product && product.id) {
          // Create unique key using product ID and index to prevent aliases from interfering
          const uniqueKey = `${product.id}-${index}`
          this.$set(this.currentImageIndexes, uniqueKey, 0)
        }
      })
    },

    // Scroll handling for sticky header blur effect
    handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      // Check if we've scrolled past the collection title's original position
      const collectionHeaderElement = document.querySelector(
        '.collection-title-header'
      )
      if (collectionHeaderElement) {
        const headerRect = collectionHeaderElement.getBoundingClientRect()
        const siteHeaderHeight = 60 // Adjust based on your site header height
        this.isScrolled = headerRect.top <= siteHeaderHeight
      } else {
        // Fallback
        this.isScrolled = scrollTop > 200
      }
    },

    // Image cycling functionality
    getProductImageUrl(product, index) {
      if (!product.images || product.images.length === 0) {
        return ''
      }
      // Create unique key using product ID and index to prevent aliases from interfering
      const uniqueKey = `${product.id}-${index}`
      const currentIndex = this.currentImageIndexes[uniqueKey] || 0
      const currentImage = product.images[currentIndex] || product.images[0]
      return currentImage.url || ''
    },

    startImageCycle(productId, index) {
      // Create unique key using product ID and index to prevent aliases from interfering
      const uniqueKey = `${productId}-${index}`

      // Prevent multiple intervals for the same product instance
      if (this.imageIntervals[uniqueKey]) {
        return // Already cycling
      }

      const product = this.items.find((p) => p.id === productId)

      if (!product || !product.images || product.images.length <= 1) {
        return
      }

      // Start cycling through images
      this.imageIntervals[uniqueKey] = setInterval(() => {
        const currentIndex = this.currentImageIndexes[uniqueKey] || 0
        const nextIndex = (currentIndex + 1) % product.images.length
        this.$set(this.currentImageIndexes, uniqueKey, nextIndex)
      }, 2000) // Change image every 2 seconds
    },

    stopImageCycle(productId, index) {
      // Create unique key using product ID and index to prevent aliases from interfering
      const uniqueKey = `${productId}-${index}`

      if (this.imageIntervals[uniqueKey]) {
        clearInterval(this.imageIntervals[uniqueKey])
        delete this.imageIntervals[uniqueKey]
      }

      // Reset to first image
      this.$set(this.currentImageIndexes, uniqueKey, 0)
    },
  },
}
</script>

<style lang="scss" scoped>
/* Collection Grid Container - Full Width Checkerboard */
.collection-grid-container {
  width: 100vw;
  margin-left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;

  // Add top padding to prevent header overlap
  padding-top: em(120); // Space for header + notification ticker

  @include grid('laptop', $down: true) {
    padding-top: em(100); // Smaller padding on mobile
  }
}

/* Collection Title Header - Sticky */
.collection-title-header {
  position: sticky;
  top: 5em; // More space for site header
  z-index: 50; // Lower than site header but above content
  border: 1px solid var(--page-font-color);
  border-left: none;
  border-right: none;
  padding: 2rem;
  text-align: center;
  background-color: var(--page-bg-color);

  @include grid('laptop', $down: true) {
    padding: 1.5rem;
    top: 4em; // More space on mobile too
  }
}

/* Products Grid - Blur Effect When Scrolled */
.collection-products-grid {
  transition: filter 0.3s ease;

  .is-scrolled & {
    filter: blur(2px);
    opacity: 0.7;
  }
}

.text-title {
  .collection-title {
    font-size: 2rem;
    font-weight: var(--text-bold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
    line-height: 1.2;

    @include grid('laptop', $down: true) {
      font-size: 1.5rem;
    }
  }

  .collection-caption {
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
    opacity: 0.8;
  }
}

.image-title {
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    max-width: 100%;
    height: auto;
  }
}

/* Products Grid - Checkerboard Pattern */
.collection-products-grid {
  width: 100%;
}

.product-grid-list {
  display: grid;
  gap: 0;

  // Dynamic columns based on template
  &.template-1-columns {
    grid-template-columns: 1fr;
  }

  &.template-2-columns {
    grid-template-columns: repeat(2, 1fr);
  }

  &.template-3-columns {
    @include grid('laptop', $down: true) {
      grid-template-columns: repeat(2, 1fr); // 2 columns on mobile
    }
    @include grid('laptop') {
      grid-template-columns: repeat(3, 1fr); // 3 columns on desktop
    }
  }

  &.template-4-columns {
    @include grid('laptop', $down: true) {
      grid-template-columns: repeat(2, 1fr); // 2 columns on mobile
    }
    @include grid('laptop') {
      grid-template-columns: repeat(4, 1fr); // 4 columns on desktop
    }
  }
}

// Product Grid Items
.product-grid-item {
  border: 1px solid var(--page-font-color);
  border-top: none;
  border-left: none;

  // First row gets top border
  .template-1-columns &:nth-child(1),
  .template-2-columns &:nth-child(-n + 2),
  .template-3-columns &:nth-child(-n + 2),
  .template-4-columns &:nth-child(-n + 2) {
    @include grid('laptop', $down: true) {
      border-top: 1px solid var(--page-font-color);
    }
  }

  .template-3-columns &:nth-child(-n + 3),
  .template-4-columns &:nth-child(-n + 4) {
    @include grid('laptop') {
      border-top: 1px solid var(--page-font-color);
    }
  }

  // First column gets left border
  .template-1-columns &:nth-child(1n),
  .template-2-columns &:nth-child(2n-1),
  .template-3-columns &:nth-child(2n-1),
  .template-4-columns &:nth-child(2n-1) {
    @include grid('laptop', $down: true) {
      border-left: 1px solid var(--page-font-color);
    }
  }

  .template-3-columns &:nth-child(3n-2),
  .template-4-columns &:nth-child(4n-3) {
    @include grid('laptop') {
      border-left: 1px solid var(--page-font-color);
    }
  }
}

// Product Grid Links
.product-grid-link {
  display: block;
  text-align: center;
  color: inherit;
  text-decoration: none;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Product Grid Article
.product-grid-article {
  display: flex;
  flex-direction: column;
  height: 100%;

  // Fix for ImageWrapper to maintain consistent height
  .image-wrapper {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Product Sections
.product-image-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  cursor: pointer;
  overflow: hidden;

  // Set consistent height based on column count
  .template-1-columns & {
    height: 60vh; // Reasonable height for single column
    max-height: 600px;
  }

  .template-2-columns & {
    height: 50vh; // Smaller for 2 columns
    max-height: 500px;
  }

  .template-3-columns &,
  .template-4-columns & {
    height: 40vh; // Standard height for 3-4 columns
    max-height: 400px;

    @include grid('laptop', $down: true) {
      height: 35vh; // Smaller on mobile
      max-height: 350px;
    }
  }

  @include grid('laptop', $down: true) {
    padding: 1rem;
  }

  // Smooth image transitions
  .product-image {
    transition: all 0.3s ease-in-out;
    width: 100%;
    height: 100%;
    object-fit: contain; // Maintain aspect ratio without cropping
    display: block;
  }

  // Hover effect
  &:hover {
    .product-image {
      filter: brightness(1.05);
    }
  }
}

.product-info-section {
  padding: 1rem 2rem 2rem 2rem;
  border-top: 1px solid var(--page-font-color);
  background-color: var(--page-bg-color);
  min-height: 80px; // Consistent height to prevent jumping
  display: flex;
  flex-direction: column;
  justify-content: center;

  @include grid('laptop', $down: true) {
    padding: 0.75rem 1rem 1rem 1rem;
    min-height: 70px;
  }
}

.product-title {
  font-size: 1rem;
  font-weight: var(--text-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
  line-height: 1.2;
}

.product-price {
  margin-top: 0.5rem;
  font-size: 0.9rem;

  .old-price {
    text-decoration: line-through;
    opacity: 0.6;
    margin-right: 0.5rem;
  }

  .current-price {
    font-weight: var(--text-bold);
  }
}

/* Load More Section */
.load-more-section {
  border: 1px solid var(--page-font-color);
  border-top: none;
  border-left: none;
  border-right: none;
  padding: 2rem;
  text-align: center;
  background-color: var(--page-bg-color);

  @include grid('laptop', $down: true) {
    padding: 1.5rem;
  }
}

/* No Results */
.no-results {
  text-align: center;
  padding: 2rem;

  .image-title {
    margin-bottom: 2rem;
  }

  p {
    font-size: 1.2rem;
    opacity: 0.7;
  }
}
</style>
