<template>
  <div id="page-product">
    <LayoutContainer>
      <div class="productHeader">
        <div class="productGalleryWrapper">
          <ProductGallery
            v-if="page"
            :product="page"
            :selected-variant="selectedVariant"
          />
        </div>

        <div class="productDetailsWrapper">
          <ProductDetails
            v-if="page"
            v-model="selectedVariant"
            :product="page"
          />
        </div>
      </div>

      <MoreCollections
        v-if="page && relatedProducts.length"
        :title="labels.similar"
        :items="relatedProducts"
      />
    </LayoutContainer>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  data() {
    return {
      selectedVariant: null,
    }
  },
  computed: {
    ...mapState('page', ['page']),

    productPrice() {
      return this.page && this.page.variants && this.page.variants.length
        ? this.page.variants[0].price
        : 'Variant not set'
    },
    labels() {
      return this.page ? this.page.labels : {}
    },
    productDetails() {
      return this.page ? this.page.details : ''
    },
    relatedProducts() {
      return (this.page && this.page.related_products) || []
    },
  },
}
</script>

<style lang="scss">
.productHeader {
  @include grid('laptop') {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Equal columns */
    grid-gap: var(--layout-gap, 2rem);
    align-items: start; /* Align items to the top */
  }
}

.productGalleryWrapper {
  @include grid('laptop') {
    position: sticky;
    top: 0;
    height: 100vh;
    /* Add some padding if your header is transparent or fixed */
    padding-top: 100px; /* Adjust this value to match your header height */
  }
}

.productDetailsWrapper {
  @include grid('laptop') {
    /* Ensures this column can scroll independently */
    padding-top: 100px; /* Adjust to align with the gallery */
    padding-bottom: 5rem;
  }
}
</style>
