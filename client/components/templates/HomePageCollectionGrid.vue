<template>
  <div class="collection-grid-container">
    <!-- Row 1: Banner spanning middle 3 columns -->
    <div
      :class="{ 'banner-cell--hidden': !isBannerVisible }"
      class="banner-cell"
      style="grid-area: 1 / 2 / 2 / 5"
    >
      <HomePageBanner
        v-if="isBannerVisible"
        :banner="page.banner"
        size="small"
        @countdown-done="handleCountdownDone"
      />
    </div>

    <!-- Row 2: Collection 1 and Collection 2 images -->
    <div
      v-if="collections[0]"
      class="collection-image-cell collection-1-image"
      style="grid-area: 2 / 2 / 3 / 3"
    >
      <NLink :to="localePath(collections[0].url_path)" class="collection-link">
        <ImageWrapper
          v-if="collections[0].featured_image"
          :image="collections[0].featured_image.large"
          :image-tablet="collections[0].featured_image.small"
          :image-mobile="collections[0].featured_image.medium"
          ratio-size="480x550"
        />
        <ImageWrapper v-else image="undefined" ratio-size="480x550" />
      </NLink>
    </div>

    <div
      v-if="collections[1]"
      class="collection-image-cell collection-2-image"
      style="grid-area: 2 / 4 / 3 / 5"
    >
      <NLink :to="localePath(collections[1].url_path)" class="collection-link">
        <ImageWrapper
          v-if="collections[1].featured_image"
          :image="collections[1].featured_image.large"
          :image-tablet="collections[1].featured_image.small"
          :image-mobile="collections[1].featured_image.medium"
          ratio-size="480x550"
        />
        <ImageWrapper v-else image="undefined" ratio-size="480x550" />
      </NLink>
    </div>

    <!-- Row 3: Collection 1 and Collection 2 names with empty border cells -->
    <div class="empty-border-cell" style="grid-area: 3 / 1 / 4 / 2"></div>

    <div
      v-if="collections[0]"
      class="collection-name-cell collection-1-name"
      style="grid-area: 3 / 2 / 4 / 3"
    >
      <NLink
        :to="localePath(collections[0].url_path)"
        class="collection-name-link"
      >
        <h2 class="collection-name">{{ collections[0].title }}</h2>
        <p v-if="collections[0].caption" class="collection-caption">
          {{ collections[0].caption }}
        </p>
      </NLink>
    </div>

    <div class="empty-border-cell" style="grid-area: 3 / 3 / 4 / 4"></div>

    <div
      v-if="collections[1]"
      class="collection-name-cell collection-2-name"
      style="grid-area: 3 / 4 / 4 / 5"
    >
      <NLink
        :to="localePath(collections[1].url_path)"
        class="collection-name-link"
      >
        <h2 class="collection-name">{{ collections[1].title }}</h2>
        <p v-if="collections[1].caption" class="collection-caption">
          {{ collections[1].caption }}
        </p>
      </NLink>
    </div>

    <div class="empty-border-cell" style="grid-area: 3 / 5 / 4 / 6"></div>

    <!-- Row 4: Collection 3 and Collection 4 images -->
    <div
      v-if="collections[2]"
      class="collection-image-cell collection-3-image"
      style="grid-area: 4 / 2 / 5 / 3"
    >
      <NLink :to="localePath(collections[2].url_path)" class="collection-link">
        <ImageWrapper
          v-if="collections[2].featured_image"
          :image="collections[2].featured_image.large"
          :image-tablet="collections[2].featured_image.small"
          :image-mobile="collections[2].featured_image.medium"
          ratio-size="480x550"
        />
        <ImageWrapper v-else image="undefined" ratio-size="480x550" />
      </NLink>
    </div>

    <div
      v-if="collections[3]"
      class="collection-image-cell collection-4-image"
      style="grid-area: 4 / 4 / 5 / 5"
    >
      <NLink :to="localePath(collections[3].url_path)" class="collection-link">
        <ImageWrapper
          v-if="collections[3].featured_image"
          :image="collections[3].featured_image.large"
          :image-tablet="collections[3].featured_image.small"
          :image-mobile="collections[3].featured_image.medium"
          ratio-size="480x550"
        />
        <ImageWrapper v-else image="undefined" ratio-size="480x550" />
      </NLink>
    </div>

    <!-- Row 5: Collection 3 and Collection 4 names with empty border cells -->
    <div class="empty-border-cell" style="grid-area: 5 / 1 / 6 / 2"></div>

    <div
      v-if="collections[2]"
      class="collection-name-cell collection-3-name"
      style="grid-area: 5 / 2 / 6 / 3"
    >
      <NLink
        :to="localePath(collections[2].url_path)"
        class="collection-name-link"
      >
        <h2 class="collection-name">{{ collections[2].title }}</h2>
        <p v-if="collections[2].caption" class="collection-caption">
          {{ collections[2].caption }}
        </p>
      </NLink>
    </div>

    <div class="empty-border-cell" style="grid-area: 5 / 3 / 6 / 4"></div>

    <div
      v-if="collections[3]"
      class="collection-name-cell collection-4-name"
      style="grid-area: 5 / 4 / 6 / 5"
    >
      <NLink
        :to="localePath(collections[3].url_path)"
        class="collection-name-link"
      >
        <h2 class="collection-name">{{ collections[3].title }}</h2>
        <p v-if="collections[3].caption" class="collection-caption">
          {{ collections[3].caption }}
        </p>
      </NLink>
    </div>

    <div class="empty-border-cell" style="grid-area: 5 / 5 / 6 / 6"></div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'HomePageCollectionGrid',
  props: {
    collections: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      countdownDone: false,
    }
  },
  computed: {
    ...mapState('page', ['page']),
    isBannerVisible() {
      // Replicate the exact same logic as HomePageBanner.vue bannerVisible computed
      if (!this.page || !this.page.banner) {
        return false
      }

      const banner = this.page.banner

      // If there is a banner image, show it
      if (banner.image) {
        return true
      }

      // If hide_on_countdown_done is true, show banner until countdown is done
      if (banner.hide_on_countdown_done) {
        return !this.countdownDone
      }

      return false
    },
  },
  methods: {
    handleCountdownDone() {
      this.countdownDone = true
    },
  },
}
</script>

<style lang="scss" scoped>
.collection-grid-container {
  display: grid;
  width: 100%;
  grid-template-columns: 10% 1fr 10% 1fr 10%;
  grid-template-rows: auto 1fr auto 1fr auto;
  gap: 0;
  flex: 1;
}

.banner-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border: 1px solid var(--page-font-color);
}

.empty-border-cell {
  border: 1px solid var(--page-font-color);
  border-left: none;
  border-right: none;
}

.collection-image-cell {
  border: 1px solid var(--page-font-color);
  border-bottom: none;
  border-top: none;
  padding: 2rem;

  @include grid('laptop', $down: true) {
    padding: 1.5rem;
  }
}

.collection-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

.collection-name-cell {
  border: 1px solid var(--page-font-color);
  padding: 1.5rem 2rem;
  text-align: center;

  @include grid('laptop', $down: true) {
    padding: 1rem 1.5rem;
  }
}

.collection-name-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

.collection-name {
  font-family: var(--font-family-title);
  line-height: var(--title-line-height);
  text-transform: uppercase;
  color: var(--black);
  margin: 0;

  @include grid('laptop', $down: true) {
    font-size: em(18);
    font-weight: var(--title-medium);
  }

  @include grid('laptop') {
    font-size: em(22);
    font-weight: var(--title-medium);
  }
}

.collection-caption {
  font-size: em(14);
  margin-top: em(8);
  color: var(--black);
  margin-bottom: 0;
}

/* Mobile responsive layout */
@media (max-width: 768px) {
  .collection-grid-container {
    display: flex;
    flex-direction: column;
    height: auto;
    gap: 0;
  }

  .banner-cell {
    order: 1;
    padding: 1.5rem;
    border: 1px solid var(--page-font-color);
    border-left: none;
    border-right: none;
  }

  .collection-image-cell {
    padding: 1.5rem;
    border: 1px solid var(--page-font-color);
    border-left: none;
    border-right: none;
    border-bottom: none;
  }

  .collection-name-cell {
    padding: 1rem 1.5rem;
    border: 1px solid var(--page-font-color);
    border-left: none;
    border-right: none;
    border-top: none;
    text-align: center;
  }

  .empty-border-cell {
    display: none;
  }

  /* Order collections properly for mobile */
  .collection-1-image {
    order: 2; /* Collection 1 image */
  }

  .collection-1-name {
    order: 3; /* Collection 1 name */
  }

  .collection-2-image {
    order: 4; /* Collection 2 image */
  }

  .collection-2-name {
    order: 5; /* Collection 2 name */
  }

  .collection-3-image {
    order: 6; /* Collection 3 image */
  }

  .collection-3-name {
    order: 7; /* Collection 3 name */
  }

  .collection-4-image {
    order: 8; /* Collection 4 image */
  }

  .collection-4-name {
    order: 9; /* Collection 4 name */
  }
}

/* Tablet responsive layout */
@media (min-width: 769px) and (max-width: 1024px) {
  .collection-grid-container {
    grid-template-columns: 5% 1fr 5% 1fr 5%;
    padding: 0 1rem;
  }

  .banner-cell {
    padding: 1.5rem;
  }

  .collection-image-cell {
    padding: 1.5rem;
  }

  .collection-name-cell {
    padding: 1rem 1.5rem;
  }
}

.banner-cell--hidden {
  display: none;
}
</style>
