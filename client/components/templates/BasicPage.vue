<template>
  <div id="page-basic" class="basic">
    <slot>
      <LayoutContainer>
        <ContentContainer>
          <Header :title="pageTitle" :is-viewport-height="false"> </Header>
        </ContentContainer>

        <div class="basicBody">
          <ContentContainer>
            <div class="basicBody__block">
              <Block
                v-for="block in $store.state.page.page.body"
                :key="block.id"
                :block="block"
              />
            </div>
          </ContentContainer>
        </div>
      </LayoutContainer>
    </slot>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    title: { type: String, default: '' },
  },
  computed: {
    ...mapState('page', ['page']),
    pageTitle() {
      return this.title || this.$store.state.page.pageTitle
    },
  },
}
</script>

<style lang="scss">
.basicBody__block > *:first-child {
  @include grid('laptop', $down: true) {
    margin-top: rem(32);
  }

  @include grid('laptop') {
    margin-top: rem(48);
  }
}

.basicBody__block > *:last-child {
  @include grid('laptop', $down: true) {
    margin-bottom: rem(48);
  }

  @include grid('laptop') {
    margin-bottom: rem(80);
  }
}

.basicBody__block {
  a {
    border-bottom: 1px dashed;
    transition: border-bottom var(--speed) var(--easing),
      color var(--speed) var(--easing);

    &:focus-visible,
    &:hover {
      color: gray;
    }
  }

  strong {
    font-weight: var(--text-bold);
  }

  h2 {
    font-size: em(21);
    font-weight: var(--text-bold);
    margin-top: em(60, 21);
    margin-bottom: em(25, 21);
  }

  h3 {
    font-size: em(16);
    font-weight: var(--text-bold);
    margin-top: em(24, 16);
    margin-bottom: em(21, 16);
  }

  ol,
  ul,
  p {
    font-size: rem(14);
    margin-bottom: em(21, 14);
  }

  ol,
  ul {
    padding-left: em(40, 14);

    // have margin-top when lists are nested

    & > *:first-child {
      @include grid('laptop', $down: true) {
        margin-top: em(21, 14);
      }

      @include grid('laptop') {
        margin-top: em(21, 14);
      }
    }
  }

  & > ol {
    padding-left: em(40, 14);
  }

  li {
    margin-bottom: em(21, 14);
  }

  ul {
    padding-left: em(12, 14);
  }

  ul {
    & > li {
      position: relative;
      &:before {
        content: '';
        background-color: var(--page-font-color);
        position: absolute;
        top: em(10, 14);
        left: em(-10, 14);
        width: em(4);
        height: em(4);
        transform: rotate(45deg);
      }
    }
  }

  // neat list indetation and counter reset

  ol > li {
    display: table;
    position: relative;
  }

  ol {
    counter-reset: item;
    list-style: decimal;
  }

  ol li:before {
    content: counters(item, '.') '. ';
    counter-increment: item;
    display: table-cell;
    position: absolute;
    top: auto;
    left: em(-40, 14);
  }
}
</style>
