<template>
  <div class="home-page-grid">
    <ul class="product-grid-list">
      <li
        v-for="(product, index) in products"
        :key="product.id + '-' + index"
        class="product-grid-item"
      >
        <NLink :to="product.url_path" class="product-grid-link">
          <article class="product-grid-article">
            <TransparentImage
              v-if="product.images && product.images.length > 0"
              :src="product.images[0].url"
              :alt="product.title"
            />
            <!-- <ImageWrapper
              v-if="product.images && product.images.length > 0"
              :image="product.images[0]"
              ratio-size="615x718"
              class="product-image"
              :transparent="true"
            /> -->
            <ImageWrapper v-else image="undefined" ratio-size="615x718" />
            <h2 class="product-title">{{ product.title }}</h2>
          </article>
        </NLink>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'HomePageGrid',
  props: {
    products: {
      type: Array,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.home-page-grid {
  // Use a negative margin to allow the border to touch the viewport edges
  margin: 0 -2px;
}

.product-grid-list {
  display: grid;
  // No gap, borders will create the lines
  // border: 2px solid var(--page-font-color);
  // border-left: 2px solid var(--page-font-color);

  // Mobile: 2 columns
  @include grid('laptop', $down: true) {
    grid-template-columns: repeat(2, 1fr);
    // Remove right border on the last item in each row
    & > .product-grid-item:nth-child(2n) {
      border-right: none;
    }
  }

  // Desktop: 4 columns
  @include grid('laptop') {
    grid-template-columns: repeat(4, 1fr);
    // Remove right border on the last item in each row
    & > .product-grid-item:nth-child(4n) {
      border-right: none;
    }
  }
}

.product-grid-item {
  // Add borders to the right and bottom of each item
  border-right: 2px solid var(--page-font-color);
  border-bottom: 2px solid var(--page-font-color);
}

.product-grid-link {
  display: block;
  text-align: center;
  color: inherit;
  text-decoration: none;
  // Add padding inside the link to create space around the content
  padding: 2rem;

  @include grid('laptop', $down: true) {
    padding: 1rem;
  }
}

.product-image {
  margin-bottom: 1rem;
}

.product-title {
  font-size: 1rem;
  font-weight: var(--text-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding-top: 1rem;
}
</style>
