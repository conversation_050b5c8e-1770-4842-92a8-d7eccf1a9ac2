<template>
  <div id="page-home" :class="{ 'no-padding': homepageView === 'grid' }">
    <ViewToggle />

    <div v-if="homepageView === '3d'">
      <img
        ref="animatedLogo"
        src="/images/logo-black.png"
        alt="Site Logo"
        class="animated-logo"
      />
      <div
        v-if="isBannerVisible"
        ref="animatedBanner"
        class="animated-banner"
        :class="{ 'is-sticky': isBannerSticky }"
      >
        <HomePageBanner :banner="page.banner" />
        <button
          v-if="isBannerSticky"
          class="dismiss-button"
          aria-label="Dismiss Banner"
          @click.stop="dismissBanner"
        >
          <Icon name="IconMenuClose" />
        </button>
      </div>
      <ProductFlow3D
        v-if="products.length"
        :products="flowItems"
        :hidden-item-ids="hiddenItemIds"
        @item-approaching="handleItemApproaching"
      />
    </div>

    <LayoutContainer v-else-if="homepageView === 'grid'" :grid="true">
      <ContentContainer>
        <HomePageBanner :banner="page.banner" size="small" class="homeBanner" />
      </ContentContainer>

      <HomePageCollectionGrid :collections="homepageCollections" />
    </LayoutContainer>

    <HomePageIsometric
      v-else-if="homepageView === 'isometric'"
      :products="products"
    />
  </div>
</template>

<script>
import { gsap } from 'gsap'
import { mapState } from 'vuex'

export default {
  props: {
    products: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      hiddenItemIds: [],
      isBannerSticky: false,
      isBannerVisible: true,
    }
  },

  computed: {
    ...mapState('page', ['page']),
    ...mapState('ui', ['homepageView']),
    homepageCollections() {
      return this.page.collections || []
    },

    flowItems() {
      if (this.homepageView !== '3d') {
        return this.products
      }
      const logoItem = {
        id: 'logo-item',
        type: 'logo',
        title: 'Brand Logo',
        price: '',
        images: [{ url: '/images/logo-black.png' }],
        url_path: '#',
      }
      const bannerItem = {
        id: 'banner-item',
        type: 'banner',
        title: this.page.banner?.title || 'Special Offer',
        price: '',
        images: [this.page.banner?.image || { url: '/images/banner3.png' }],
        url_path: this.page.banner?.link || '#',
      }
      return [logoItem, bannerItem, ...this.products]
    },
  },
  methods: {
    dismissBanner() {
      this.isBannerVisible = false
    },
    handleItemApproaching(product) {
      if (
        product.type === 'logo' &&
        !this.hiddenItemIds.includes('logo-item')
      ) {
        this.hiddenItemIds.push('logo-item')
        const logoEl = this.$refs.animatedLogo
        gsap
          .timeline()
          .to(logoEl, {
            autoAlpha: 1,
            scale: 1,
            duration: 0.5,
            ease: 'power2.out',
          })
          .to(logoEl, {
            y: '-150vh',
            autoAlpha: 0,
            duration: 1,
            ease: 'power2.in',
            delay: 0.5,
          })
      } else if (
        product.type === 'banner' &&
        !this.hiddenItemIds.includes('banner-item')
      ) {
        this.hiddenItemIds.push('banner-item')
        const bannerEl = this.$refs.animatedBanner
        gsap.to(bannerEl, {
          autoAlpha: 1,
          y: 0,
          duration: 0.8,
          ease: 'power3.out',
          onComplete: () => {
            this.isBannerSticky = true
          },
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
#page-home {
  padding-top: em(115);

  &.no-padding {
    padding-top: 0;
  }
}

.homeBanner {
  margin-bottom: em(42);
}

.animated-logo {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  z-index: 20;
  opacity: 0;
  visibility: hidden;
  width: 150px;
  pointer-events: none;
}

.animated-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 15;
  transform: translateY(100%);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;

  &.is-sticky {
    cursor: pointer;
  }

  @include grid('laptop') {
    left: 50%;
    width: 100%;
    max-width: 450px;
    transform: translate(-50%, 100%);
  }
}

.dismiss-button {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 16;
  background: rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 50%;
  color: white;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px;

  &:hover {
    background: rgba(0, 0, 0, 0.6);
  }
}

.isometric-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  font-size: 2rem;
  color: var(--gray-b3);
}
</style>
