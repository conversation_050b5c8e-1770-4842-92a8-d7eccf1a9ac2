<template>
  <div class="contentContainer" :class="{ 'is-small': isSmall }">
    <slot />
  </div>
</template>

<script>
export default {
  props: {
    isSmall: { type: Boolean, default: false },
  },
}
</script>

<style lang="scss">
.contentContainer {
  margin-left: auto;
  margin-right: auto;
  transition: max-width var(--speed-fast) var(--easing);

  &.is-small {
    @include grid('laptop') {
      max-width: $container-medium;
    }
    @include grid('desktop-large') {
      max-width: $container-large;
    }
    @include grid('desktop-huge') {
      max-width: $container-max;
    }
  }

  &:not(.is-small) {
    @include grid('tablet') {
      max-width: $container-medium;
    }
    @include grid('laptop') {
      max-width: $container-large;
    }
    @include grid('desktop-large') {
      max-width: $container-huge;
    }
    @include grid('desktop-huge') {
      max-width: $container-max;
    }
  }
}
</style>
