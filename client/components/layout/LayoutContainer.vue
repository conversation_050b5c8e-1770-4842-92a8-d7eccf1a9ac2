<template>
  <div
    class="layoutContainer"
    :class="{ hasTrackOnMobile: hasTrackOnMobile, grid: grid }"
  >
    <slot />
  </div>
</template>

<script>
export default {
  props: {
    hasTrackOnMobile: {
      type: Boolean,
      default: false,
    },
    grid: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="scss">
.layoutContainer {
  margin-left: auto;
  margin-right: auto;

  @include grid('laptop', $down: true) {
    padding-left: em(26);
    padding-right: em(26);
  }
  @include grid('laptop') {
    padding-left: em(54);
    padding-right: em(54);
  }
  @include grid('desktop-large') {
    padding-left: em(96);
    padding-right: em(96);
  }

  &.hasTrackOnMobile {
    @include grid('laptop', $down: true) {
      padding-left: em(0);
      padding-right: em(0);
      margin-left: em(-26);
      margin-right: em(-26);
    }
    @include grid('laptop') {
      padding-left: em(0);
      padding-right: em(0);
    }
    @include grid('desktop-large') {
      padding-left: em(0);
      padding-right: em(0);
    }
  }

  &.grid {
    padding: 0;
  }
}
</style>
