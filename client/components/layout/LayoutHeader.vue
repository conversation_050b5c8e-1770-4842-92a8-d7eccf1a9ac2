<template>
  <header
    class="layoutHeader"
    :class="{ 'border-bottom': showBorder, 'demo-hidden': demoTokenIsEnabled }"
  >
    <LayoutBannerProductDraft v-if="isPreviewMode" :live-at="page.live_at" />

    <div v-if="!demoTokenIsEnabled" class="layoutHeader__inner">
      <LayoutContainer>
        <Navbar />
      </LayoutContainer>
    </div>

    <div class="layoutHeader__alert">
      <Transition name="alert" mode="out-in">
        <div
          v-if="!$device.isDesktop && $store.state.ui.cartOpen"
          key="alertDisplayed"
        >
          <!-- <Alert
            :is-contained="true"
            :title="'header alert title here'"
            :message="'10% discount on your 1st order. Terms & Conditions apply.'"
          >
            <Icon name="IconUser" />
          </Alert> -->
        </div>
      </Transition>
    </div>
  </header>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data: () => ({
    alertDisplayed: false,
  }),
  computed: {
    ...mapState('ui', ['homepageView']),
    ...mapState('page', ['page', 'pageType', 'demoTokenIsEnabled']),
    page() {
      return this.$store.state.page.page
    },
    isPreviewMode() {
      if (this.page && this.$route.query.preview_token) {
        return this.page.has_unpublished_changes || !this.page.live
      }
      return false
    },
    showBorder() {
      // Only show the border on the HomePage when the view is not '3d'
      return this.pageType === 'home.HomePage' && this.homepageView !== '3d'
    },
  },
}
</script>

<style lang="scss">
.alert-enter-active,
.alert-leave-active {
  transition: opacity var(--speed) var(--easing),
    transform calc(var(--speed) * 2) var(--easing);
}
.alert-enter,
.alert-leave-active {
  transform: translateY(-100%);
  opacity: 0;
}

.layoutHeader {
  position: fixed;
  top: 0; // Default: at the very top when no notifications
  left: 0;
  width: 100%;
  z-index: 10;
  transition: top 0.3s ease; // Smooth transition when notifications appear/disappear

  &.border-bottom {
    border-bottom: 1px solid var(--page-font-color);
  }

  &.demo-hidden {
    display: none;
  }
}

.layoutHeader__inner {
  background-color: var(--page-bg-color);
}
.layoutHeader__alert {
  position: relative;
  z-index: -1;
}
</style>

<style lang="scss">
/* Global styles for notification-based header positioning */
body.has-notifications .layoutHeader {
  top: 3em !important; /* Push down to make room for notification ticker */
}

@media (max-width: 1024px) {
  body.has-notifications .layoutHeader {
    top: 2.5em !important; /* Smaller offset on mobile */
  }
}
</style>
