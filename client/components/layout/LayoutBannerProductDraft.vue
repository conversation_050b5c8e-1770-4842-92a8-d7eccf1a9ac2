<template>
  <transition name="fade" mode="out-in">
    <div key="draftBannerDisplayed" class="draft-banner">
      <span class="draft-banner-label">
        {{ $t('productPage.banner.draft') }}
        <span v-if="timeUntilPublished">
          - {{ $t('productPage.banner.draft.publicIn') }}
          {{ timeUntilPublished }}
        </span>
      </span>
    </div>
  </transition>
</template>

<script>
export default {
  props: {
    liveAt: {
      type: String,
      default: null,
    },
  },
  computed: {
    timeUntilPublished() {
      if (this.liveAt) {
        const date = this.$dayjs(this.liveAt)
        const now = this.$dayjs()
        if (now.isBefore(date)) {
          return now.to(date)
        }
      }
      return null
    },
  },
}
</script>

<style lang="scss" scoped>
.draft-banner {
  background-color: var(--black);
  color: var(--white);
  padding: em(8, 14) em(16, 14);
  font-size: em(14);
  text-align: center;

  @include grid('laptop') {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }
}

.draft-banner-label {
  display: block;
  margin-top: em(1, 14);
}
</style>
