<template>
  <div class="layoutBody">
    <slot />
  </div>
</template>

<script>
export default {
  props: {
    title: { type: String, default: '' },
  },
}
</script>

<style lang="scss">
.layoutBody {
  flex: 1;
  transition: padding-top 0.3s ease; // Smooth transition when notifications appear/disappear

  // Default: Just navbar height when header is at top (0px position + 32px navbar = 32px)
  padding-top: em(32) !important; // Minimal space - just navbar height

  @include grid('laptop', $down: true) {
    padding-top: em(32) !important; // Same on mobile when header at top
  }
}

// When notifications are present: full padding
body.has-notifications .layoutBody {
  padding-top: em(
    140
  ) !important; // Space for notification ticker + repositioned header

  @include grid('laptop', $down: true) {
    padding-top: em(120) !important; // Smaller spacing on mobile
  }
}
</style>
