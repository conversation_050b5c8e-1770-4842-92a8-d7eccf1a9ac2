<template>
  <div class="switchCurrency">
    <span class="switchCurrency__current">
      <span class="switchCurrency__label">{{ $t('mainNav.currency') }}</span>
      <span class="relative">
        <Button class="switchCurrency__selected" @click="toggleMenu">
          {{ currency }}
        </Button>
        <ul class="availableCurrencies__list" :class="{ show: showCurrencies }">
          <li
            v-for="item in currencyCodes"
            :key="item"
            class="availableCurrencies__item"
          >
            <Button
              :class="{ selected: item === currency }"
              class="availableCurrencies__item-button"
              @click="setCurrency(item)"
            >
              {{ item }}
            </Button>
          </li>
        </ul>
      </span>
    </span>
  </div>
</template>

<script>
export default {
  data: () => ({
    showCurrencies: false,
  }),
  computed: {
    config() {
      return this.$store.state.config.config
    },
    currencies() {
      return this.$store.state.currency.currencies
    },
    currencyCodes() {
      return Object.keys(this.currencies)
    },
    currency() {
      return this.$store.state.currency.currency
    },
  },
  mounted() {
    window.addEventListener('click', (e) => {
      this.showCurrencies = false
    })
  },
  methods: {
    toggleMenu(event) {
      event.stopPropagation()
      this.showCurrencies = !this.showCurrencies
    },
    setCurrency(currency) {
      this.$store.dispatch('currency/setCurrency', currency)
      this.showCurrencies = false
    },
  },
}
</script>

<style lang="scss" scoped>
.switchCurrency,
.switchCurrency a {
  font-size: em(14);
}
.switchCurrency__label {
  margin-right: em(4);
}
.switchCurrency__selected {
  font-weight: var(--text-bold);
}
.relative {
  position: relative;
}
.availableCurrencies__list {
  position: absolute;
  left: 0;
  bottom: 100%;
  background-color: var(--page-bg-color);
  width: 100%;
  opacity: 0;
  pointer-events: none;
  transform: translateY(em(8));
  transition: opacity var(--speed) var(--easing),
    transform var(--speed) var(--easing);

  &.show {
    opacity: 1;
    transform: none;
    pointer-events: auto;
  }
}
.availableCurrencies__item-button {
  padding: em(8);
  margin: 0 em(-8);
  &.selected {
    font-weight: var(--text-bold);
  }
  &:hover {
    text-decoration: underline;
  }
}
</style>
