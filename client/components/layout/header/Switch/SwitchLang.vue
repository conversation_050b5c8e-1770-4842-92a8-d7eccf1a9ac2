<template>
  <div class="switchLang">
    <ul class="switchLang__available">
      <li v-for="locale in availableLocales" :key="locale.code">
        <NLink
          v-if="isTranslationMissing"
          :to="localePath('index', locale.code)"
        >
          {{ locale.name_local }}
        </NLink>
        <NLink v-else :to="switchLocalePath(locale.code)">
          {{ locale.name_local }}
        </NLink>
      </li>
    </ul>

    <Separator />

    <span class="switchLang__selected">
      {{ currentLocale.name_local }}
    </span>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  computed: {
    ...mapState('page', ['page']),

    isTranslationMissing() {
      const isMissing = {}
      if (this.page && this.page.translated_urls !== undefined) {
        isMissing.value = Object.keys(this.page.translated_urls).length === 0
      }
      return isMissing.value
    },
    availableLocales() {
      return this.$i18n.locales.filter((i) => i.code !== this.$i18n.locale)
    },
    currentLocale() {
      const currentLocaleObj = this.$i18n.locales.filter(
        (i) => i.code === this.$i18n.locale
      )[0]
      return currentLocaleObj
    },
  },
}
</script>

<style lang="scss" scoped>
.switchLang {
  display: flex;
  align-items: center;
}
.switchLang__available,
.switchLang__selected {
  font-size: em(14);
}
.switchLang__selected {
  font-weight: var(--text-bold);
}
</style>
