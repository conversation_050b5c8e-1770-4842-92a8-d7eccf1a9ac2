<template>
  <div
    class="headerMenuPanelBackdrop"
    :class="{ isMobileSafari: $device.isSafari && $device.isMobile }"
    @click="$emit('click')"
  ></div>
</template>

<style lang="scss">
.headerMenuPanelBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--backdrop-color);
  opacity: 0;
  z-index: -1;

  transition: opacity calc(var(--speed) * 2) var(--easing);
  pointer-events: none;

  .menuOpen &:not(.isMobileSafari) {
    opacity: 0.5;
    pointer-events: auto;
  }
}
</style>
