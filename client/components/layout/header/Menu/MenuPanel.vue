<template>
  <div class="headerMenuPanel">
    <div class="headerMenuPanel__inner">
      <NavbarMainNav />

      <MenuPanelFooter>
        <SwitchCurrency />
        <SwitchLang />
        <MenuFooter v-if="isHomepage3dView" />
      </MenuPanelFooter>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  computed: {
    ...mapState('page', ['pageType']),
    ...mapState('ui', ['homepageView']),

    isHomepage3dView() {
      return this.pageType === 'home.HomePage' && this.homepageView === '3d'
    },
  },
}
</script>

<style lang="scss">
.headerMenuPanel {
  min-height: 100vh;
  min-height: calc((var(--vh, 1vh) * 100));

  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: var(--page-bg-color);
  padding-top: em(88);
  padding-bottom: em(30);
  z-index: -1;

  transform: translateX(-100%);
  transition: transform calc(var(--speed) * 2) var(--easing);

  @include grid('laptop', $down: true) {
    // min-width: 40vw;
    max-width: 80vw;

    margin-left: em(-26);
    margin-right: em(-26);
    padding-left: 0;
    padding-right: em(26);
  }

  @include grid('laptop') {
    max-width: 40vw;

    margin-left: em(-54);
    margin-right: em(-54);
    padding-left: 0;
    padding-right: em(54);
  }

  @include grid('desktop-large') {
    margin-left: em(-96);
    margin-right: em(-96);
    padding-left: 0;
    padding-right: em(96);
  }

  .menuOpen & {
    transform: none;
  }
}

.headerMenuPanel__inner {
  $scrollbarWidth: 4;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  overflow: hidden auto;
  padding-top: em(38);
  padding-right: em(16);

  @include grid('laptop', $down: true) {
    padding-left: em(26);
  }
  @include grid('laptop') {
    padding-left: em(54);
  }
  @include grid('desktop-large') {
    padding-left: em(96);
  }

  &::-webkit-scrollbar {
    width: em($scrollbarWidth);
    background-color: var(--scrollbar-bg-color);
    display: none;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-color);
  }
  // firefox
  scrollbar-width: thin;
}
</style>
