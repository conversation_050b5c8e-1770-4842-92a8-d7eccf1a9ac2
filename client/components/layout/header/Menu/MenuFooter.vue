<template>
  <div class="menu-footer">
    <ul v-if="footerMenu.length" class="menu-footer__pages">
      <li v-for="item in footerMenu" :key="item.id">
        <NLink :to="item.url">{{ item.title }}</NLink>
      </li>
    </ul>
    <a
      href="https://www.instagram.com/madkatstore/"
      class="menu-footer__social-link"
      target="_blank"
      rel="noopener"
    >
      <Icon name="IconSocialInstagram" />
      <span>Instagram</span>
    </a>
  </div>
</template>

<script>
export default {
  name: '<PERSON>uFooter',
  computed: {
    config() {
      return this.$store.state.config.config
    },
    footerMenu() {
      if (this.config.footer && this.config.footer.menu) {
        return this.config.footer.menu.children || []
      }
      return []
    },
  },
}
</script>

<style lang="scss" scoped>
.menu-footer {
  margin-top: em(40);
  padding-top: em(30);
  border-top: 1px solid var(--gray-b3);
}

.menu-footer__pages {
  display: grid;
  row-gap: em(16);
  font-size: em(14);
  margin-bottom: em(24);

  a:hover {
    text-decoration: underline;
  }
}

.menu-footer__social-link {
  display: inline-flex;
  align-items: center;
  font-size: em(14);
  gap: em(8);

  &:hover {
    text-decoration: underline;
  }
}
</style>
