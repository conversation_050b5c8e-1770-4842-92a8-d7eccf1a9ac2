<template>
  <div v-if="show" class="cookieMessage">
    <div class="cookieMessage__inner">
      <p class="cookieMessage__title">{{ $t('cookieMessage.title') }}</p>
      <p class="cookieMessage__text">
        {{ $t('cookieMessage.text') }}
      </p>
      <p class="cookieMessage__description">
        {{ $t('cookieMessage.description') }}
      </p>
      <p v-if="cookieLink" class="cookieMessage__text">
        <NLink :to="cookieLink.url" class="cookieMessage__link">
          {{ cookieLink.title }}
        </NLink>
      </p>
      <div class="cookieMessage__controls">
        <div class="cookieMessage__button">
          <Button
            class="button--outline button--cookie button--invert-outline"
            @click="rejectAll"
          >
            {{ $t('cookieMessage.button.rejectAll') }}
          </Button>
        </div>
        <div class="cookieMessage__button">
          <Button
            class="button--primary button--cookie button--invert"
            @click="acceptAll"
          >
            {{ $t('cookieMessage.button.acceptAll') }}
          </Button>
        </div>
      </div>
      <div class="cookieMessage__settings">
        <button
          class="cookieMessage__settingsButton"
          @click="showSettings = !showSettings"
        >
          {{ $t('cookieMessage.button.settings') }}
        </button>
      </div>
      <div v-if="showSettings" class="cookieMessage__preferences">
        <div class="cookieMessage__preferenceGroup">
          <label class="cookieMessage__preferenceLabel">
            <input
              type="checkbox"
              :checked="preferences.necessary"
              disabled
              class="cookieMessage__checkbox"
            />
            <span class="cookieMessage__preferenceTitle">{{
              $t('cookieMessage.preferences.necessary.title')
            }}</span>
          </label>
          <p class="cookieMessage__preferenceDescription">
            {{ $t('cookieMessage.preferences.necessary.description') }}
          </p>
        </div>
        <div class="cookieMessage__preferenceGroup">
          <label class="cookieMessage__preferenceLabel">
            <input
              v-model="preferences.analytics"
              type="checkbox"
              class="cookieMessage__checkbox"
            />
            <span class="cookieMessage__preferenceTitle">{{
              $t('cookieMessage.preferences.analytics.title')
            }}</span>
          </label>
          <p class="cookieMessage__preferenceDescription">
            {{ $t('cookieMessage.preferences.analytics.description') }}
          </p>
        </div>
        <div class="cookieMessage__preferenceGroup">
          <label class="cookieMessage__preferenceLabel">
            <input
              v-model="preferences.marketing"
              type="checkbox"
              class="cookieMessage__checkbox"
            />
            <span class="cookieMessage__preferenceTitle">{{
              $t('cookieMessage.preferences.marketing.title')
            }}</span>
          </label>
          <p class="cookieMessage__preferenceDescription">
            {{ $t('cookieMessage.preferences.marketing.description') }}
          </p>
        </div>
        <div class="cookieMessage__customControls">
          <Button
            class="button--outline button--cookie button--invert-outline"
            @click="savePreferences"
          >
            {{ $t('cookieMessage.button.savePreferences') }}
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: () => ({
    show: false,
    showSettings: false,
    key: 'cookielaw',
    preferencesKey: 'cookie-preferences',
    preferences: {
      necessary: true, // Always true, cannot be disabled
      analytics: false,
      marketing: false,
    },
  }),
  computed: {
    config() {
      return this.$store.state.config.config
    },
    cookieLink() {
      if (this.config) return this.config.page_links.cookie
      return null
    },
  },
  mounted() {
    setTimeout(() => {
      if (!this.$cookieConsent.hasConsent()) {
        this.loadPreferences()
        this.show = true
      }
    }, 1000)
  },
  methods: {
    acceptAll() {
      this.$cookieConsent.acceptAll()
      this.hide()
    },
    rejectAll() {
      this.$cookieConsent.rejectAll()
      this.hide()
    },
    savePreferences() {
      this.$cookieConsent.savePreferences(this.preferences)
      this.hide()
    },
    hide() {
      this.show = false
      this.showSettings = false
    },
    loadPreferences() {
      this.preferences = this.$cookieConsent.getPreferences()
    },
    // Legacy method for backward compatibility
    accept() {
      this.acceptAll()
    },
  },
}
</script>

<style lang="scss" scoped>
.cookieMessage {
  background-color: var(--cookies-bg-color);
  color: var(--cookies-font-color);
  position: fixed;
  bottom: 0;
  left: auto;
  right: 0;
  width: 100%;
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

  @include grid('tablet') {
    max-width: 75%;
  }

  @include grid('tablet-large') {
    max-width: 50%;
  }

  @include grid('laptop') {
    max-width: em(450);
  }
}

.cookieMessage__inner {
  padding: em(24) em(32) em(32);
  display: grid;
  row-gap: em(16);
}

.cookieMessage__title {
  font-weight: var(--text-bold);
  font-size: em(16);
  margin: 0;
}

.cookieMessage__text {
  font-size: em(14);
  line-height: 1.4;
  margin: 0;
}

.cookieMessage__description {
  font-size: em(13);
  line-height: 1.4;
  margin: 0;
  opacity: 0.9;
}

.cookieMessage__link {
  text-decoration: underline;

  &:hover {
    opacity: 0.8;
  }
}

.cookieMessage__controls {
  display: grid;
  gap: em(12);

  @include grid('tablet') {
    grid-auto-flow: column;
    grid-auto-columns: 1fr 1fr;
  }
}

// Button container styles handled by Button component

.cookieMessage__settings {
  text-align: center;
  margin-top: em(8);
}

.cookieMessage__settingsButton {
  background: none;
  border: none;
  color: var(--cookies-font-color);
  font-size: em(13);
  text-decoration: underline;
  cursor: pointer;
  padding: em(4) 0;

  &:hover {
    opacity: 0.8;
  }
}

.cookieMessage__preferences {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: em(20);
  margin-top: em(16);
  display: grid;
  gap: em(16);
}

.cookieMessage__preferenceGroup {
  display: grid;
  gap: em(6);
}

.cookieMessage__preferenceLabel {
  display: flex;
  align-items: flex-start;
  gap: em(10);
  cursor: pointer;
  font-size: em(14);
}

.cookieMessage__checkbox {
  margin: 0;
  flex-shrink: 0;
  margin-top: em(2);
}

.cookieMessage__preferenceTitle {
  font-weight: var(--text-medium);
  line-height: 1.3;
}

.cookieMessage__preferenceDescription {
  font-size: em(12);
  line-height: 1.4;
  margin: 0;
  opacity: 0.8;
  margin-left: em(24); // Align with checkbox content
}

.cookieMessage__customControls {
  margin-top: em(8);
  text-align: center;
}
</style>
