<template>
  <div v-if="show" class="cookieMessage">
    <div class="cookieMessage__inner">
      <p class="cookieMessage__title">{{ $t('cookieMessage.title') }}</p>
      <p class="cookieMessage__text">
        {{ $t('cookieMessage.text') }}
      </p>
      <p v-if="cookieLink" class="cookieMessage__text">
        <NLink :to="cookieLink.url" class="cookieMessage__link">
          {{ cookieLink.title }}
        </NLink>
      </p>
      <div class="cookieMessage__controls">
        <div class="cookieMessage__button">
          <Button
            class="button--primary button--cookie button--invert"
            @click="accept"
          >
            {{ $t('cookieMessage.button.ok') }}
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: () => ({
    show: false,
    key: 'cookielaw',
  }),
  computed: {
    config() {
      return this.$store.state.config.config
    },
    cookieLink() {
      if (this.config) return this.config.page_links.cookie
      return null
    },
  },
  mounted() {
    setTimeout(() => {
      if (!this.$cookies.get(this.key)) this.show = true
    }, 1000)
  },
  methods: {
    accept() {
      this.$cookies.set(this.key, {
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      })
      this.show = false
    },
  },
}
</script>

<style lang="scss" scoped>
.cookieMessage {
  background-color: var(--cookies-bg-color);
  color: var(--cookies-font-color);
  position: fixed;
  bottom: 0;
  left: auto;
  right: 0;
  width: 100%;
  z-index: 10;

  @include grid('tablet') {
    max-width: 75%;
  }

  @include grid('tablet-large') {
    max-width: 50%;
  }

  @include grid('laptop') {
    max-width: em(400);
  }
}
.cookieMessage__inner {
  padding: em(38) em(40) em(40);
  display: grid;
  row-gap: em(12);
}
.cookieMessage__title {
  font-weight: var(--text-bold);
}
.cookieMessage__text {
  font-size: em(14);
}
.cookieMessage__link {
  text-decoration: underline;
}
.cookieMessage__controls {
  display: grid;

  @include grid('tablet', $down: true) {
    row-gap: em(14);
  }
  @include grid('tablet') {
    grid-auto-flow: column;
    grid-auto-columns: 1fr 1fr;
    column-gap: em(14);
  }
}
</style>
