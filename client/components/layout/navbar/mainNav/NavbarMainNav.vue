<template>
  <nav>
    <ul class="mainNav">
      <NavbarMainNavItem :url="mainMenu.url">
        {{ mainMenu.title }}
      </NavbarMainNavItem>

      <NavbarMainNavItem
        v-for="item in children"
        :key="item.url"
        :items="item.children"
      >
      </NavbarMainNavItem>

      <NavbarMainNavItem
        v-if="!demoTokenIsEnabled"
        :url="localePath('shop-cart')"
      >
        {{ cartLabel }}
      </NavbarMainNavItem>

      <li v-if="$auth.loggedIn">
        <Button class="logout" @click="logout">
          {{ $t('mainNav.logout') }}
        </Button>
      </li>

      <NavbarMainNavItem v-if="!$auth.loggedIn" :url="localePath('auth-login')">
        {{ $t('auth.login.form.buttonLabel') }}
      </NavbarMainNavItem>

      <NavbarMainNavItem
        v-if="!$auth.loggedIn"
        :url="localePath('auth-register')"
      >
        {{ $t('auth.register.form.buttonLabel') }}
      </NavbarMainNavItem>
    </ul>
  </nav>
</template>

<script>
import { mapState } from 'vuex'

export default {
  computed: {
    ...mapState('page', ['demoTokenIsEnabled']),
    labels() {
      return this.$store.state.config.config.labels || []
    },
    mainMenu() {
      return this.$store.state.config.config.main_menu
    },
    children() {
      return this.mainMenu.children || []
    },
    cartLabel() {
      return this.labels.cart || ''
    },
  },
  methods: {
    async logout() {
      const data = { refresh: this.$auth.strategy.refreshToken.get() }
      await this.$api.user.logout(data)
      await this.$auth.logout()
      this.$router.push(this.localePath('index'))
    },
  },
}
</script>

<style lang="scss" scoped>
.mainNav {
  display: grid;
  row-gap: em(20);
}
.logout {
  font-size: em(21);
  font-weight: var(--text-bold);
  letter-spacing: -0.02em;
  text-align: left;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
