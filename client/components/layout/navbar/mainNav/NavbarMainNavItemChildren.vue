<template>
  <ul class="mainNavItemChildren">
    <li v-for="item in items" :key="item.id">
      <NLink
        :to="localePath(item.url)"
        class="mainNavItemChildren__link"
        :class="{ highlighted: item.highlighted }"
      >
        {{ item.title }}
      </NLink>
    </li>
  </ul>
</template>

<script>
export default {
  props: {
    items: { type: Array, default: () => [] },
  },
}
</script>

<style lang="scss" scoped>
.mainNavItemChildren {
  .highlighted {
    font-weight: var(--text-bold);
  }
}

.mainNavItemChildren__link {
  font-size: em(14);
  padding-top: em(4, 14);
  padding-bottom: em(4, 14);
  display: inline-flex;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover,
  &:focus-visible {
    opacity: 0.5;
  }

  &.nuxt-link-active {
    position: relative;

    &:before {
      content: '';
      position: absolute;
      bottom: em(7, 14);
      left: 0;
      width: 100%;
      height: em(2, 14);
      background-color: currentColor;
      opacity: 0.5;
    }
  }
}
</style>
