<template>
  <li class="mainNavItem">
    <NLink v-if="url.length" :to="localePath(url)" class="mainNavItem__link">
      <slot />
    </NLink>

    <NavbarMainNavItemChildren v-if="items" :items="items" />
  </li>
</template>

<script>
export default {
  props: {
    url: {
      type: String,
      default: '',
    },
    item: {
      type: Object,
      default: null,
    },
    items: {
      type: Array,
      default: null,
    },
  },
}
</script>

<style lang="scss" scoped>
.mainNavItem__link {
  font-size: em(21);
  font-weight: var(--text-bold);
  letter-spacing: -0.02em;
  display: inline-flex;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover,
  &:focus-visible {
    opacity: 0.5;
  }

  &.nuxt-link-exact-active {
    position: relative;
    &:before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: em(2, 21);
      background-color: currentColor;
      opacity: 0.5;
    }
  }
}
</style>
