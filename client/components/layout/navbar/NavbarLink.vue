<template>
  <NLink :to="localePath(url)" class="navbarLink">
    <Icon :name="icon" :size="16" />
    <span class="navbarLink__label">{{ label }}</span>
  </NLink>
</template>

<script>
export default {
  props: {
    icon: { type: String, default: '' },
    label: { type: String, default: '' },
    url: { type: String, default: '' },
  },
}
</script>

<style lang="scss">
.navbarLink__label {
  @include grid('laptop', $down: true) {
    display: none;
  }

  @include grid('laptop') {
    font-size: em(14);
    margin-left: em(14, 14);
  }
}
</style>
