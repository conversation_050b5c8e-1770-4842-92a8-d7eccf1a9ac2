<template>
  <div class="headerNavbar" :class="{ menuOpen: $store.state.ui.menuOpen }">
    <div class="headerNavbar__inner">
      <div class="headerNavbarItem__start">
        <NavbarMenuToggle aria-label="Toggle navigation" @click="toggleMenu" />
        <Logo>
          <LogoBrand />
        </Logo>
      </div>

      <div class="headerNavbarItem__end">
        <NavbarLink
          v-if="$auth.loggedIn"
          class="headerNavbarItemLink"
          icon="IconUser"
          url="account"
          :label="loggedInUser"
        />

        <NavbarLink
          v-else
          class="headerNavbarItemLink"
          icon="IconUser"
          url="account"
          :label="config.labels.account"
        />

        <NavbarLink
          v-if="!demoTokenIsEnabled"
          class="headerNavbarItemLink"
          icon="IconBag"
          url="shop-cart"
          :label="config.labels.cart"
        />
      </div>
    </div>

    <MenuPanelBackdrop v-touch:swipe="onSwipe" @click="toggleMenu" />
    <MenuPanel id="aria-menu-collapse" />

    <CartFlyout />
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  computed: {
    ...mapState('config', ['config']),
    ...mapState('page', ['demoTokenIsEnabled']),
    loggedInUser() {
      return this.$auth.user.first_name
        ? this.$auth.user.first_name
        : this.$auth.user.email
    },
  },
  watch: {
    '$route.path'() {
      this.$store.commit('ui/setMenuOpen', false)
    },
  },
  methods: {
    onSwipe(event) {
      if (event === 'left') {
        this.$store.commit('ui/setMenuOpen', false)
      }
    },
    toggleMenu() {
      this.$store.commit('ui/setMenuOpen', !this.$store.state.ui.menuOpen)
      this.$store.commit('ui/setCartOpen', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.headerNavbar {
  padding-top: em(16);
  padding-bottom: em(16);
  display: flex;
  align-items: center;
  position: relative;
  z-index: 10;
}
.headerNavbar__inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  pointer-events: none;
  position: relative;
}
.headerNavbarItem__start {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}
.headerNavbarItem__end {
  display: flex;
  align-items: center;
  margin-right: em(-16);
  pointer-events: auto;
  flex: 0 0 auto;
}
.headerNavbarItemLink {
  display: flex;
  align-items: center;
  padding: em(16);
  z-index: 1;
}
.headerNavbarNoAccount {
  display: flex;
  position: relative;

  a {
    margin-left: em(16);
  }
}
</style>
