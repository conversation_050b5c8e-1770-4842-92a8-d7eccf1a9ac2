<template>
  <Button
    class="navbarMenuToggle"
    :aria-label="ariaLabel"
    :aria-expanded="$store.state.ui.menuOpen.toString()"
    aria-controls="aria-menu-collapse"
    @click="$emit('click', $event)"
  >
    <template #icon>
      <Icon v-if="$store.state.ui.menuOpen" name="IconMenuClose" />
      <Icon v-else name="IconMenu" />
    </template>
  </Button>
</template>

<script>
// check https://bootstrap-vue.org/docs/components/navbar on small screen width to see an example of what aria should do
export default {
  props: {
    ariaLabel: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="scss">
.navbarMenuToggle {
  display: flex;
  padding: em(7);
  line-height: 1;
  margin-left: em(-9);
  pointer-events: auto;
}
</style>
