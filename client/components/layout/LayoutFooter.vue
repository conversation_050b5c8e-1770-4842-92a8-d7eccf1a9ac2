<template>
  <footer class="layoutFooter">
    <LayoutContainer>
      <div class="layoutFooter__inner">
        <p class="layoutFooter__item layoutFooter__copyright">
          {{ footerCopyright }}
        </p>
        <div v-if="footerMenu.length" class="layoutFooter__item">
          <ul class="layoutFooter__pages">
            <li v-for="item in footerMenu" :key="item.id">
              <NLink :to="item.url">{{ item.title }}</NLink>
            </li>
            <li>
              <a
                href="https://www.instagram.com/madkatstore/"
                class="layoutFooter__item__instagram"
                target="_blank"
                rel="noopener"
              >
                <Icon name="IconSocialInstagram" />
              </a>
            </li>
          </ul>
        </div>
      </div>
    </LayoutContainer>
  </footer>
</template>

<script>
export default {
  computed: {
    config() {
      return this.$store.state.config.config
    },
    footerMenu() {
      if (this.config.footer.menu) {
        return this.config.footer.menu.children || []
      }
      return []
    },
    footerCopyright() {
      return this.$store.state.config.config.footer
        ? this.$store.state.config.config.footer.copyright_text
        : 'Not available'
    },
  },
}
</script>

<style lang="scss">
.layoutFooter {
  @include grid('laptop', $down: true) {
    padding-top: em(40);
    padding-bottom: em(16);
  }

  @include grid('laptop') {
    padding-top: em(32);
    padding-bottom: em(32);
  }
}

.layoutFooter__inner {
  display: grid;
  align-items: baseline;

  @include grid('laptop', $down: true) {
    row-gap: em(36);
  }

  @include grid('laptop') {
    grid-template-columns: 1fr 3fr;
    column-gap: em(26);
  }
}
.layoutFooter__pages {
  font-size: em(12);
  display: grid;
  // row-gap: em(12);
  // column-gap: em(26, 12);

  @include grid('tablet', $down: true) {
    text-align: center;
  }

  @include grid('tablet', $to: 'tablet-large') {
    grid-template-columns: repeat(3, 1fr);
  }

  @include grid('tablet-large', $to: 'laptop') {
    grid-template-columns: repeat(5, 1fr);
  }

  @include grid('laptop') {
    grid-auto-flow: column;
    display: flex;
    justify-content: flex-end;
  }

  li {
    // handle long links
    overflow: hidden;
    text-overflow: ellipsis;
    // width: 100%;
    white-space: nowrap;
    padding: em(6) em(16);

    &:last-child {
      @include grid('laptop') {
        margin-right: 0;
      }
    }
  }
}
.layoutFooter__item__instagram {
  display: table;
  margin: auto;

  @include grid('tablet') {
    display: block;
    transform: translateY(-3px);
  }
}
.layoutFooter__copyright {
  font-size: em(12);

  @include grid('laptop', $down: true) {
    text-align: center;
    grid-row: 2;
  }
}
</style>
