<template>
  <div class="formResetPassword">
    <Form
      ref="form"
      :action="$api.user.resetPassword"
      :payload="payload"
      @success="onSuccess"
    >
      <FormField
        v-model="formData.email"
        vid="email"
        :name="$t('form.email')"
        type="email"
        rules="required|email"
      />

      <FormButton class="button--primary margin-top">
        {{ $t('auth.resetPassword.form.buttonLabel') }}
      </FormButton>
    </Form>
  </div>
</template>

<script>
export default {
  data: () => ({
    formData: {
      email: '',
      return_url: '',
    },
  }),
  computed: {
    payload() {
      return this.formData
    },
  },
  mounted() {
    this.formData.return_url =
      window.location.origin + this.localePath('auth-reset-password-confirm')
  },
  methods: {
    onSuccess(data) {
      this.$toast.success(data.detail)
      this.formData.email = ''
      this.$refs.form.reset()
      if ('activeElement' in document) document.activeElement.blur()
      if (this.$route.query.next) this.$router.push(this.$route.query.next)
    },
  },
}
</script>

<style lang="scss" scoped>
.formResetPassword {
  margin-bottom: em(40);
}
.formResetPassword form {
  display: flex;
  flex-direction: column;
  row-gap: em(32);
}

.margin-top {
  margin-top: em(28);
}
</style>
