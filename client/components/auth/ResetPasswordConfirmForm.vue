<template>
  <div class="formResetPasswordConfirm">
    <Form
      ref="form"
      :action="$api.user.resetPasswordConfirm"
      :payload="payload"
      @success="onSuccess"
      @error="onError"
    >
      <div class="formResetPasswordConfirm__fields">
        <FormField
          v-model="formData.new_password"
          vid="new_password"
          :name="$t('form.password')"
          type="password"
          rules="required|password"
        />
        <FormField
          v-model="passwordConfirm"
          vid="password_confirm"
          :name="$t('form.passwordConfirm')"
          type="password"
          rules="required|confirmed:new_password"
        />

        <FormButton class="button--primary margin-top">
          {{ $t('auth.resetPasswordConfirm.form.buttonLabel') }}
        </FormButton>
      </div>
    </Form>
  </div>
</template>

<script>
export default {
  data: () => ({
    formData: {
      uid: '',
      token: '',
      new_password: '',
    },
    passwordConfirm: '',
  }),
  computed: {
    payload() {
      const data = { ...this.formData }
      return data
    },
  },
  mounted() {
    this.formData.uid = this.$route.query.u || ''
    this.formData.token = this.$route.query.t || ''
  },
  methods: {
    onSuccess(data) {
      this.$toast.success(data.detail)
      this.reset(true)
      this.$router.push(this.localePath('auth-login'))
    },
    onError(data) {
      this.reset()

      if (data.uid || data.token) {
        this.$toast.error(this.$t('auth.resetPasswordConfirm.form.invalidUrl'))
      } else {
        this.$refs.form.setErrors(data)
        this.$toast.error(this.$t('form.correctErrors'))
      }
    },
    reset(all = false) {
      if (all) this.formData = {}
      this.passwordConfirm = ''
      this.$refs.form.reset()
      if ('activeElement' in document) document.activeElement.blur()
    },
  },
}
</script>

<style lang="scss" scoped>
.formResetPasswordConfirm {
  margin-bottom: em(40);
}
.formResetPasswordConfirm__fields {
  display: flex;
  flex-direction: column;
  row-gap: em(32);
}
.margin-top {
  margin-top: em(28);
}
</style>
