<template>
  <div class="formChangePassword">
    <Form
      ref="form"
      :action="$api.user.changePassword"
      :payload="payload"
      @success="onSuccess"
      @invalid="onInvalid"
    >
      <div class="formChangePassword__fields">
        <FormField
          v-model="formData.old_password"
          vid="old_password"
          :name="$t('form.passwordOld')"
          type="password"
          rules="required|password"
        />
        <FormField
          v-model="formData.new_password"
          vid="new_password"
          :name="$t('form.passwordNew')"
          type="password"
          rules="required|password"
        />
        <FormField
          v-model="passwordConfirm"
          vid="password-confirm"
          :name="$t('form.passwordNewConfirm')"
          type="password"
          rules="required|confirmed:new_password"
        />
        <FormButton class="button--primary margin-top">
          {{ $t('account.changePassword.form.buttonLabel') }}
        </FormButton>
      </div>
    </Form>
  </div>
</template>

<script>
export default {
  data: () => ({
    formData: {
      old_password: '',
      new_password: '',
    },
    passwordConfirm: '',
  }),
  computed: {
    payload() {
      return this.formData
    },
  },
  methods: {
    onSuccess(data) {
      this.$toast.success(data.detail)
      this.reset(true)
      this.$router.push(this.localePath('account'))
    },
    onInvalid(data) {
      this.reset()
    },
    reset(all = false) {
      this.passwordConfirm = ''
      if (all) {
        this.formData = {}
        this.$refs.form.reset()
      }
      if ('activeElement' in document) document.activeElement.blur()
    },
  },
}
</script>

<style lang="scss" scoped>
.formChangePassword {
  margin-bottom: em(40);
}
.formChangePassword__fields {
  display: flex;
  flex-direction: column;
  row-gap: em(32);
}
.margin-top {
  margin-top: em(28);
}
</style>
