<template>
  <div class="loginForm">
    <Form
      ref="form"
      :action="formAction"
      @success="onSuccess"
      @invalid="onInvalid"
    >
      <FormField
        v-model="formData.email"
        vid="email"
        :name="$t('form.email')"
        type="email"
        rules="required|email"
      />
      <FormField
        v-model="formData.password"
        vid="password"
        :name="$t('form.password')"
        type="password"
        rules="required|password"
      />

      <NLink :to="localePath('auth-reset-password')" class="link">
        {{ $t('auth.login.form.forgotPassword') }}
      </NLink>

      <FormButton class="button--primary">
        {{ $t('auth.login.form.buttonLabel') }}
      </FormButton>
    </Form>
  </div>
</template>

<script>
export default {
  data: () => ({
    formData: {
      email: '',
      password: '',
    },
  }),
  computed: {},
  methods: {
    async formAction() {
      return await this.$auth.loginWith('local', { data: this.formData })
    },
    async onSuccess(data) {
      await this.$store.dispatch('user/setUserFromToken')
      await this.$store.dispatch('basket/refreshBasket')
      this.$toast.success(data.detail)
      const nextUrl = this.$route.query.next || this.localePath('account')
      this.$router.push(nextUrl)
    },
    onInvalid(data) {
      this.formData = { ...this.formData, password: '' }
    },
  },
}
</script>

<style lang="scss" scoped>
.loginForm form {
  display: flex;
  flex-direction: column;
  row-gap: em(32);
}

.link {
  font-weight: var(--text-medium);
  margin-bottom: em(18);
}
</style>
