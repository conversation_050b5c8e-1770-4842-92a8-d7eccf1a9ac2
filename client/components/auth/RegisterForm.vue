<template>
  <div class="registerForm">
    <Form
      ref="form"
      :action="$api.user.register"
      :payload="payload"
      @success="onSuccess"
    >
      <FormField
        v-model="formData.email"
        vid="email"
        :name="$t('form.email')"
        type="email"
        rules="required|email"
      />
      <FormField
        v-model="formData.first_name"
        vid="first_name"
        :name="$t('form.firstName')"
        rules="required"
      />
      <FormField
        v-model="formData.last_name"
        vid="last_name"
        :name="$t('form.lastName')"
        rules="required"
      />
      <FormField
        v-model="formData.password"
        vid="password"
        :name="$t('form.password')"
        type="password"
        rules="required|password"
      />
      <FormField
        v-model="termsAccepted"
        vid="terms"
        type="checkbox"
        :label="false"
        :placeholder="termsLabel"
        rules="isTrue"
      />
      <FormButton class="button--primary margin-top">
        {{ $t('auth.login.extraLinks.buttonLabel') }}
      </FormButton>
    </Form>
  </div>
</template>

<script>
export default {
  data: () => ({
    formData: {
      email: '',
      first_name: '',
      last_name: '',
      password: '',
      return_url: '',
    },
    passwordConfirm: false,
    termsAccepted: false,
  }),
  computed: {
    config() {
      return this.$store.state.config.config
    },
    payload() {
      return this.formData
    },
    termsLabel() {
      let value = this.$t('form.terms.label')
      const termsLink = this.config.page_links.terms
      const termsUrl = termsLink ? termsLink.url : '#'
      const privacyLink = this.config.page_links.privacy
      const privacyUrl = privacyLink ? privacyLink.url : '#'

      value = value.replace(
        '{0}',
        `<a href="${termsUrl}">${this.$t('form.terms.label.terms')}</a>`
      )
      value = value.replace(
        '{1}',
        `<a href="${privacyUrl}">${this.$t('form.terms.label.privacy')}</a>`
      )

      return value
    },
  },
  mounted() {
    this.formData.return_url =
      window.location.origin + this.localePath('auth-activate')
  },
  methods: {
    async onSuccess(data) {
      this.$toast.success(data.detail)
      await this.$store.dispatch('user/setUserToken', data)
      await this.$store.dispatch('basket/refreshBasket')
      const nextUrl = this.$route.query.next || this.localePath('account')
      this.$router.push(nextUrl)
    },
  },
}
</script>

<style lang="scss" scoped>
.registerForm form {
  display: flex;
  flex-direction: column;
  row-gap: em(32);
}

.registerForm__checkbox {
  margin-bottom: em(12);
}

.registerForm__checkbox-link {
  font-weight: var(--text-bold);
}

.margin-top {
  margin-top: em(28);
}
</style>
