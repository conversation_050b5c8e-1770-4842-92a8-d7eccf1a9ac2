<template>
  <ul v-if="items" class="socials">
    <li v-for="item in items" :key="item.id">
      <a
        :href="item.url"
        class="socials__item-link"
        rel="noopener noreferrer"
        target="_blank"
      >
        <!-- {{ item.label }} -->
        <Icon :name="item.iconName" :size="16" />
      </a>
    </li>
  </ul>
</template>

<script>
export default {
  props: {
    items: { type: Array, default: () => [] },
    iconName: { type: String, default: '' },
  },
}
</script>

<style lang="scss">
.socials {
  display: flex;
  margin-right: em(-8);
}
.socials__item-link {
  display: flex;
  padding: em(8);
}
</style>
