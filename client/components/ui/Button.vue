<template>
  <button
    class="button"
    :class="{ 'button--disabled': disabled }"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot name="icon" />
    <ButtonLabel :fetching="fetching">
      <slot />
    </ButtonLabel>
  </button>
</template>

<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    fetching: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="scss">
.button--primary {
  cursor: pointer;
  border: em(1, 21) solid transparent;
  line-height: 1;
  font-size: rem(21);
  font-weight: var(--text-bold);
  padding: em(16, 21) em(18, 21) em(17, 21);
  background-color: var(--button-bg-color);
  color: var(--button-font-color);
  outline: none;
  display: block;
  width: 100%;
  text-align: center;
  text-decoration: none;
  text-transform: none;
  transition: background-color calc(var(--speed)) var(--easing),
    border-color calc(var(--speed)) var(--easing),
    color calc(var(--speed)) var(--easing);

  &:focus-visible,
  &:hover {
    background-color: var(--button-hocus-bg-color);
    color: var(--button-font-color);
  }
}
.button--invert {
  background-color: var(--button-invert-bg-color);
  color: var(--button-invert-font-color);

  &:focus-visible,
  &:hover {
    // background-color: var(--button-invert-hocus-bg-color);
    color: var(--button-invert-hocus-font-color);
  }
}
.button--small {
  font-size: rem(16);
  padding: em(13, 16) em(18, 16) em(14, 16);
  display: inline-block;
  width: auto;
}
.button--cookie {
  font-size: rem(16);
  padding: em(13, 16) em(18, 16) em(14, 16);
}
.button--outline {
  background-color: transparent;
  border-color: var(--button-outline-color);
  color: var(--button-outline-color);

  &:focus-visible,
  &:hover {
    background-color: transparent;
    color: var(--button-hocus-bg-color);
  }
}
.button--invert-outline {
  background-color: transparent;
  border-color: var(--button-outline-invert-bg-color);
  color: var(--button-outline-invert-font-color);

  &:focus-visible,
  &:hover {
    // background-color: var(--button-outline-invert-hocus-bg-color);
    background-color: transparent;
    border-color: var(--button-outline-invert-hocus-bg-color);
    color: var(--button-outline-invert-hocus-font-color);
  }
}
.button--disabled {
  background-color: var(--gray-ef);
  color: var(--gray-b3);
  pointer-events: none;
  user-select: none;
}
</style>
