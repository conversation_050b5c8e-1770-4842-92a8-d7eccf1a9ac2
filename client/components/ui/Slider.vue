<template>
  <div class="slider">
    <SliderImages
      v-touch:swipe="onSwipeHorizontal"
      :images="images"
      :current-index.sync="currentIndex"
      @prev="prevSlide"
      @next="nextSlide"
    />
    <div v-if="total > 1" class="sliderControlsWrapper">
      <SliderControls
        :images="images"
        :current-index.sync="currentIndex"
        @prev="prevSlide"
        @next="nextSlide"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    images: { type: Array, default: () => [] },
  },
  data: () => ({
    currentIndex: 0,
    punch: false,
  }),
  computed: {
    total() {
      return this.images.length
    },
  },
  methods: {
    prevSlide() {
      if (this.currentIndex === 0) this.currentIndex = this.total - 1
      else this.currentIndex--
    },
    nextSlide() {
      if (this.currentIndex >= this.total - 1) this.currentIndex = 0
      else this.currentIndex++
    },
    onSwipeHorizontal(event) {
      if (!this.$device.isDesktop) {
        if (event === 'left') {
          this.nextSlide()
        } else if (event === 'right') {
          this.prevSlide()
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.sliderControlsWrapper {
  display: none;
  margin-top: 30px;

  @include grid('tablet') {
    display: block;
  }
}
</style>
