<template>
  <div class="view-toggle">
    <button
      :class="{ active: homepageView === '3d' }"
      aria-label="Switch to 3D View"
      @click="setView('3d')"
    >
      <Icon name="IconView3D" :size="20" />
    </button>
    <button
      :class="{ active: homepageView === 'grid' }"
      aria-label="Switch to 2D View"
      @click="setView('grid')"
    >
      <Icon name="IconView2D" :size="20" />
    </button>
    <!-- <button
      :class="{ active: homepageView === 'isometric' }"
      aria-label="Switch to Isometric View"
      @click="setView('isometric')"
    >
      Iso
    </button> -->
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'ViewToggle',
  computed: {
    ...mapState('ui', ['homepageView']),
  },
  methods: {
    setView(view) {
      this.$store.dispatch('ui/changeHomepageView', view)
    },
  },
}
</script>

<style lang="scss" scoped>
.view-toggle {
  position: fixed;
  z-index: 100;
  display: flex;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  // Mobile Styles: Horizontal pill at the bottom
  @include grid('laptop', $down: true) {
    flex-direction: row;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem;
    border-radius: 9999px;
    gap: 0.5rem;
  }

  // Desktop Styles: Vertical stack on the right
  @include grid('laptop') {
    flex-direction: column;
    top: 50%;
    right: 2rem;
    transform: translateY(-50%);
    padding: 0.75rem;
    border-radius: 9999px;
    gap: 0.75rem;
  }

  button {
    background-color: transparent;
    border: 1px solid var(--gray-b3);
    color: var(--page-font-color);
    border-radius: 50%;
    cursor: pointer;
    font-weight: var(--text-bold);
    transition: background-color 0.3s, color 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;

    // Mobile button size
    @include grid('laptop', $down: true) {
      width: 2.5rem;
      height: 2.5rem;
      font-size: 0.8rem;
    }

    // Desktop button size
    @include grid('laptop') {
      width: 3rem;
      height: 3rem;
    }

    &:hover {
      background-color: var(--gray-ef);
    }

    &.active {
      background-color: var(--page-font-color);
      color: var(--page-bg-color);
      border-color: var(--page-font-color);
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}
</style>
