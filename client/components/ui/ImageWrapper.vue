<template>
  <div class="imageWrapper" :class="{ 'auto-ratio': ratioSize === 'auto' }">
    <div
      v-if="ratioSize !== 'auto'"
      class="imageWrapper__ratio"
      :style="{ paddingTop: ratioPaddingTop }"
    ></div>
    <img
      v-if="currentImage"
      v-lazy="currentImage.url"
      class="imageWrapper__image"
      :src="currentImage.url"
      :alt="currentImage.alt"
      draggable="false"
    />
    <div v-else class="imageWrapper__fallback">
      <Icon name="IconFallbackLogo" :size="80" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    ratioSize: {
      type: String,
      default: '',
    },
    image: {
      type: [Object, String],
      default: null,
    },
    imageMobile: {
      type: Object,
      default: null,
    },
    imageTablet: {
      type: Object,
      default: null,
    },
    imageLargeDesktop: {
      type: Object,
      default: null,
    },
  },
  data: () => ({
    windowWidth: 0,
  }),
  computed: {
    ratioPaddingTop() {
      const [width, height] = this.ratioSize.split('x')
      const value = (height / width) * 100 + '%'
      return value
    },
    currentImage() {
      if (
        this.$device.isDesktop &&
        this.windowWidth > 1800 &&
        this.imageLargeDesktop
      ) {
        return this.imageLargeDesktop
      } else if (this.$device.isTablet && this.imageTablet) {
        return this.imageTablet
      } else if (this.$device.isMobile && this.imageMobile) {
        return this.imageMobile
      } else {
        return this.image
      }
    },
  },
  mounted() {
    this.windowWidth = window.innerWidth
  },
}
</script>

<style lang="scss">
.imageWrapper {
  position: relative;
  overflow: hidden;

  &.auto-ratio {
    height: 100%;
  }

  > .imageWrapper__image {
    width: 100%;
    transform: translateY(24px);
    opacity: 0;
    transition: opacity calc(var(--speed) * 1.4) var(--easing),
      transform calc(var(--speed) * 1.4) var(--easing);

    /* For the full-height slider */
    .auto-ratio & {
      height: 100%;
      object-fit: contain; /* Use 'contain' to prevent cropping */
      transform: none; /* Override the default entrance animation for this case */
    }

    &[lazy='loaded'] {
      transform: none;
      opacity: 1;
    }
    &[lazy='error'] {
      transform: none;
      opacity: 1;
    }
  }
  .imageWrapper__ratio {
    display: block;
    content: '';
    width: 100%;
  }
  > .imageWrapper__image,
  > .imageWrapper__fallback {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    max-width: calc(100% + 2px);
    width: calc(100% + 2px);
  }

  .imageWrapper__fallback {
    background: var(--missing-image-bg-color);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
