<template>
  <span class="buttonInner">
    <slot />
  </span>
</template>

<style lang="scss">
.buttonInner {
  display: block;

  .button--primary & {
    border: em(1, 21) solid transparent;
    font-size: rem(21);
    font-weight: var(--text-bold);
    padding: em(16, 21) em(18, 21) em(17, 21);
  }

  .button--outline & {
    border-color: var(--button-outline-color);
  }

  .button--small & {
    font-size: rem(16);
    padding: em(13, 16) em(18, 16) em(14, 16);
  }
}
</style>
