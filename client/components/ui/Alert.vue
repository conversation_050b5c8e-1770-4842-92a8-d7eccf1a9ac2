<template>
  <div class="alert" :class="{ isContained: isContained }">
    <div class="alert__grid-cols">
      <slot />
      <div class="alert__grid-rows">
        <p v-if="title" class="alert__title">{{ title }}</p>
        <p v-if="message" class="alert__message">{{ message }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isContained: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    message: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="scss" scoped>
.alert {
  background-color: var(--gray-ef);
  padding-top: em(18);
  padding-bottom: em(18);
  position: relative;
  z-index: -1;

  &.isContained {
    padding-left: em(24);
    padding-right: em(24);
  }
}

.alert__grid-cols {
  display: grid;
  column-gap: em(18);
  grid-auto-flow: column;
  grid-template-columns: auto;
  align-items: center;
  justify-content: flex-start;
}

.alert__grid-rows {
  display: grid;
  row-gap: em(6);
}

.alert__title {
  font-size: em(14);
  font-weight: var(--text-bold);
}

.alert__message {
  font-size: em(12);
}
</style>
