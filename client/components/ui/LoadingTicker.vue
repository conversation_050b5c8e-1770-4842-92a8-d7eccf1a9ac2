<template>
  <span class="loadingTicker">
    <span class="dot start"></span>
    <span class="dot middle"></span>
    <span class="dot end"></span>
  </span>
</template>

<style lang="scss" scoped>
.loadingTicker {
  display: flex;
  justify-content: center;
  align-items: center;
  height: em(21, 21);
  column-gap: em(16, 21);
}

.dot {
  background-color: currentColor;
  border-radius: 9999em;
  width: em(8, 21);
  height: em(8, 21);
  animation: opacity var(--speed) var(--easing) infinite alternate;
  // animation-play-state: paused;
}

.dot.middle {
  animation-delay: -0.5s;
}
.dot.end {
  animation-delay: -1s;
}

@keyframes opacity {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.2;
  }
}
</style>
