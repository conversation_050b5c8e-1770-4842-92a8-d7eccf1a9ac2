<template>
  <div class="sliderImages">
    <div v-if="images.length" class="wrapper">
      <div
        v-for="(item, index) in images"
        :key="item.large.url"
        class="item"
        :class="{ 'is-current': currentIndex === index }"
      >
        <ImageWrapper
          :image="item.large"
          :image-tablet="item.small"
          :image-mobile="item.medium"
          :image-large-desktop="item.huge"
          ratio-size="auto"
        />
      </div>
    </div>

    <ImageWrapper v-else image="undefined" ratio-size="480x636" />

    <transition name="fade">
      <div v-if="loaded && images.length > 1" class="indicators">
        <span class="prev" @click="$emit('prev')">
          <Icon name="IconPrev" />
        </span>
        <span class="next" @click="$emit('next')">
          <Icon name="IconNext" />
        </span>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    currentIndex: { type: Number, default: 0 },
    images: { type: Array, required: true },
  },
  data() {
    return {
      loaded: false,
    }
  },
  mounted() {
    this.loaded = true
  },
}
</script>

<style lang="scss" scoped>
.sliderImages {
  position: relative;
  /* Make the slider fill its sticky parent */
  height: 100%;
}
.overlay {
  content: '';
  @include position(absolute, 0);
  z-index: 2;
}
.wrapper {
  position: relative;
  height: 100%;
  &::before {
    /* We remove the fixed aspect ratio */
    display: none;
  }
}
.item {
  @include position(absolute, 0);
  opacity: 0;
  height: 100%; /* Make each slide take full height */
  transition: opacity calc(var(--speed) * 1.4) var(--easing),
    transform calc(var(--speed) * 1.4) var(--easing);
  transform: translateY(8px) scale(0.96);
  opacity: 0;

  &.is-current {
    opacity: 1;
    transition-delay: var(--speed);
    transform: none;
    z-index: 1;
  }
}
.indicators {
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 50%;
  left: -24px;
  transform: translateY(-50%);
  z-index: 1;
  width: calc(100% + 48px);
  pointer-events: none;

  @include grid('laptop') {
    left: -36px;
    width: calc(100% + 72px);
  }

  .prev,
  .next {
    pointer-events: auto;
    cursor: pointer;
  }
}
</style>
