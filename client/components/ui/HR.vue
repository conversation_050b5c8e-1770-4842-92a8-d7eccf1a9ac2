<template>
  <hr
    :class="{
      'margins-24': margins === 24,
      'margins-32': margins === 32,
      'margins-36': margins === 36,
      'margins-40': margins === 40,
      'margins-44': margins === 44,
    }"
  />
</template>

<script>
export default {
  props: {
    margins: { type: Number, default: null },
  },
}
</script>

<style lang="scss" scoped>
hr {
  border-color: var(--page-font-color);
  opacity: 0.15;

  &.black {
    opacity: 1;
  }

  &.margins-24 {
    margin-top: em(24);
    margin-bottom: em(24);
  }

  &.margins-32 {
    margin-top: em(32);
    margin-bottom: em(32);
  }

  &.margins-36 {
    margin-top: em(36);
    margin-bottom: em(36);
  }

  &.margins-40 {
    margin-top: em(40);
    margin-bottom: em(40);
  }

  &.margins-44 {
    margin-top: em(44);
    margin-bottom: em(44);
  }
}
</style>
