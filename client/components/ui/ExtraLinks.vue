<template>
  <div class="extraLinks">
    <p class="extraLinks__title">{{ title }}</p>
    <NLink :to="localePath(buttonUrl)" class="button--primary button--outline">
      <ButtonLabel>
        {{ buttonLabel }}
      </ButtonLabel>
    </NLink>
  </div>
</template>

<script>
export default {
  props: {
    title: { type: String, default: '' },
    buttonUrl: { type: String, default: '' },
    buttonLabel: { type: String, default: '' },
  },
}
</script>

<style lang="scss" scoped>
.extraLinks {
  margin-top: em(32);
}

.extraLinks__title {
  font-size: em(18);
  font-weight: var(--text-bold);
  text-align: center;
  margin-bottom: em(30, 18);
}
</style>
