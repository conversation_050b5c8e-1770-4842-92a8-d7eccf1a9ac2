<template>
  <div class="sliderControls">
    <div class="wrapper">
      <div class="controls">
        <span class="prev" @click="$emit('prev')">
          <Icon name="IconPrev" />
        </span>
        <span class="next" @click="$emit('next')">
          <Icon name="IconNext" />
        </span>
      </div>
      <div class="items">
        <span
          v-for="(item, index) in images"
          :key="item.small.url"
          class="item"
          :class="{ 'is-current': currentIndex === index }"
          @click="$emit('update:currentIndex', index)"
        >
          <ImageWrapper :image="item.small" ratio-size="80x95" />
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentIndex: { type: Number, default: 0 },
    images: { type: Array, required: true },
  },
}
</script>

<style lang="scss" scoped>
.wrapper {
  // display: table;
  margin: auto;
  position: relative;
}
.controls {
  @include position(absolute, 0 null null 0);
  width: 100%;
  height: 100%;

  span {
    @include size(50px);
    display: flex;
    padding: 10px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
.prev {
  @include position(absolute, 50% null null auto);
  transform: translateY(-50%);
}
.next {
  @include position(absolute, 50% 0 null auto);
  transform: translateY(-50%);
}
.items {
  display: flex;
  flex: 1;
  width: 80%;
  margin: auto;
}
.item {
  // @include size(80px, 95px);
  flex: 0 1 120px;
  display: block;
  cursor: pointer;
  margin: 0 10px;
  transition: opacity var(--speed) var(--easing);

  &.is-current {
    opacity: 0.5;
  }
}
</style>
