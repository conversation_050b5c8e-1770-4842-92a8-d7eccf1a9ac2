<template>
  <article class="moreItems">
    <h1 class="moreItems__title">{{ title }}</h1>

    <LayoutContainer :has-track-on-mobile="true">
      <div class="moreItems__inner">
        <ul class="moreItems__list">
          <li
            v-for="(item, index) in items"
            :key="`${item + index}`"
            class="moreItems__list-item"
          >
            <MoreItemsLink :item="item" />
          </li>
        </ul>
      </div>
    </LayoutContainer>
  </article>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    items: {
      type: Array,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.moreItems {
  margin-top: em(75);
  margin-bottom: em(45);
}
.moreItems__inner {
  overflow-x: auto;

  &::-webkit-scrollbar {
    width: em(4);
    height: em(8);
    background-color: var(--scrollbar-bg-color);
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-color);
  }
  // firefox
  scrollbar-width: thin;
}
.moreItems__title {
  font-size: em(18);
  font-weight: var(--text-bold);
  margin-bottom: em(15);
}
.moreItems__list {
  @include grid('laptop', $down: true) {
    display: flex;
    margin-right: em(-8);
    margin-left: em(26);
  }

  @include grid('laptop') {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    column-gap: em(26);
    row-gap: em(26);
  }

  @include grid('desktop-large') {
    column-gap: em(40);
    row-gap: em(40);
  }
}
.moreItems__list-item {
  @include grid('laptop', $down: true) {
    flex: 0 0 45%;
    margin-right: em(8);
  }
}
</style>
