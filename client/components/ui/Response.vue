<template>
  <div class="response">
    <div class="response__inner">
      <Icon :size="96" :name="icon" />
      <h1 class="response__title">{{ title }}</h1>
      <!-- eslint-disable-next-line vue/no-v-html -->
      <div v-html="message"></div>
    </div>

    <slot />
  </div>
</template>

<script>
export default {
  props: {
    icon: { type: String, default: '' },
    title: { type: String, default: '' },
    message: { type: String, default: '' },
  },
}
</script>

<style lang="scss">
.response {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  min-height: 100vh;
  padding-top: em(64);
  padding-bottom: em(32);
}
.response__inner {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.response__title {
  font-size: em(40);
  line-height: var(--title-line-height);
  margin-top: em(34, 40);
  margin-bottom: em(14, 40);
  font-weight: var(--text-bold);

  @include grid('laptop', $down: true) {
    font-size: em(32);
  }
}
</style>
