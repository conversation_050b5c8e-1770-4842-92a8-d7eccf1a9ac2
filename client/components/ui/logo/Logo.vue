<template>
  <div class="headerLogo__wrapper header-logo">
    <a :href="localePath('index')" rel="home" class="headerLogo__link">
      <span class="headerLogo">
        <slot />
      </span>
    </a>
  </div>
</template>

<style lang="scss" scoped>
.headerLogo__wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @include grid('laptop', $down: true) {
    margin-left: em(16);
  }

  @include grid('laptop') {
    margin-left: em(32);
  }
}
.headerLogo {
  user-select: none;
  // display: flex;
}

.headerLogo__link {
  pointer-events: auto;
}
</style>
