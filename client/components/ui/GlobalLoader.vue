<template>
  <transition name="fade">
    <div v-if="isLoading" class="loader-overlay">
      <div class="logo-wrapper">
        <LogoBrand />
      </div>
    </div>
  </transition>
</template>

<script>
import LogoBrand from '~/components/ui/logo/LogoBrand.vue'

export default {
  components: { LogoBrand },
  computed: {
    isLoading() {
      return this.$store.state.page.isLoading
    },
  },
}
</script>

<style lang="scss" scoped>
.loader-overlay {
  position: fixed;
  inset: 0;
  background-color: #fff;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-wrapper {
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
