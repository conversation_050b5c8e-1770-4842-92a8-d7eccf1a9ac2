<template>
  <span
    class="icon"
    :class="{
      'iconSize-11': size === 11,
      'iconSize-16': size === 16,
      'iconSize-20': size === 20,
      'iconSize-32': size === 32,
      'iconSize-48': size === 48,
      'iconSize-64': size === 64,
      'iconSize-96': size === 96,
      'iconSize-80': size === 80,
      'iconOpacity-15': name === 'IconBagPlus' || name === 'IconFallbackLogo',
    }"
  >
    <slot v-if="name === 'IconMenu'">
      <IconMenu />
    </slot>
    <slot v-else-if="name === 'IconMenuClose'">
      <IconMenuClose />
    </slot>
    <slot v-else-if="name === 'IconUser'">
      <IconUser />
    </slot>
    <slot v-else-if="name === 'IconBag'">
      <IconBag />
    </slot>
    <slot v-else-if="name === 'IconBoxChecked'">
      <IconBoxChecked />
    </slot>
    <slot v-else-if="name === 'IconBagCheck'">
      <IconBagCheck />
    </slot>
    <slot v-else-if="name === 'IconBagPlus'">
      <IconBagPlus />
    </slot>
    <slot v-else-if="name === 'IconBagX'">
      <IconBagX />
    </slot>
    <slot v-else-if="name === 'IconTruck'">
      <IconTruck />
    </slot>
    <slot v-else-if="name === 'IconFallbackLogo'">
      <IconFallbackLogo />
    </slot>
    <slot v-else-if="name === 'IconCircleCheckLarge'">
      <IconCircleCheckLarge />
    </slot>
    <slot v-else-if="name === 'IconCircleCheckSmall'">
      <IconCircleCheckSmall />
    </slot>
    <slot v-else-if="name === 'IconCartRemove'">
      <IconCartRemove />
    </slot>
    <slot v-else-if="name === 'IconArrowLeft'">
      <IconArrowLeft />
    </slot>
    <slot v-else-if="name === 'IconPrev'">
      <IconPrev />
    </slot>
    <slot v-else-if="name === 'IconNext'">
      <IconNext />
    </slot>
    <slot v-else-if="name === 'IconPasswordVisible'">
      <IconPasswordVisible />
    </slot>
    <slot v-else-if="name === 'IconPasswordHidden'">
      <IconPasswordHidden />
    </slot>
    <slot v-else-if="name === 'IconSocialFacebook'">
      <IconSocialFacebook />
    </slot>
    <slot v-else-if="name === 'IconSocialInstagram'">
      <IconSocialInstagram />
    </slot>
    <slot v-else-if="name === 'IconSocialTwitter'">
      <IconSocialTwitter />
    </slot>
    <slot v-else-if="name === 'IconSocialTwitch'">
      <IconSocialTwitch />
    </slot>
    <slot v-else-if="name === 'IconView3D'">
      <IconView3D />
    </slot>
    <slot v-else-if="name === 'IconView2D'">
      <IconView2D />
    </slot>
    <slot v-else> Default icon </slot>
  </span>
</template>

<script>
export default {
  props: {
    name: { type: String, default: '' },
    size: { type: Number, default: null },
  },
}
</script>

<style lang="scss">
.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: rem(24);
  height: rem(24);

  &.iconSize-11 {
    width: rem(11);
    height: rem(11);
  }

  &.iconSize-16 {
    width: rem(16);
    height: rem(16);
  }

  &.iconSize-20 {
    width: rem(20);
    height: rem(20);
  }

  &.iconSize-32 {
    width: rem(32);
    height: rem(32);
  }

  &.iconSize-48 {
    width: rem(48);
    height: rem(48);
  }

  &.iconSize-64 {
    width: rem(64);
    height: rem(64);
  }

  &.iconSize-96 {
    width: rem(96);
    height: rem(96);
  }

  &.iconSize-80 {
    width: rem(80);
    height: rem(80);
  }

  &.iconOpacity-15 {
    opacity: 0.15;
  }

  svg {
    display: block;
    width: 100%;
    height: 100%;
    fill: currentColor;
  }
}
</style>
