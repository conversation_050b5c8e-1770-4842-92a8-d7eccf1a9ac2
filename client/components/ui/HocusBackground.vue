<template>
  <span class="hocusBackground__wrapper">
    <span class="disabledBackground"></span>
    <span class="hocusBackground"></span>
    <slot />
  </span>
</template>

<style lang="scss">
.hocusBackground__wrapper {
  position: relative;
  z-index: 0;

  display: block;
}

.hocusBackground,
.disabledBackground {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  top: 0;
  // z-index: -1;

  // transform: scaleY(1);
  transform-origin: top;
  transition: transform calc(var(--speed) * 2) var(--easing);
}

.hocusBackground {
  background-color: var(--button-bg-color);

  .button--disabled & {
    transform: scaleY(0);
    transform-origin: bottom;
  }

  .button--primary:focus-visible &,
  .button--primary:hover & {
    transform: scaleY(0);
    transform-origin: bottom;
  }

  .button--outline & {
    background-color: var(--button-outline-hocus-bg-color);
    transform: scaleY(0);
    transform-origin: bottom;
  }

  .button--outline:focus-visible &,
  .button--outline:hover & {
    transform: none;
    transform-origin: top;
  }
}

.disabledBackground {
  background-color: var(--gray-ef);

  .button--outline & {
    display: none;
  }
}
</style>
