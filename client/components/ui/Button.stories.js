
import Button from './Button.vue';

export default {
  component: Button,
  title: 'Components/Button',
};

// 👇 We create a “template” of how args map to rendering
const Template = (args, { argTypes }) => ({
  components: { Button },
  props: Object.keys(argTypes),
  template: `
  <Button v-bind="$props">
    {{content}}
  </Button>
  `,
});

// 👇 Each story then reuses that template
export const Primary = Template.bind({});
Primary.args = { content: 'Primary' };

export const Secondary = Template.bind({});
Secondary.args = { ...Primary.args, content: 'Secondary' };

export const Disabled = Template.bind({});
Disabled.args = {
  disabled: true,
  ...Primary.args,
  content: 'Disabled'
};
