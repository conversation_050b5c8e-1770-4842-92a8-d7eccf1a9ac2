<template>
  <span class="buttonLabel">
    <LoadingTicker v-if="fetching" />
    <span v-else>
      <slot />
    </span>
  </span>
</template>

<script>
export default {
  props: {
    fetching: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="scss">
.buttonLabel {
  $translateUnit: em(1, 16);

  pointer-events: none;
  display: block;
  transform: translateY($translateUnit);
}
</style>
