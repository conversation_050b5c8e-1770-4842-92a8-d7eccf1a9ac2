<template>
  <zoom-on-hover
    :class="{ 'is-loaded': imgLoaded }"
    :img-normal="item.large.url"
    :scale="3"
    @loaded="imgLoaded = true"
  ></zoom-on-hover>
</template>

<script>
export default {
  props: {
    item: {
      type: [Object, String],
      default: null,
    },
  },
  data() {
    return {
      imgLoaded: false,
    }
  },
}
</script>

<style lang="scss">
.zoom-on-hover {
  .normal {
    transform: translateY(8px) scale(0.96);
    opacity: 0;
    transition: opacity calc(var(--speed) * 1.4) var(--easing),
      transform calc(var(--speed) * 1.4) var(--easing);
  }

  &.is-loaded .normal {
    opacity: 1;
    transform: none;
  }
}
</style>
