<template>
  <NLink :to="item.url_path" class="moreItemsLink">
    <ImageWrapper
      v-if="item.image"
      class="moreItemsLink__img"
      :image="item.image"
      ratio-size="480x636"
    />
  </NLink>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
}
</script>

<style lang="scss">
.moreItemsLink__img {
  width: 100%;
}
</style>
