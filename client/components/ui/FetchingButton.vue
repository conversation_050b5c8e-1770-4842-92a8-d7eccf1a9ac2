<template>
  <Button :disabled="fetching" :fetching="fetching" @click="onClick">
    <slot />
  </Button>
</template>

<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    asyncAction: {
      type: Function,
      default: null,
    },
    actionSuccess: {
      type: Function,
      default: null,
    },
    actionError: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      fetching: false,
      minDuration: 600,
    }
  },
  methods: {
    async onClick($event) {
      this.$emit('click', $event)
      this.fetching = true
      try {
        const data = await this.asyncAction($event)
        setTimeout(() => {
          this.fetching = false
          if (this.actionSuccess) this.actionSuccess(data)
        }, this.minDuration)
      } catch (error) {
        setTimeout(() => {
          this.fetching = false
          if (this.actionError) this.actionError(error)
        }, this.minDuration)
      }
    },
  },
}
</script>
