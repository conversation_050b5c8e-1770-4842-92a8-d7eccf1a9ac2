<template>
  <header
    class="header"
    :class="{
      isViewportHeight: isViewportHeight,
      isTextCentered: isTextCentered,
    }"
  >
    <slot name="above" />
    <HeaderTitle>
      {{ title }}
    </HeaderTitle>

    <slot />
  </header>
</template>

<script>
export default {
  props: {
    title: { type: String, default: '' },
    isViewportHeight: { type: Boolean, default: false },
    isTextCentered: { type: Boolean, default: false },
  },
}
</script>

<style lang="scss" scoped>
.header {
  @include grid('laptop', $down: true) {
    margin-top: em(130);
    margin-bottom: em(42);
  }

  @include grid('laptop') {
    margin-top: em(170);
    margin-bottom: em(48);
  }
}
.isViewportHeight {
  min-height: 100vh;
}
.isTextCentered {
  text-align: center;
}
</style>
