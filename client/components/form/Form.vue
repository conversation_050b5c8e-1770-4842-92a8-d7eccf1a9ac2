<template>
  <ValidationObserver
    ref="form"
    tag="form"
    class="form"
    @submit.prevent="onSubmit"
  >
    <slot></slot>
  </ValidationObserver>
</template>

<script>
import { ValidationObserver } from 'vee-validate'

export const FormStatus = Object.freeze({
  IDLE: 'idle',
  FETCHING: 'fetching',
  SUCCESS: 'success',
  ERROR: 'error',
})

export default {
  components: {
    ValidationObserver,
  },
  provide() {
    return { form: this }
  },
  props: {
    action: { type: [String, Function], default: null },
    payload: { type: null, default: () => ({}) },
    method: { type: String, default: 'POST' },
  },
  data: () => ({
    FormStatus,
    status: FormStatus.IDLE,
  }),
  methods: {
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (valid) {
        this.$emit('submit', this.payload)
        if (!this.$listeners.submit) await this.submit()
      } else {
        this.$emit('invalid')
        if (!this.$listeners.invalid) this.invalid()
      }
    },
    async submit() {
      this.status = FormStatus.FETCHING
      try {
        const response =
          typeof this.action === 'function'
            ? await this.action(this.payload)
            : await this.$axios({
                method: this.method,
                url: this.action,
                data: this.payload,
              })
        this.status = FormStatus.SUCCESS
        this.$emit('success', response.data)
        if (!this.$listeners.success) this.success(response.data)
      } catch (e) {
        this.status = FormStatus.ERROR
        this.$emit('error', e.response.data)
        if (!this.$listeners.error) this.error(e.response.data)
      }
    },
    success(data) {
      if (data.detail) {
        this.$toast.success(data.detail)
      }
    },
    error(data) {
      // Accepts errors object: `{ email: ['This field is required'] }`
      if (data.detail) {
        this.$toast.error(data.detail)
      } else if (data.non_field_errors) {
        this.$toast.error(data.non_field_errors[0])
      } else {
        this.setErrors(data)
        this.$toast.error(this.$t('form.correctErrors'))
      }
      this.$emit('invalid', data)
      if (!this.$listeners.invalid) this.invalid(data)
    },
    invalid(data) {
      // Do nothing for now...
    },
    reset() {
      this.$refs.form.reset()
    },
    setErrors(data) {
      this.$refs.form.setErrors(data)
    },
  },
}
</script>
