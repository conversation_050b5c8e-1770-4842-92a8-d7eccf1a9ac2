<template>
  <ValidationProvider
    v-slot="{ errors, required }"
    :vid="vid"
    :name="name || label || vid"
    :rules="rules"
    slim
  >
    <div
      class="formField"
      :class="{
        'is-error': errors.length,
        'is-required': required,
        'has-value': value || isFocused,
      }"
    >
      <label v-if="label !== false" :for="vid" class="formField__label">
        {{ label || name || vid }}
      </label>

      <FormInput
        :value="value"
        :vid="vid"
        :type="type"
        :placeholder="placeholder"
        :choices="choices"
        @input="$emit('input', $event)"
        @focus="isFocused = true"
        @focusout="isFocused = false"
      />

      <span class="help">
        <slot name="help">{{ help }}</slot>
      </span>

      <span class="errors">
        <span class="errors__overflow">
          <Transition name="onError">
            <span v-if="errors.length" class="errors__message">
              <span>{{ errors[0] }}</span>
            </span>
          </Transition>
        </span>
      </span>
    </div>
  </ValidationProvider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'

export default {
  components: {
    ValidationProvider,
  },
  props: {
    vid: { type: String, required: true },
    name: { type: String, default: '' },
    label: { type: [String, Boolean], default: '' },
    help: { type: String, default: '' },
    rules: { type: [String, Object], default: '' },

    // Input props
    value: { type: null, default: '' },
    type: { type: String, default: 'text' },
    placeholder: { type: String, default: '' },
    choices: { type: Array, default: () => [] },
  },
  data() {
    return {
      isFocused: false,
    }
  },
}
</script>

<style lang="scss">
// multiple :not() selectors need to be chained
input:not([type='checkbox']):not([type='radio']),
textarea,
select {
  display: block;
  width: 100%;

  font-size: em(21);

  padding-top: em(8, 21);
  padding-bottom: em(9, 21);
  background-color: var(--white);

  border-bottom: em(1, 21) solid var(--gray-b3);
  transition: border-color var(--speed) var(--easing);

  .formField.has-value & {
    border-color: var(--page-font-color);
    outline: none;
  }

  .formField.is-error & {
    // needs to be after .has-value
    border-color: var(--error-color);
  }
}

.formField {
  position: relative;
  display: flex;
  flex-direction: column;
}

.formField__label {
  display: flex;
  pointer-events: none;

  transform: translateY(em(28, 14));
  transform-origin: top left;
  transition: color var(--speed) var(--easing),
    transform var(--speed) var(--easing);

  .formField.has-value & {
    color: var(--gray-b3);
    transform: scale(calc(14 / 16));
  }
}

.errors {
  position: absolute;
  top: 100%;
  left: auto;
}

.errors__overflow {
  // background-color: yellow;
  overflow: hidden;
  margin-top: em(9, 12);
  margin-bottom: em(10, 12);
  display: block;
}

.errors__message {
  font-size: rem(12);
  color: var(--error-color);
  display: block;
  transform-origin: bottom left;
}

// transition
.onError-enter-active,
.onError-leave-active {
  transition: opacity var(--speed) var(--easing),
    transform calc(var(--speed) * 2) var(--easing);
}
.onError-enter,
.onError-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}
</style>
