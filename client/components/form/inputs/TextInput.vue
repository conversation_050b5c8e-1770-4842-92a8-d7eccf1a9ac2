<template>
  <div class="textInput">
    <textarea
      v-if="type === 'textarea'"
      :id="vid"
      :value="value"
      @input="$emit('input', $event.target.value)"
      @focus="$emit('focus', $event)"
      @focusout="$emit('focusout', $event)"
    ></textarea>
    <input
      v-else
      :id="vid"
      :type="inputType"
      :value="value"
      :class="{ passwordInput: type === 'password' }"
      @input="$emit('input', $event.target.value)"
      @focus="$emit('focus', $event)"
      @focusout="$emit('focusout', $event)"
    />
    <span
      v-if="type === 'password'"
      class="toggleVisibilityPassword"
      aria-label="toggle-visibility-password"
      :aria-pressed="isPasswordVisible.toString()"
      @click="toggleVisibilityPassword"
      @keyup.enter.space="toggleVisibilityPassword"
    >
      <Icon v-if="!isPasswordVisible" name="IconPasswordVisible" />
      <Icon v-else name="IconPasswordHidden" />
    </span>
  </div>
</template>

<script>
export default {
  props: {
    vid: { type: String, required: true },
    value: { type: String, default: '' },
    type: { type: String, default: 'text' },
  },
  data: () => ({
    isPasswordVisible: false,
  }),
  created() {
    this.inputType = this.type
  },
  methods: {
    toggleVisibilityPassword() {
      this.isPasswordVisible = !this.isPasswordVisible
      this.inputType = this.isPasswordVisible ? 'text' : 'password'
    },
  },
}
</script>

<style lang="scss">
.passwordInput {
  padding-right: em(32, 21);
}

// password dots only
input[type='password'] {
  font: small-caption;
  letter-spacing: 0.05rem;
}

.toggleVisibilityPassword {
  position: absolute;
  bottom: em(10);
  right: 0;
  cursor: pointer;
}
</style>
