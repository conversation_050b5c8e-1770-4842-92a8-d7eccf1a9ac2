<template>
  <div class="checkboxInput">
    <span class="wrap">
      <input
        :id="vid"
        :value="value"
        type="checkbox"
        @input="$emit('input', $event.target.checked)"
      />
      <span class="checkboxInput__marker">
        <Icon name="IconBoxChecked" :size="16" />
      </span>
      <label class="checkboxInput__label" :for="vid">
        <!-- eslint-disable-next-line vue/no-v-html -->
        <span v-html="placeholder"></span>
      </label>
    </span>
  </div>
</template>

<script>
export default {
  props: {
    vid: { type: String, default: '' },
    value: { type: [String, Boolean], default: '' },
    placeholder: { type: String, default: '' },
  },
}
</script>

<style lang="scss" scoped>
.checkboxInput {
  position: relative;
}

.wrap {
  display: flex;
  align-items: center;
}

.checkboxInput__label {
  font-size: em(14);
  flex: 1;
  pointer-events: auto;
  padding-left: em(36, 14);

  .gray > & {
    padding-top: em(29, 14);
    padding-bottom: em(27, 14);
    padding-right: em(20, 14);
    padding-left: em(56, 14);
  }

  input[type='checkbox']:focus-visible ~ & {
    outline: 2px dashed var(--gray-b3);
    // color: var(--gray-b3);
    // transition: color var(--speed) var(--easing);
  }

  &::v-deep a {
    font-weight: var(--text-bold);
  }
}

.checkboxInput__marker {
  box-shadow: inset 0 0 0 em(1) var(--page-font-color);
  position: absolute;
  top: em(3);
  left: 0;
  pointer-events: none;

  .gray > & {
    top: em(24, 14);
    left: em(20, 14);
  }

  & > * {
    // and if child is checkbox
    input[type='checkbox'] + & {
      opacity: 0;
      transition: opacity var(--speed) var(--easing);
    }

    input[type='checkbox']:checked + & {
      opacity: 1;
    }
  }
}
</style>
