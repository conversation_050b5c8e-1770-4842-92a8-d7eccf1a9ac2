<template>
  <div class="selectInput">
    <select
      :id="vid"
      :class="{ 'is-placeholder': !value }"
      :value="value"
      @input="$emit('input', $event.target.value)"
      @focus="$emit('focus', $event)"
      @focusout="$emit('focusout', $event)"
    >
      <option value="">
        {{ placeholder || $t('form.selectPlaceholder') }}
      </option>
      <option v-for="item in choices" :key="item[0]" :value="item[0]">
        {{ item[1] }}
      </option>
    </select>
  </div>
</template>

<script>
export default {
  props: {
    vid: { type: String, required: true },
    value: { type: null, default: '' },
    placeholder: { type: String, default: '' },
    choices: { type: Array, default: () => [] },
  },
}
</script>
