<template>
  <div class="formInput">
    <SelectInput
      v-if="choices.length || type === 'select'"
      :vid="vid"
      :value="value"
      :type="type"
      :placeholder="placeholder"
      :choices="choices"
      @input="$emit('input', $event)"
      @focus="$emit('focus', $event)"
      @focusout="$emit('focusout', $event)"
    />
    <CheckboxInput
      v-else-if="type === 'checkbox'"
      :vid="vid"
      :value="value"
      :type="type"
      :placeholder="placeholder"
      @input="$emit('input', $event)"
      @focus="$emit('focus', $event)"
      @focusout="$emit('focusout', $event)"
    />
    <TextInput
      v-else
      :vid="vid"
      :value="value"
      :type="type"
      :placeholder="placeholder"
      @input="$emit('input', $event)"
      @focus="$emit('focus', $event)"
      @focusout="$emit('focusout', $event)"
    />
  </div>
</template>

<script>
export default {
  props: {
    value: { type: null, default: '' },
    vid: { type: String, required: true },
    type: { type: String, default: 'text' },
    placeholder: { type: String, default: '' },
    choices: { type: Array, default: () => [] },
  },
}
</script>
