<template>
  <Button
    class="button--primary formButton"
    :class="{
      'is-fetching': fetchingDelayed,
      'is-invalid': invalid,
      'is-error': error,
      'is-success': success,
    }"
    :disabled="invalid || fetchingDelayed"
    :fetching="fetchingDelayed"
  >
    <span v-if="invalid">
      <slot name="error">{{ $t('form.correctErrors') }}</slot>
    </span>
    <span v-else>
      <slot>{{ $t('form.submit') }}</slot>
    </span>
  </Button>
</template>

<script>
export default {
  inject: ['$_veeObserver', 'form'],
  props: {
    forceInvalid: { type: Boolean, default: false },
    fetchingOnSuccess: { type: Boolean, default: false },
  },
  data: () => ({
    fetchingDelayed: false,
    fetchingDelayedTimeout: null,
  }),
  computed: {
    invalid() {
      return this.forceInvalid || this.$_veeObserver.flags.failed
    },
    fetching() {
      const isFetching = this.form.status === this.form.FormStatus.FETCHING
      if (this.fetchingOnSuccess) return isFetching || this.success
      return isFetching
    },
    error() {
      return this.form.status === this.form.FormStatus.ERROR
    },
    success() {
      return this.form.status === this.form.FormStatus.SUCCESS
    },
  },
  watch: {
    fetching(value) {
      if (this.fetchingDelayedTimeout) clearTimeout(this.fetchingDelayedTimeout)
      if (value) this.fetchingDelayed = true
      else {
        this.fetchingDelayedTimeout = setTimeout(() => {
          this.fetchingDelayed = false
        }, 600)
      }
    },
  },
}
</script>
