<template>
  <div v-if="!hasError && notifications.length > 0" class="notificationTicker">
    <!-- Ticker Style (scrolling) -->
    <div v-if="displayType === 'ticker'" class="notificationTicker__ticker">
      <div
        class="notificationTicker__scroll"
        :style="{
          backgroundColor: currentNotification.background_color,
          color: currentNotification.text_color,
        }"
      >
        <div
          class="notificationTicker__content"
          :style="{ animationDuration: settings.ticker_scroll_speed }"
        >
          <span
            v-for="(notification, index) in notifications"
            :key="notification.id"
            class="notificationTicker__item"
          >
            {{ notification.message }}
            <button
              v-if="notification.copyable_code"
              class="notificationTicker__copy"
              @click="
                copyToClipboard(
                  notification.copyable_code,
                  notification.copy_button_text
                )
              "
            >
              {{ notification.copy_button_text }}
            </button>
            <a
              v-if="notification.link_url"
              :href="notification.link_url"
              class="notificationTicker__link"
              target="_blank"
              rel="noopener noreferrer"
            >
              {{ notification.link_text }}
            </a>
            <span
              v-if="index < notifications.length - 1"
              class="notificationTicker__separator"
              >•</span
            >
          </span>
        </div>
      </div>
    </div>

    <!-- Carousel Style (one at a time) -->
    <div
      v-else-if="displayType === 'carousel'"
      class="notificationTicker__carousel"
    >
      <transition name="slide-fade" mode="out-in">
        <div
          :key="currentNotification.id"
          class="notificationTicker__carouselItem"
          :style="{
            backgroundColor: currentNotification.background_color,
            color: currentNotification.text_color,
          }"
        >
          <div class="notificationTicker__carouselContent">
            <span class="notificationTicker__message">
              {{ currentNotification.message }}
            </span>
            <div class="notificationTicker__actions">
              <button
                v-if="currentNotification.copyable_code"
                class="notificationTicker__copy"
                @click="
                  copyToClipboard(
                    currentNotification.copyable_code,
                    currentNotification.copy_button_text
                  )
                "
              >
                {{ currentNotification.copy_button_text }}
              </button>
              <a
                v-if="currentNotification.link_url"
                :href="currentNotification.link_url"
                class="notificationTicker__link"
                target="_blank"
                rel="noopener noreferrer"
              >
                {{ currentNotification.link_text }}
              </a>
            </div>
          </div>

          <!-- Navigation dots for carousel -->
          <div v-if="notifications.length > 1" class="notificationTicker__dots">
            <button
              v-for="(notification, index) in notifications"
              :key="notification.id"
              class="notificationTicker__dot"
              :class="{ active: index === currentIndex }"
              @click="setCurrentIndex(index)"
            />
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      notifications: [],
      settings: {
        display_type: 'ticker',
        carousel_rotation_speed: 5,
        ticker_scroll_speed: '30s',
      },
      currentIndex: 0,
      carouselInterval: null,
      refreshInterval: null,
      hasError: false,
    }
  },
  computed: {
    currentNotification() {
      return this.notifications[this.currentIndex] || {}
    },
    displayType() {
      // Use the global display type from settings
      return this.settings.display_type || 'ticker'
    },
  },
  watch: {
    displayType() {
      // Restart carousel rotation when display type changes
      this.stopCarouselRotation()
      this.startCarouselRotation()
    },
  },
  async mounted() {
    try {
      // Set initial state (no notifications)
      this.updateNotificationVisibility()

      await this.fetchNotifications()
      this.startCarouselRotation()
      this.startRefreshInterval()

      // Update again after fetching
      this.updateNotificationVisibility()
    } catch (error) {
      this.hasError = true
      this.updateNotificationVisibility()
      // Don't break the page, just disable the component
    }
  },
  beforeDestroy() {
    this.stopCarouselRotation()
    this.stopRefreshInterval()
    // Remove notification class when component is destroyed
    if (typeof document !== 'undefined') {
      document.body.classList.remove('has-notifications')
    }
  },
  errorCaptured(err, instance, info) {
    console.error(err, instance, info)
    this.hasError = true
    // Prevent the error from propagating
    return false
  },
  methods: {
    async fetchNotifications() {
      try {
        // Safely check if API is available
        if (
          !this.$api ||
          !this.$api.config ||
          !this.$api.config.getNotifications
        ) {
          this.useFallbackNotifications()
          return
        }

        // Add cache-busting parameter and locale to ensure fresh data
        const timestamp = Date.now()
        const response = await this.$api.config.getNotifications({
          params: {
            _t: timestamp,
            active_only: true, // Only fetch active notifications
            locale: this.$i18n.locale, // Pass current locale for multilingual support
          },
        })

        // Handle the new API structure with settings and notifications
        const data = response?.data || response || {}

        // Extract settings and notifications from the response
        if (data.settings && data.notifications) {
          // Update settings from API
          this.settings = {
            display_type: data.settings.display_type || 'ticker',
            carousel_rotation_speed: data.settings.carousel_rotation_speed || 5,
            ticker_scroll_speed: data.settings.ticker_scroll_speed || '30s',
          }

          // Update notifications (they should already be filtered and localized)
          if (Array.isArray(data.notifications)) {
            this.notifications = data.notifications.filter(
              (notification) => notification.is_currently_active === true
            )
          } else {
            this.notifications = []
          }
        } else if (Array.isArray(data)) {
          // Fallback for old API structure
          this.notifications = data.filter(
            (notification) => notification.is_currently_active === true
          )
        } else {
          this.useFallbackNotifications()
        }

        // Reset current index if it's out of bounds
        if (this.currentIndex >= this.notifications.length) {
          this.currentIndex = 0
        }

        // Update global notification visibility state
        this.updateNotificationVisibility()
      } catch (error) {
        this.useFallbackNotifications()
      }
    },

    useFallbackNotifications() {
      // Fallback when API is unavailable - use test notification for debugging
      this.notifications = [
        {
          id: 'test-1',
          message: 'Free Shipping Spain',
          background_color: '#007bff',
          text_color: '#ffffff',
          is_currently_active: true,
          display_type: 'ticker',
        },
      ]
      this.updateNotificationVisibility()
    },

    async forceRefresh() {
      // Force refresh by clearing cache and fetching new data
      try {
        if (
          this.$api &&
          this.$api.config &&
          this.$api.config.refreshNotifications
        ) {
          await this.$api.config.refreshNotifications()
        }
        await this.fetchNotifications()
      } catch (error) {
        await this.fetchNotifications() // Try regular fetch as fallback
      }
    },

    updateNotificationVisibility() {
      // Update CSS class on body to indicate if notifications are visible
      const hasNotifications = !this.hasError && this.notifications.length > 0

      if (typeof document !== 'undefined') {
        if (hasNotifications) {
          document.body.classList.add('has-notifications')
        } else {
          document.body.classList.remove('has-notifications')
        }
      }
    },

    startCarouselRotation() {
      if (this.displayType === 'carousel' && this.notifications.length > 1) {
        const rotationSpeed =
          (this.settings.carousel_rotation_speed || 5) * 1000 // Convert to milliseconds
        this.carouselInterval = setInterval(() => {
          this.nextNotification()
        }, rotationSpeed)
      }
    },

    stopCarouselRotation() {
      if (this.carouselInterval) {
        clearInterval(this.carouselInterval)
        this.carouselInterval = null
      }
    },

    startRefreshInterval() {
      // Refresh notifications every 30 minutes
      this.refreshInterval = setInterval(async () => {
        try {
          await this.fetchNotifications()
        } catch (error) {
          console.warn('Refresh interval error:', error.message)
          // Don't break the interval, just log the error
        }
      }, 1800000) // 30 minutes (30 * 60 * 1000 milliseconds)
    },

    stopRefreshInterval() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    },

    nextNotification() {
      this.currentIndex = (this.currentIndex + 1) % this.notifications.length
    },

    setCurrentIndex(index) {
      this.currentIndex = index
      // Restart carousel rotation
      this.stopCarouselRotation()
      this.startCarouselRotation()
    },

    async copyToClipboard(text, buttonText) {
      try {
        await navigator.clipboard.writeText(text)
        this.$toast.success(this.$t('notifications.copied', { code: text }))
      } catch (error) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$toast.success(this.$t('notifications.copied', { code: text }))
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.notificationTicker {
  position: fixed;
  top: 0; // At the very top
  left: 0;
  width: 100%;
  z-index: 15; // Above header (z-index: 10)
  min-height: em(48); // Consistent height for header positioning

  @include grid('laptop', $down: true) {
    min-height: em(40); // Smaller height on mobile
  }
}

// Ticker styles (scrolling)
.notificationTicker__ticker {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
}

.notificationTicker__scroll {
  padding: em(12) em(16);
  display: flex;
  align-items: center;
  color: inherit; // Ensure text color is inherited
}

.notificationTicker__content {
  animation: scroll-left 30s linear infinite;
  display: flex;
  align-items: center;
  white-space: nowrap;
  color: inherit; // Ensure text color is inherited
}

.notificationTicker__item {
  display: inline-flex;
  align-items: center;
  margin-right: em(40);
  font-size: em(14);
  font-weight: 500;
  color: inherit; // Ensure text color is inherited from parent
}

.notificationTicker__separator {
  margin: 0 em(20);
  opacity: 0.7;
}

// Carousel styles (one at a time)
.notificationTicker__carousel {
  position: relative;
}

.notificationTicker__carouselItem {
  padding: em(16) em(20);
  text-align: center;
  position: relative;
}

.notificationTicker__carouselContent {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: em(12);
}

.notificationTicker__message {
  font-size: em(14);
  font-weight: 500;
  color: inherit; // Ensure text color is inherited from parent
}

.notificationTicker__actions {
  display: flex;
  align-items: center;
  gap: em(12);
}

// Shared button styles
.notificationTicker__copy,
.notificationTicker__link {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: em(4) em(8);
  border-radius: em(4);
  font-size: em(12);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }
}

// Navigation dots
.notificationTicker__dots {
  display: flex;
  justify-content: center;
  gap: em(8);
  margin-top: em(12);
}

.notificationTicker__dot {
  width: em(8);
  height: em(8);
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.2s ease;

  &.active {
    background: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
  }

  &:hover {
    background: rgba(255, 255, 255, 0.6);
  }
}

// Animations
@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

// Responsive design
@include grid('tablet', $down: true) {
  .notificationTicker__carouselContent {
    flex-direction: column;
    gap: em(8);
  }

  .notificationTicker__message {
    font-size: em(13);
  }

  .notificationTicker__copy,
  .notificationTicker__link {
    font-size: em(11);
    padding: em(3) em(6);
  }
}
</style>
