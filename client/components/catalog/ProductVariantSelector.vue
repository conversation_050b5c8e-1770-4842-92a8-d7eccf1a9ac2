<template>
  <div class="productVariantSelector">
    <ProductVariantSelectorColor
      v-if="hasColorChoices"
      v-model="selectedColor"
      :choices="colorChoicesOrderedVariants"
    />
    <ProductVariantSelectorSize
      v-if="hasSizeChoices"
      v-model="selectedSize"
      :choices="sizeChoicesOrderedVariants"
    />
  </div>
</template>

<script>
export default {
  props: {
    value: { type: Object, default: null },
    variants: { type: Array, default: () => [] },
    variantAttributes: { type: Object, default: () => ({}) },
  },
  data: () => ({
    colorChoices: {},
    sizeChoices: {},

    selectedColor: null,
    selectedSize: null,
  }),
  computed: {
    hasColorChoices() {
      return Object.keys(this.colorChoices).length > 0
    },
    hasSizeChoices() {
      return Object.keys(this.sizeChoices).length > 0
    },
    allColorAttributes() {
      return this.variantAttributes.color
        ? this.variantAttributes.color.values.map((x) => x.value)
        : []
    },
    allSizeAttributes() {
      return this.variantAttributes.size
        ? this.variantAttributes.size.values.map((x) => x.value)
        : []
    },
    colorChoicesOrderedVariants() {
      return this.allColorAttributes.reduce((obj, attr) => {
        if (attr in this.colorChoices) obj.push(this.colorChoices[attr])
        return obj
      }, [])
    },
    sizeChoicesOrderedVariants() {
      return this.allSizeAttributes.reduce((obj, attr) => {
        if (attr in this.sizeChoices) obj.push(this.sizeChoices[attr])
        return obj
      }, [])
    },
  },
  watch: {
    selectedColor: {
      deep: true,
      handler: 'handleChange',
    },
    selectedSize: {
      deep: true,
      handler: 'handleChange',
    },
  },
  created() {
    this.variants.forEach((variant) => {
      const colorAttr = variant.combination.attrs.color
      if (colorAttr && !(colorAttr.value in this.colorChoices)) {
        this.colorChoices[colorAttr.value] = variant
      }
      const sizeAttr = variant.combination.attrs.size
      if (sizeAttr && !(sizeAttr.value in this.sizeChoices)) {
        this.sizeChoices[sizeAttr.value] = variant
      }
    })
  },
  mounted() {
    let initialVariant = null

    if (this.variants && this.variants.length && this.$route.query.var) {
      initialVariant = this.variants.find(
        (x) => x.code === this.$route.query.var
      )
    }

    if (initialVariant) {
      // Set color only if the product has color variants and the initial variant has a color
      if (this.hasColorChoices && initialVariant.combination.attrs.color) {
        this.selectedColor = this.colorChoicesOrderedVariants.find(
          (x) =>
            x.combination.attrs.color.value ===
            initialVariant.combination.attrs.color.value
        )
      }
      // Set size only if the product has size variants and the initial variant has a size
      if (this.hasSizeChoices && initialVariant.combination.attrs.size) {
        this.selectedSize = this.sizeChoicesOrderedVariants.find(
          (x) =>
            x.combination.attrs.size.value ===
            initialVariant.combination.attrs.size.value
        )
      }
    } else {
      // Set defaults only for the variant types that exist
      this.selectedColor = this.hasColorChoices
        ? this.colorChoicesOrderedVariants[0] || null
        : null
      this.selectedSize = this.hasSizeChoices
        ? this.sizeChoicesOrderedVariants[0] || null
        : null
    }
  },
  methods: {
    handleChange(value) {
      const colorValue = this.selectedColor
        ? this.selectedColor.combination.attrs.color.value
        : null
      const sizeValue = this.selectedSize
        ? this.selectedSize.combination.attrs.size.value
        : null

      const selectedVariant = this.findSelectedVariant(colorValue, sizeValue)

      // ✅ FIX: Check if the variant code in the URL is different before navigating.
      const newVariantCode = selectedVariant ? selectedVariant.code : null
      if (this.$route.query.var !== newVariantCode) {
        this.$router
          .replace({
            query: {
              ...this.$route.query,
              var: newVariantCode,
            },
          })
          .catch((err) => {
            // Catch the error to prevent it from appearing in the console, just in case.
            if (err.name !== 'NavigationDuplicated') {
              throw err
            }
          })
      }

      this.$emit('input', selectedVariant)
    },
    findSelectedVariant(colorValue, sizeValue) {
      for (const variant of this.variants) {
        const variantAttrs = variant.combination.attrs

        // prettier-ignore
        if (this.hasColorChoices && this.hasSizeChoices) {
          if (colorValue && sizeValue && colorValue === variantAttrs.color.value && sizeValue === variantAttrs.size.value) return variant
        } else if (this.hasColorChoices) {
          if (colorValue && colorValue === variantAttrs.color.value) return variant
        } else if (this.hasSizeChoices) {
          if (sizeValue && sizeValue === variantAttrs.size.value) return variant
        }
      }
      return null
    },
  },
}
</script>
