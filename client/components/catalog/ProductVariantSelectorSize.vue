<template>
  <div class="productSizes">
    <ul class="productSizes__list">
      <li
        v-for="variant in choices"
        :key="variant.id"
        class="productSizes__list-item"
      >
        <div
          class="productSize"
          :class="{
            isCurrent: isSelected(variant),
          }"
        >
          <Button class="productSize__btn" @click="$emit('input', variant)">
            <span class="productSize__label">
              {{ variant.combination.attrs.size.value_label.toUpperCase() }}
              <span class="productSize__line"></span>
            </span>
          </Button>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    value: { type: Object, default: null },
    choices: { type: Array, required: true },
  },
  methods: {
    isSelected(variant) {
      return this.value && variant.id === this.value.id
    },
  },
}
</script>

<style lang="scss">
.productSizes {
  padding: em(7);
  margin-top: em(22);
  margin-bottom: em(7);
  text-align: center;
}
.productSizes__list {
  display: flex;
  column-gap: em(2);

  @include grid('laptop', $down: true) {
    justify-content: center;
  }
}
.productSize__btn {
  padding: em(8) em(10) em(10);

  .productSize.isCurrent & {
    font-weight: var(--text-bold);
  }

  .productSize.outOfStock & {
    color: var(--gray-b3);
  }
}

.productSize__label {
  position: relative;
  display: block;
}

.productSize__line {
  color: currentColor;
  background-color: currentColor;
  width: 100%;
  height: em(2);
  opacity: 0;
  position: absolute;
  top: 100%;
  left: 0;

  .productSize.isCurrent & {
    opacity: 1;
    color: var(--page-font-color);
  }

  .productSize.outOfStock & {
    opacity: 1;
    top: 50%;
    margin-top: em(-1);
    height: em(1);
  }
}
</style>
