<template>
  <div class="productAddToCart">
    <Form :action="addProduct">
      <FormButton :force-invalid="!selectedVariant || !isAvailable">
        <template #error>
          <span v-if="!isAvailable">
            {{ $t('generic.notAvailable') }}
          </span>
          <span v-else>{{ $t('productPage.selectVariant.buttonLabel') }}</span>
        </template>
        <template #default>
          {{ $t('productPage.addToCart.buttonLabel') }}
        </template>
      </FormButton>
    </Form>
  </div>
</template>

<script>
export default {
  props: {
    selectedVariant: {
      type: Object,
      default: null,
    },
  },
  computed: {
    isAvailable() {
      if (this.selectedVariant)
        return this.selectedVariant.available_quantity > 0
      return false
    },
  },
  methods: {
    async addProduct() {
      if (!this.selectedVariant) return

      await this.$store.dispatch(
        'basket/addProductToBasket',
        this.selectedVariant
      )
      this.$store.commit('ui/setCartOpen', true)
      const data = { detail: this.$t('cart.added') }
      return { data }
    },
  },
}
</script>

<style lang="scss">
.productAddToCart {
  margin-top: em(7);
  margin-bottom: em(12);
}
</style>
