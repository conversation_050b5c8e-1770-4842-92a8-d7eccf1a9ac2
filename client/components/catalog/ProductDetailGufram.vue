<template>
  <div v-if="page" class="page-wrapper">
    <div class="product-page-container">
      <div class="gallery-container">
        <div class="main-image-wrapper">
          <transition name="fade" mode="out-in">
            <img
              :key="activeImage.url"
              :src="activeImage.url"
              :alt="activeImage.alt"
              class="main-product-image"
            />
          </transition>
        </div>
        <div v-if="variantImages.length > 1" class="image-thumbnail-nav">
          <div
            v-for="(image, index) in variantImages"
            :key="image.large.url"
            class="nav-thumbnail"
            :class="{ active: index === activeImageIndex }"
            @click="selectImage(index)"
          >
            <img :src="image.small.url" :alt="image.small.alt" />
          </div>
        </div>
      </div>

      <div class="details-container">
        <div class="details-content-scroll">
          <div v-if="collectionName" class="collection-name-wrapper">
            <label class="selector-label">{{
              $t('productPage.collection.label')
            }}</label>
            <p>{{ collectionName }}</p>
          </div>

          <h1 class="product-title">{{ page.title }}</h1>

          <p v-if="selectedVariant" class="product-price">
            <span
              v-if="
                selectedVariant.old_price &&
                selectedVariant.old_price.startsWith('0.00') === false
              "
              class="old-price"
            >
              {{ selectedVariant.old_price }}
            </span>
            {{ selectedVariant.price }}
          </p>

          <div v-if="hasColorVariants" class="selector-group">
            <label class="selector-label"
              >{{ $t('productPage.color.label') }}:
              <span>{{ selectedColor }}</span></label
            >
            <div class="color-selector">
              <div
                v-for="color in uniqueColors"
                :key="color.name"
                class="color-swatch"
                :class="{ active: color.name === selectedColor }"
                @click="selectColor(color.name)"
              >
                <img :src="color.image" :alt="color.name" />
              </div>
            </div>
          </div>

          <div v-if="hasSizeVariants" class="selector-group">
            <label class="selector-label">{{
              $t('productPage.size.label')
            }}</label>
            <div class="size-selector">
              <button
                v-for="size in availableSizes"
                :key="size"
                class="size-button"
                :class="{ active: size === selectedSize }"
                @click="selectSize(size)"
              >
                {{ size }}
              </button>
            </div>
          </div>

          <button
            class="add-to-cart-button"
            :disabled="isAddingToCart || !isAvailable"
            @click="addToCart"
          >
            <span v-if="isAddingToCart">{{
              $t('productPage.addToCart.loading')
            }}</span>
            <span v-else-if="!isAvailable">{{
              $t('generic.notAvailable')
            }}</span>
            <span v-else>{{ $t('productPage.addToCart.buttonLabel') }}</span>
          </button>

          <div class="accordion-section">
            <div class="accordion-item">
              <button
                class="accordion-header"
                @click="toggleAccordion('details')"
              >
                <span>{{
                  $t('productPage.productDetails.productDetailsLabel')
                }}</span>
                <span
                  class="accordion-icon"
                  :class="{ toggled: activeAccordion === 'details' }"
                ></span>
              </button>
              <div
                v-if="activeAccordion === 'details'"
                class="accordion-content"
                v-html="page.details"
              ></div>
            </div>
            <div v-if="page.guides" class="accordion-item">
              <button
                class="accordion-header"
                @click="toggleAccordion('shipping')"
              >
                <span>{{
                  $t('productPage.productDetails.productGuidesLabel')
                }}</span>
                <span
                  class="accordion-icon"
                  :class="{ toggled: activeAccordion === 'shipping' }"
                ></span>
              </button>
              <div
                v-if="activeAccordion === 'shipping'"
                class="accordion-content"
                v-html="page.guides"
              ></div>
            </div>
          </div>

          <!-- Related Products Section -->
          <div v-if="relatedProducts.length" class="related-products-section">
            <h2 class="related-products-title">
              {{ $t('homePage.relatedProducts.title') }}
            </h2>
            <div class="related-products-grid">
              <NLink
                v-for="product in relatedProducts"
                :key="product.id"
                :to="product.url_path"
                class="related-product-item"
              >
                <div class="related-product-image-wrapper">
                  <img
                    v-if="product.images && product.images.length > 0"
                    :src="product.images[0].medium.url"
                    :alt="product.title"
                    class="related-product-image"
                  />
                  <div v-else class="related-product-placeholder">
                    <span>No Image</span>
                  </div>
                </div>
                <div class="related-product-info">
                  <h3 class="related-product-title">{{ product.title }}</h3>
                  <p v-if="product.price" class="related-product-price">
                    <span
                      v-if="
                        product.old_price &&
                        !product.old_price.startsWith('0.00')
                      "
                      class="related-product-old-price"
                    >
                      {{ product.old_price }}
                    </span>
                    {{ product.price }}
                  </p>
                </div>
              </NLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductDetailFinal',
  props: {
    page: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      selectedColor: null,
      selectedSize: null,
      activeImageIndex: 0,
      activeAccordion: 'details',
      slideshowInterval: null,
      isAddingToCart: false,
    }
  },
  computed: {
    collectionName() {
      if (!this.page || !this.page.url_path) return ''
      const pathParts = this.page.url_path.split('/')
      if (pathParts.length > 3 && pathParts[2] === 'colecciones') {
        const slug = pathParts[3]
        return slug
          .split('-')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
      }
      return ''
    },
    hasColorVariants() {
      if (!this.page || !this.page.variants) return false
      return this.page.variants.some(
        (variant) => variant.combination.attrs.color !== undefined
      )
    },
    hasSizeVariants() {
      if (!this.page || !this.page.variants) return false
      return this.page.variants.some(
        (variant) => variant.combination.attrs.size !== undefined
      )
    },
    uniqueColors() {
      if (!this.hasColorVariants) return []

      // Get the ordered color attributes from variant_attributes
      const orderedColors =
        this.page.variant_attributes?.color?.values?.map((v) => v.value) || []

      // Build a map of available colors from variants
      const colors = new Map()
      this.page.variants.forEach((variant) => {
        const colorAttr = variant.combination.attrs.color
        if (colorAttr) {
          const colorName = colorAttr.value
          if (!colors.has(colorName)) {
            colors.set(colorName, {
              name: colorName,
              image: variant.images[0].small.url,
            })
          }
        }
      })

      // Return colors in the order specified by variant_attributes
      return orderedColors
        .filter((colorValue) => colors.has(colorValue))
        .map((colorValue) => colors.get(colorValue))
    },
    availableSizesForColor() {
      if (!this.hasSizeVariants || !this.selectedColor) return []

      // Get the ordered size attributes from variant_attributes
      const orderedSizes =
        this.page.variant_attributes?.size?.values?.map((v) => v.value) || []

      // Get available sizes for the selected color
      const availableSizes = new Set(
        this.page.variants
          .filter((v) => {
            const colorAttr = v.combination.attrs.color
            return colorAttr && colorAttr.value === this.selectedColor
          })
          .map((v) => v.combination.attrs.size.value)
      )

      // Return sizes in the order specified by variant_attributes
      return orderedSizes.filter((size) => availableSizes.has(size))
    },
    availableSizes() {
      if (!this.hasSizeVariants) return []

      // For size-only products (no color variants)
      if (!this.hasColorVariants) {
        const orderedSizes =
          this.page.variant_attributes?.size?.values?.map((v) => v.value) || []
        const availableSizes = new Set(
          this.page.variants.map((v) => v.combination.attrs.size.value)
        )
        return orderedSizes.filter((size) => availableSizes.has(size))
      }

      // For products with both color and size variants
      return this.availableSizesForColor
    },
    selectedVariant() {
      if (
        !this.page ||
        !this.page.variants ||
        this.page.variants.length === 0
      ) {
        return null
      }

      // For products without color/size variants, just return the first variant
      if (!this.hasColorVariants && !this.hasSizeVariants) {
        return this.page.variants[0]
      }

      // For size-only products
      if (!this.hasColorVariants && this.hasSizeVariants) {
        if (!this.selectedSize) {
          return this.page.variants[0]
        }
        return (
          this.page.variants.find((v) => {
            const sizeAttr = v.combination.attrs.size
            return sizeAttr && sizeAttr.value === this.selectedSize
          }) || this.page.variants[0]
        )
      }

      // For color-only products
      if (this.hasColorVariants && !this.hasSizeVariants) {
        if (!this.selectedColor) {
          return this.page.variants[0]
        }
        return (
          this.page.variants.find((v) => {
            const colorAttr = v.combination.attrs.color
            return colorAttr && colorAttr.value === this.selectedColor
          }) || this.page.variants[0]
        )
      }

      // For products with both color and size variants
      if (!this.selectedColor || !this.selectedSize) {
        return this.page.variants[0]
      }
      return (
        this.page.variants.find((v) => {
          const colorAttr = v.combination.attrs.color
          const sizeAttr = v.combination.attrs.size
          return (
            colorAttr &&
            sizeAttr &&
            colorAttr.value === this.selectedColor &&
            sizeAttr.value === this.selectedSize
          )
        }) || this.page.variants[0]
      )
    },
    variantImages() {
      if (!this.page) return []

      if (
        this.selectedVariant &&
        this.selectedVariant.images &&
        this.selectedVariant.images.length > 0
      ) {
        return this.selectedVariant.images
      }
      // Fall back to main product images for size-only products or when variant has no images
      return this.page.images || []
    },
    activeImage() {
      if (!this.variantImages || this.variantImages.length === 0) {
        return { url: '', alt: '' }
      }

      // Ensure activeImageIndex is within bounds
      const imageIndex = Math.min(
        this.activeImageIndex,
        this.variantImages.length - 1
      )
      const image = this.variantImages[imageIndex]

      return image ? image.large : { url: '', alt: '' }
    },
    isAvailable() {
      if (this.selectedVariant) {
        return this.selectedVariant.available_quantity > 0
      }
      return false
    },
    relatedProducts() {
      return (this.page && this.page.related_products) || []
    },
  },
  watch: {
    selectedVariant(newVariant, oldVariant) {
      if (newVariant && (!oldVariant || newVariant.id !== oldVariant.id)) {
        this.activeImageIndex = 0
        this.startSlideshow()
        if (newVariant.code) {
          this.$router
            .replace({
              query: { ...this.$route.query, var: newVariant.code },
            })
            .catch((err) => {
              if (err.name !== 'NavigationDuplicated') throw err
            })
        }
      }
    },
    variantImages(newImages) {
      // Reset image index if it's out of bounds for the new image set
      if (
        newImages &&
        newImages.length > 0 &&
        this.activeImageIndex >= newImages.length
      ) {
        this.activeImageIndex = 0
      }
    },
  },
  created() {
    this.initializeVariant()
  },
  mounted() {
    this.startSlideshow()
  },
  beforeDestroy() {
    this.stopSlideshow()
  },
  methods: {
    initializeVariant() {
      const variantCodeFromUrl = this.$route.query.var
      let initialVariant = null

      if (variantCodeFromUrl) {
        initialVariant = this.page.variants.find(
          (v) => v.code === variantCodeFromUrl
        )
      }
      if (!initialVariant && this.page.variants.length > 0) {
        // Instead of just taking the first variant in the array,
        // find the first variant according to the proper attribute order
        initialVariant = this.getFirstVariantInOrder()
      }
      if (initialVariant) {
        // Only set color and size if the variant has these attributes
        const colorAttr = initialVariant.combination.attrs.color
        const sizeAttr = initialVariant.combination.attrs.size

        if (colorAttr) {
          this.selectedColor = colorAttr.value
        }
        if (sizeAttr) {
          this.selectedSize = sizeAttr.value
        }
      }

      // For size-only products, ensure we have a size selected
      if (
        !this.hasColorVariants &&
        this.hasSizeVariants &&
        !this.selectedSize
      ) {
        const firstAvailableSize = this.availableSizes[0]
        if (firstAvailableSize) {
          this.selectedSize = firstAvailableSize
        }
      }

      // For color-only products, ensure we have a color selected
      if (
        this.hasColorVariants &&
        !this.hasSizeVariants &&
        !this.selectedColor
      ) {
        const firstAvailableColor = this.uniqueColors[0]
        if (firstAvailableColor) {
          this.selectedColor = firstAvailableColor.name
        }
      }
    },
    selectColor(color) {
      this.selectedColor = color

      // Try to keep the current size if it's available for the new color
      const currentSize = this.selectedSize
      const availableSizesForNewColor = this.availableSizes

      if (currentSize && availableSizesForNewColor.includes(currentSize)) {
        // Keep the current size if it's available for the new color
        this.selectedSize = currentSize
      } else {
        // Fall back to the first available size for the new color
        this.selectedSize = availableSizesForNewColor[0]
      }
    },
    selectSize(size) {
      this.selectedSize = size
    },
    selectImage(index) {
      this.stopSlideshow()
      this.activeImageIndex = index
    },
    async addToCart() {
      if (!this.selectedVariant) {
        console.error('No variant selected to add to cart.')
        if (this.$toast) {
          this.$toast.error(this.$t('cart.addError.noVariant'))
        }
        return
      }

      if (!this.isAvailable) {
        console.error('Selected variant is not available.')
        if (this.$toast) {
          this.$toast.error(this.$t('generic.notAvailable'))
        }
        return
      }

      // Show loading state
      this.isAddingToCart = true

      try {
        const result = await this.$store.dispatch(
          'basket/addProductToBasket',
          this.selectedVariant
        )

        if (result.success) {
          // Open cart panel on success for both desktop and mobile
          // The cart panel now works on mobile too
          this.$store.commit('ui/setCartOpen', true)
        } else {
          // Error handling is already done in the store action
          // Don't open the cart panel on error
          console.error('Failed to add product to cart:', result.error)
        }
      } finally {
        // Hide loading state
        this.isAddingToCart = false
      }
    },
    toggleAccordion(section) {
      this.activeAccordion = this.activeAccordion === section ? null : section
    },
    startSlideshow() {
      this.stopSlideshow()
      if (this.variantImages.length > 1) {
        this.slideshowInterval = setInterval(() => {
          this.activeImageIndex =
            (this.activeImageIndex + 1) % this.variantImages.length
        }, 4000)
      }
    },
    stopSlideshow() {
      clearInterval(this.slideshowInterval)
      this.slideshowInterval = null
    },
    getFirstVariantInOrder() {
      // Get the first variant according to the proper attribute order
      // This ensures we select XS instead of S when no variant is specified

      // For size-only products
      if (!this.hasColorVariants && this.hasSizeVariants) {
        const orderedSizes =
          this.page.variant_attributes?.size?.values?.map((v) => v.value) || []
        for (const size of orderedSizes) {
          const variant = this.page.variants.find(
            (v) =>
              v.combination.attrs.size &&
              v.combination.attrs.size.value === size
          )
          if (variant) return variant
        }
      }

      // For color-only products
      if (this.hasColorVariants && !this.hasSizeVariants) {
        const orderedColors =
          this.page.variant_attributes?.color?.values?.map((v) => v.value) || []
        for (const color of orderedColors) {
          const variant = this.page.variants.find(
            (v) =>
              v.combination.attrs.color &&
              v.combination.attrs.color.value === color
          )
          if (variant) return variant
        }
      }

      // For products with both color and size variants
      if (this.hasColorVariants && this.hasSizeVariants) {
        const orderedColors =
          this.page.variant_attributes?.color?.values?.map((v) => v.value) || []
        const orderedSizes =
          this.page.variant_attributes?.size?.values?.map((v) => v.value) || []

        // Find the first color-size combination in order
        for (const color of orderedColors) {
          for (const size of orderedSizes) {
            const variant = this.page.variants.find(
              (v) =>
                v.combination.attrs.color &&
                v.combination.attrs.color.value === color &&
                v.combination.attrs.size &&
                v.combination.attrs.size.value === size
            )
            if (variant) return variant
          }
        }
      }

      // Fallback to the first variant in the array if no ordered selection works
      return this.page.variants[0]
    },
  },
}
</script>

<style lang="scss" scoped>
// Main container with max-width for large screens
.page-wrapper {
  width: 100%;
}
.product-page-container {
  display: grid;
  width: 100%;
  min-height: 100vh;
  padding-top: em(115);

  // Constrain width and center on large screens
  max-width: 1800px;
  margin: 0 auto;

  @include grid('laptop') {
    grid-template-columns: 2fr 1fr;
  }
  @include grid('laptop', $down: true) {
    grid-template-columns: 1fr;
    padding-top: em(80);
  }
}

// Left Side
.gallery-container {
  position: sticky;
  top: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  border-right: 1px solid #e0e0e0;
  padding-top: em(115);

  @include grid('laptop', $down: true) {
    position: relative;
    height: auto;
    min-height: 60vh;
    border-right: none;
    padding-top: 0;
  }
}

.main-image-wrapper {
  width: 100%;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-product-image {
  max-width: 100%;
  max-height: 75vh;
  object-fit: contain;
}

.image-thumbnail-nav {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding-bottom: 2rem;
}

.nav-thumbnail {
  width: 60px;
  height: 60px;
  margin: 0 8px;
  border: 2px solid transparent;
  cursor: pointer;
  opacity: 0.6;
  transition: all 0.3s ease;
  &:hover {
    opacity: 1;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  &.active {
    border-color: #000;
    opacity: 1;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// Right Side
.details-container {
  overflow-y: auto;
  padding: 0 4rem 4rem 4rem;
}

.collection-name-wrapper {
  margin-bottom: 0.5rem;
  .selector-label {
    margin-bottom: 0;
  }
  p {
    font-size: em(16);
    font-weight: var(--text-bold);
  }
}

.product-title {
  font-family: var(--font-family-title);
  font-size: em(18);
  font-weight: var(--text-bold, 700);
  text-transform: uppercase;
  margin: 0 0 1rem 0;
  line-height: 1.1;
}

.product-price {
  font-size: em(22);
  margin-bottom: 3rem;
  .old-price {
    text-decoration: line-through;
    color: #999;
    margin-right: 0.75rem;
    font-size: em(18);
  }
}

.selector-group {
  margin-bottom: 2rem;
}

.selector-label {
  display: block;
  margin-bottom: 1rem;
  font-size: em(12);
  color: #555;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  span {
    color: #000;
    font-weight: var(--text-bold, 700);
  }
}

.color-selector {
  display: flex;
  flex-wrap: wrap;
}

.color-swatch {
  width: 52px;
  height: 52px;
  margin: 0 10px 10px 0;
  cursor: pointer;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  &:hover {
    border-color: #999;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  &.active {
    border-color: #000;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.size-selector {
  display: flex;
  flex-wrap: wrap;
}

.size-button {
  min-width: 52px;
  height: 52px;
  padding: 0 1rem;
  margin: 0 10px 10px 0;
  border: 1px solid #000;
  background-color: #fff;
  color: #000;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: em(16);
  &:hover {
    background-color: #000;
    color: #fff;
  }
  &.active {
    background-color: #000;
    color: #fff;
  }
}

.add-to-cart-button {
  max-width: 400px;
  background-color: #000;
  color: #fff;
  border: 1px solid #000;
  padding: 1.25rem;
  font-size: em(16);
  text-transform: uppercase;
  cursor: pointer;
  margin-bottom: 3rem;
  transition: all 0.3s ease;
  &:hover:not(:disabled) {
    background-color: #333;
    border-color: #333;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #ccc;
    border-color: #ccc;
    color: #666;
  }
}

.accordion-section {
  border-top: 1px solid #e0e0e0;
  max-width: 650px;
}
.accordion-item {
  border-bottom: 1px solid #e0e0e0;
}
.accordion-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: none;
  border: none;
  text-align: left;
  padding: 1.5rem 0;
  font-size: em(14);
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: var(--text-bold, 700);
}
.accordion-icon {
  position: relative;
  width: 14px;
  height: 14px;
  &::before,
  &::after {
    content: '';
    position: absolute;
    background-color: #000;
    transition: transform 0.3s ease;
  }
  &::before {
    // horizontal bar
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    margin-top: -1px;
  }
  &::after {
    // vertical bar
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    margin-left: -1px;
  }
  &.toggled::after {
    transform: rotate(90deg);
  }
}

.accordion-content {
  padding-bottom: 1.5rem;
  font-size: em(14);
  line-height: 1.7;
  color: #333;
  ::v-deep ul {
    padding-left: 20px;
    margin-top: 1rem;
  }
  ::v-deep li {
    margin-bottom: 0.5rem;
  }
  ::v-deep a {
    color: #000;
    text-decoration: underline;
  }
}

// Related Products Section
.related-products-section {
  margin-top: 4rem;
  padding-top: 3rem;
  border-top: 1px solid #e0e0e0;
  max-width: 650px;
}

.related-products-title {
  font-family: var(--font-family-title);
  font-size: em(18);
  font-weight: var(--text-bold, 700);
  text-transform: uppercase;
  margin: 0 0 2rem 0;
  letter-spacing: 0.05em;
}

.related-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;

  @include grid('tablet') {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  @include grid('laptop') {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

.related-product-item {
  display: block;
  text-decoration: none;
  color: inherit;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
}

.related-product-image-wrapper {
  position: relative;
  width: 100%;
  aspect-ratio: 4/5;
  overflow: hidden;
  background-color: #f8f8f8;
  margin-bottom: 0.75rem;
}

.related-product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  .related-product-item:hover & {
    transform: scale(1.05);
  }
}

.related-product-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #999;
  font-size: em(12);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.related-product-info {
  text-align: left;
}

.related-product-title {
  font-size: em(13);
  font-weight: var(--text-medium, 500);
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  text-transform: uppercase;
  letter-spacing: 0.02em;
}

.related-product-price {
  font-size: em(14);
  margin: 0;
  font-weight: var(--text-bold, 700);
}

.related-product-old-price {
  text-decoration: line-through;
  color: #999;
  margin-right: 0.5rem;
  font-size: em(12);
  font-weight: normal;
}

// Transition
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
