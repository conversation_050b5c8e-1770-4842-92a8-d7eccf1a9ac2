<template>
  <div>
    <div class="flow-container">
      <a
        v-for="(product, index) in initialProducts"
        :key="product.id + '-' + index"
        href="#"
        class="flow-item"
        :aria-label="`View product: ${product.title}`"
        @click.prevent="handleProductClick($event)"
      >
        <TransparentImage :src="product.images[0].url" :alt="product.title" />
      </a>
    </div>
  </div>
</template>

<script>
import { gsap } from 'gsap'

export default {
  name: 'ProductFlow3D',

  props: {
    products: {
      type: Array,
      required: true,
    },
    hiddenItemIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isMobile: false,
      maxConcurrentItems: 12,
      nextProductIndex: 0,
      previousProductIndex: 0,
      renderTicker: null,
      handleMouseWheel: null,
      handleResize: null,
      resizeTimeout: null,
      filteredProducts: [],
      itemStates: [],
      gridLanes: {},
      // MODIFIED: Sequence is now flattened. Each lane is its own group to increase spacing.
      animationSequence: [
        ['h1'],
        ['l4'],
        ['h4'],
        ['l1'],
        ['m1'],
        ['m4'],
        ['h2'],
        ['l3'],
        ['h3'],
        ['l2'],
        ['m2'],
        ['m3'],
      ],
      currentGroupIndex: 0,
      currentLaneInGroupIndex: 0,
      touchStartY: 0,
      flowState: null,
      handleTouchStart: null,
      handleTouchMove: null,
      handleTouchEnd: null,
    }
  },
  computed: {
    initialProducts() {
      if (!this.filteredProducts.length) return []
      const count = this.isMobile ? 8 : this.maxConcurrentItems
      return this.filteredProducts.slice(0, count)
    },
  },
  mounted() {
    this.filteredProducts = this.products.filter(
      (p) => p.url_path && p.images?.length > 0
    )
    this.setupComponent()
    this.handleResize = () => {
      clearTimeout(this.resizeTimeout)
      this.resizeTimeout = setTimeout(() => this.setupComponent(true), 250)
    }
    window.addEventListener('resize', this.handleResize)
    this.$nextTick(() => {
      this.initAnimation()
    })
  },
  beforeDestroy() {
    gsap.ticker.remove(this.renderTicker)
    window.removeEventListener('wheel', this.handleMouseWheel)
    window.removeEventListener('resize', this.handleResize)
    window.removeEventListener('touchstart', this.handleTouchStart)
    window.removeEventListener('touchmove', this.handleTouchMove)
    window.removeEventListener('touchend', this.handleTouchEnd)
  },
  methods: {
    setupComponent(isResizing = false) {
      this.isMobile = window.innerWidth <= 768
      this.maxConcurrentItems = this.isMobile ? 8 : 12

      const xSpread = this.isMobile ? 0.5 : 0.6
      const ySpread = this.isMobile ? 0.5 : 0.5

      const xPositions = [
        -window.innerWidth * xSpread,
        -window.innerWidth * (xSpread / 3),
        window.innerWidth * (xSpread / 3),
        window.innerWidth * xSpread,
      ]
      const yPositions = [
        -window.innerHeight * ySpread,
        0,
        window.innerHeight * ySpread,
      ]

      this.gridLanes = {
        h1: { x: xPositions[0], y: yPositions[0] },
        h2: { x: xPositions[1], y: yPositions[0] },
        h3: { x: xPositions[2], y: yPositions[0] },
        h4: { x: xPositions[3], y: yPositions[0] },
        m1: { x: xPositions[0], y: yPositions[1] },
        m2: { x: xPositions[1], y: yPositions[1] },
        m3: { x: xPositions[2], y: yPositions[1] },
        m4: { x: xPositions[3], y: yPositions[1] },
        l1: { x: xPositions[0], y: yPositions[2] },
        l2: { x: xPositions[1], y: yPositions[2] },
        l3: { x: xPositions[2], y: yPositions[2] },
        l4: { x: xPositions[3], y: yPositions[2] },
      }

      if (isResizing && this.itemStates.length) {
        this.itemStates.forEach((state) => {
          const lane = this.gridLanes[state.laneKey]
          if (lane) {
            state.x = lane.x
            state.y = lane.y
          }
        })
      }
    },
    mod(n, m) {
      return ((n % m) + m) % m
    },
    getProductUrl(product) {
      // Skip URL modification for special items (logo, banner)
      if (product.type === 'logo' || product.type === 'banner') {
        return product.url_path
      }

      // Use the product URL as-is - don't modify it
      return product.url_path
    },
    handleProductClick(event) {
      // Find the current product from the element's state (after recycling)
      const clickedElement = event.currentTarget
      const elementState = this.itemStates?.find(
        (state) => state.el === clickedElement
      )
      const product = elementState?.product

      if (!product) {
        return
      }

      // Skip navigation for non-product items (banners, logos, etc.)
      if (
        product.url_path === '#' ||
        product.type === 'banner' ||
        product.type === 'logo'
      ) {
        return
      }

      // Add a small delay to ensure the click isn't interfered with by animations
      setTimeout(() => {
        window.location.href = product.url_path
      }, 100)
    },
    getNextLane() {
      const group = this.animationSequence[this.currentGroupIndex]
      const laneKey = group[this.currentLaneInGroupIndex]

      this.currentLaneInGroupIndex++
      if (this.currentLaneInGroupIndex >= group.length) {
        this.currentLaneInGroupIndex = 0
        this.currentGroupIndex++
        if (this.currentGroupIndex >= this.animationSequence.length) {
          this.currentGroupIndex = 0
        }
      }
      return { ...this.gridLanes[laneKey], key: laneKey }
    },
    getPreviousLane() {
      this.currentLaneInGroupIndex--
      if (this.currentLaneInGroupIndex < 0) {
        this.currentGroupIndex--
        if (this.currentGroupIndex < 0) {
          this.currentGroupIndex = this.animationSequence.length - 1
        }
        const prevGroup = this.animationSequence[this.currentGroupIndex]
        this.currentLaneInGroupIndex = prevGroup.length - 1
      }

      const group = this.animationSequence[this.currentGroupIndex]
      const laneKey = group[this.currentLaneInGroupIndex]
      return { ...this.gridLanes[laneKey], key: laneKey }
    },
    initAnimation() {
      const items = this.$el.querySelectorAll('.flow-item')
      if (!items.length) return

      const productCount = this.filteredProducts.length
      this.nextProductIndex = Math.min(items.length, productCount)
      this.previousProductIndex = this.mod(
        this.nextProductIndex - 1,
        productCount
      )

      this.currentGroupIndex = 0
      this.currentLaneInGroupIndex = 0

      gsap.set(items, { transformOrigin: '50% 50%' })

      this.flowState = {
        velocity: 0,
        zRange: { min: -6000, max: 1000 },
        initialZRange: () => gsap.utils.random(-6000, 500),
        recycleZRange: () => gsap.utils.random(-6000, -4500),
      }

      this.itemStates = Array.from(items, (item, index) => {
        const productIndex = this.mod(index, productCount)
        const product = this.filteredProducts[productIndex]
        const lane = this.getNextLane()

        let initialZ = this.flowState.initialZRange()
        item.classList.remove('is-banner-item')

        if (product.type === 'logo') {
          initialZ = 900
        } else if (product.type === 'banner') {
          initialZ = 800
          item.classList.add('is-banner-item')
        }

        return {
          el: item,
          product,
          x: lane.x,
          y: lane.y,
          z: initialZ,
          laneKey: lane.key,
          isApproaching: false,
          fadeInProgress: 0,
        }
      })

      gsap.from(items, {
        autoAlpha: 0,
        scale: 0.8,
        duration: 0.8,
        ease: 'power2.out',
        stagger: 0.07,
      })

      this.renderTicker = () => {
        this.flowState.velocity *= 0.95
        this.itemStates.forEach((state) => {
          // MODIFIED: Slowed down movement speed for longer display time
          state.z += this.flowState.velocity + 3.5

          if (state.z > 100 && !state.isApproaching) {
            if (
              state.product.type === 'logo' ||
              state.product.type === 'banner'
            ) {
              this.$emit('item-approaching', state.product)
              state.isApproaching = true
            }
          }

          if (state.z > this.flowState.zRange.max) {
            const nextProduct = this.filteredProducts[this.nextProductIndex]
            if (!nextProduct) return

            this.updateItemContent(state, nextProduct)
            state.isApproaching = false
            state.fadeInProgress = 0
            this.nextProductIndex = (this.nextProductIndex + 1) % productCount
            this.previousProductIndex = this.mod(
              this.previousProductIndex + 1,
              productCount
            )

            const nextLane = this.getNextLane()
            state.laneKey = nextLane.key
            state.x = nextLane.x
            state.y = nextLane.y
            state.z = this.flowState.recycleZRange()
          } else if (state.z < this.flowState.zRange.min) {
            const prevProduct = this.filteredProducts[this.previousProductIndex]
            if (!prevProduct) return

            this.updateItemContent(state, prevProduct)
            state.isApproaching = false
            state.fadeInProgress = 0
            this.previousProductIndex = this.mod(
              this.previousProductIndex - 1,
              productCount
            )
            this.nextProductIndex = this.mod(
              this.nextProductIndex - 1,
              productCount
            )

            const prevLane = this.getPreviousLane()
            state.laneKey = prevLane.key
            state.x = prevLane.x
            state.y = prevLane.y
            state.z = this.flowState.zRange.max
          }

          // NEW: Add a Z-offset to stagger items based on their row (H, M, L)
          let zOffset = 0
          const row = state.laneKey.charAt(0)
          if (row === 'h') {
            zOffset = 150 // Top row items are slightly forward
          } else if (row === 'l') {
            zOffset = -150 // Bottom row items are slightly back
          }

          // Update fade-in progress for smooth transitions
          if (state.fadeInProgress < 1) {
            state.fadeInProgress = Math.min(1, state.fadeInProgress + 0.02)
          }

          const isHidden = this.hiddenItemIds.includes(state.product.id)
          const zWithOffset = state.z + zOffset
          gsap.set(state.el, {
            x: state.x,
            y: state.y,
            z: zWithOffset, // Apply the stagger offset here
            opacity: isHidden
              ? 0
              : Math.max(
                  0,
                  Math.min(
                    1,
                    (gsap.utils.mapRange(-6000, -4500, 0, 0.3, state.z) +
                      gsap.utils.mapRange(-4500, -2000, 0.3, 1, state.z)) *
                      state.fadeInProgress
                  )
                ),
            scale: gsap.utils.mapRange(-6000, 1000, 0.6, 1.2, state.z),
            zIndex: Math.floor(
              gsap.utils.mapRange(-6000, 1000, 1, 1000, zWithOffset)
            ),
            pointerEvents: isHidden ? 'none' : 'auto',
          })
        })
      }

      this.handleMouseWheel = (e) => {
        this.flowState.velocity += e.deltaY * 0.1
        this.flowState.velocity = Math.max(
          -50,
          Math.min(50, this.flowState.velocity)
        )
      }

      this.handleTouchStart = (e) => {
        if (e.touches.length) {
          this.touchStartY = e.touches[0].clientY
        }
      }

      this.handleTouchMove = (e) => {
        if (e.touches.length && this.touchStartY && this.flowState) {
          // Only prevent default if the user is actually dragging (moved more than 10px)
          const currentY = e.touches[0].clientY
          const deltaY = Math.abs(this.touchStartY - currentY)

          if (deltaY > 10) {
            e.preventDefault()
            const signedDeltaY = this.touchStartY - currentY
            this.flowState.velocity += signedDeltaY * 0.35
            this.flowState.velocity = Math.max(
              -50,
              Math.min(50, this.flowState.velocity)
            )
            this.touchStartY = currentY
          }
        }
      }

      this.handleTouchEnd = () => {
        this.touchStartY = 0
      }

      window.addEventListener('wheel', this.handleMouseWheel)
      window.addEventListener('touchstart', this.handleTouchStart, {
        passive: true,
      })
      window.addEventListener('touchmove', this.handleTouchMove, {
        passive: false,
      })
      window.addEventListener('touchend', this.handleTouchEnd)

      gsap.ticker.add(this.renderTicker)
    },
    updateItemContent(state, product) {
      state.product = product
      state.el.setAttribute('aria-label', `View product: ${product.title}`)

      // Update the link URL
      const productUrl = this.getProductUrl(product)
      if (state.el.href !== undefined) {
        state.el.href = productUrl
      }

      const img = state.el.querySelector('.flow-item-image')
      if (img) {
        const originalUrl = product.images[0].url
        // Use original image directly since backgrounds are already removed
        img.src = originalUrl
      }
    },
  },
}
</script>

<style lang="scss" scoped>
/* STYLES remain the same */
.flow-container {
  --item-size-desktop: 55vmin;
  --item-size-mobile: 65vmin;
  --item-margin-desktop: calc(var(--item-size-desktop) * -0.5);
  position: fixed;
  inset: 0;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  perspective: 1500px;
  overflow: hidden;
}

.flow-item {
  position: absolute;
  left: 50%;
  top: 50%;
  width: var(--item-size-desktop);
  height: var(--item-size-desktop);
  margin: var(--item-margin-desktop) 0 0 var(--item-margin-desktop);
  pointer-events: auto;
  cursor: pointer;
  will-change: transform, opacity;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  text-decoration: none;
}

.flow-item.is-banner-item .flow-item-image {
  width: 90%;
  height: auto;
  max-height: 40%;
}

.flow-item-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  user-select: none;
  border-radius: 20px;
}
</style>
