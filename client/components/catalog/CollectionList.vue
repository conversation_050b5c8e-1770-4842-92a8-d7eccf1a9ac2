<template>
  <div class="collectionList">
    <ul class="collectionList__list">
      <li
        v-for="(item, index) in items"
        :key="`${item + index}`"
        class="collectionList__list-item"
      >
        <CollectionListItem :item="item" />
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true,
    },
  },
}
</script>

<style lang="scss">
.collectionList {
  margin-bottom: em(75);
}
.collectionList__list {
  display: grid;
  grid-gap: em(26);

  @include grid('mobile-large', $down: true) {
    row-gap: em(40);
  }

  @include grid('tablet') {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: em(26);
  }

  @include grid('desktop-large') {
    grid-gap: em(40);
  }
}

.collectionList__list-item {
  text-align: center;
}
</style>
