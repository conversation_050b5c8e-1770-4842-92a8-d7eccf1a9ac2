<template>
  <NLink :to="localePath(item.url_path)" class="image-link">
    <ImageWrapper
      v-if="item.featured_image"
      :image="item.featured_image.large"
      :image-tablet="item.featured_image.small"
      :image-mobile="item.featured_image.medium"
      ratio-size="480x550"
    />
    <ImageWrapper v-else image="undefined" ratio-size="480x550" />
  </NLink>
</template>

<script>
export default {
  name: 'CollectionListItemImage',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.image-link {
  display: block;
  padding: 2rem;
  @include grid('laptop', $down: true) {
    padding: 1.5rem;
  }
}
</style>
