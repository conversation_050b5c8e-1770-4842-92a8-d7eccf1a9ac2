<template>
  <transition name="fade" mode="out-in">
    <Slider :key="galleryKey" :images="productImages" />
  </transition>
</template>

<script>
export default {
  props: {
    selectedVariant: { type: Object, default: null },
    product: { type: Object, required: true },
  },
  computed: {
    galleryKey() {
      return this.productImages
        .map((x) => x.small.url.split('/').pop())
        .join('|')
    },

    selectedVariantId() {
      return this.selectedVariant ? this.selectedVariant.id : 0
    },
    productImages() {
      if (this.selectedVariant) {
        return this.selectedVariant.images || []
      }
      return this.product.images || []
    },
  },
}
</script>
