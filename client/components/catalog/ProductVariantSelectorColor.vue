<template>
  <div class="productColors">
    <ul class="productColors__list">
      <li
        v-for="variant in choices"
        :key="variant.id"
        class="productColors__list-item"
      >
        <div
          class="productColorVariant"
          :class="{ isCurrent: isSelected(variant) }"
          @click="$emit('input', variant)"
        >
          <ImageWrapper
            v-if="variant.images.length"
            class="productColorVariant__img"
            :image="variant.images[0].small"
            ratio-size="200x233"
          />
          <span
            v-else
            class="productColorVariant__raw"
            :style="{ backgroundColor: variant.combination.attrs.color.value }"
          ></span>
          <div class="productColorVariant__line"></div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    value: { type: Object, default: null },
    choices: { type: Array, required: true },
  },
  methods: {
    isSelected(variant) {
      return this.value && variant.id === this.value.id
    },
  },
}
</script>

<style lang="scss" scoped>
.productColors {
  margin-top: em(30);
  margin-bottom: em(22);
}

.productColors__list {
  display: grid;
  grid-template-columns:
    minmax(em(50), 1fr)
    minmax(em(50), 1fr)
    minmax(em(50), 1fr)
    minmax(em(50), 1fr);

  @include grid('tablet', $to: 'laptop') {
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
  }

  @include grid('laptop') {
    display: grid;
    grid-template-columns:
      minmax(em(50), 1fr)
      minmax(em(50), 1fr)
      minmax(em(50), 1fr)
      minmax(em(50), 1fr);
    gap: em(22);
  }
}

.productColors__list-item {
  @include grid('tablet', $down: true) {
    display: flex;
    flex-basis: 33.3333%;
  }

  @include grid('tablet') {
    display: flex;
    flex-basis: em(72);
  }
}

.productColorVariant {
  width: 100%;
  cursor: pointer;

  @include grid('laptop', $down: true) {
    padding: em(11);
  }
}

.productColorVariant__img {
  display: block;
  margin-bottom: em(6);
  opacity: 1;
  transition: opacity var(--speed) var(--easing);

  .productColorVariant.isCurrent & {
    opacity: 0.5;
  }
}

.productColorVariant__raw {
  display: block;
  margin-bottom: em(6);
  padding-bottom: 100%;
  border-radius: 9999em;
  opacity: 1;
  transition: opacity var(--speed) var(--easing);

  .productColorVariant.isCurrent & {
    opacity: 0.5;
  }
}

.productColorVariant__line {
  background-color: currentColor;
  width: 100%;
  height: em(2);
  opacity: 0;

  .productColorVariant:hover &,
  .productColorVariant.isCurrent & {
    opacity: 1;
  }
}
</style>
