<template>
  <article class="moreCollections">
    <h1 class="moreCollections__title">{{ title }}</h1>
    <LayoutContainer :has-track-on-mobile="true">
      <div class="moreCollections__inner">
        <ul class="moreCollections__list">
          <li
            v-for="(item, index) in relatedItems"
            :key="`${item + index}`"
            class="moreCollections__list-item"
          >
            <MoreItemsLink :item="item" />
          </li>
        </ul>
      </div>
    </LayoutContainer>
  </article>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    items: {
      type: Array,
      required: true,
    },
  },
  computed: {
    relatedItems() {
      const result = this.items.map((item) => {
        return {
          url_path: item.url_path,
          image: item.images[0],
        }
      })
      return result
    },
  },
}
</script>

<style lang="scss" scoped>
.moreCollections {
  margin-top: em(75);
  margin-bottom: em(45);
}

.moreCollections__inner {
  overflow-x: auto;

  &::-webkit-scrollbar {
    width: em(4);
    height: em(8);
    background-color: var(--scrollbar-bg-color);
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-color);
  }
  // firefox
  scrollbar-width: thin;
}

.moreCollections__title {
  font-size: em(24);
  font-weight: var(--text-bold);
  margin-bottom: em(38);
}

.moreCollections__list {
  @include grid('laptop', $down: true) {
    display: flex;
    margin-right: em(-8);
    margin-left: em(26);
  }

  @include grid('laptop') {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    column-gap: em(26);
    row-gap: em(26);
  }

  @include grid('desktop-large') {
    column-gap: em(40);
    row-gap: em(40);
  }
}

.moreCollections__list-item {
  @include grid('laptop', $down: true) {
    flex: 0 0 45%;
    margin-right: em(8);
  }
}
</style>
