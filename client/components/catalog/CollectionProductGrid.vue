<template>
  <div class="product-grid-wrapper">
    <div
      v-for="(productGroup, groupIndex) in productGroups"
      :key="`group-${groupIndex}`"
      class="product-grid-container"
    >
      <!-- Product 1 Image -->
      <div
        v-if="productGroup[0]"
        class="product-image-cell"
        :class="`product-${groupIndex * 2 + 1}-image`"
        style="grid-area: 1 / 2 / 2 / 3"
      >
        <NLink
          :to="productGroup[0].url_path"
          class="product-link"
          :class="{ isDraft: !productGroup[0].live }"
        >
          <span v-if="!productGroup[0].live" class="draft-tag">
            <span class="draft-tag-label">
              {{ $t('collectionPage.itemTag.draft') }}
            </span>
          </span>
          <div class="product-images">
            <ImageWrapper
              v-if="productGroup[0].images && productGroup[0].images.length > 0"
              :image="productGroup[0].images[0]"
              ratio-size="615x718"
              class="product-img"
            />
            <div
              v-if="productGroup[0].images && productGroup[0].images.length > 1"
              class="showOnHover"
            >
              <ImageWrapper
                :image="productGroup[0].images[1]"
                ratio-size="615x718"
                class="product-img showOnHover__image"
              />
            </div>
            <ImageWrapper v-else image="undefined" ratio-size="615x718" />
          </div>
        </NLink>
      </div>

      <!-- Product 2 Image -->
      <div
        v-if="productGroup[1]"
        class="product-image-cell"
        :class="`product-${groupIndex * 2 + 2}-image`"
        style="grid-area: 1 / 4 / 2 / 5"
      >
        <NLink
          :to="productGroup[1].url_path"
          class="product-link"
          :class="{ isDraft: !productGroup[1].live }"
        >
          <span v-if="!productGroup[1].live" class="draft-tag">
            <span class="draft-tag-label">
              {{ $t('collectionPage.itemTag.draft') }}
            </span>
          </span>
          <div class="product-images">
            <ImageWrapper
              v-if="productGroup[1].images && productGroup[1].images.length > 0"
              :image="productGroup[1].images[0]"
              ratio-size="615x718"
              class="product-img"
            />
            <div
              v-if="productGroup[1].images && productGroup[1].images.length > 1"
              class="showOnHover"
            >
              <ImageWrapper
                :image="productGroup[1].images[1]"
                ratio-size="615x718"
                class="product-img showOnHover__image"
              />
            </div>
            <ImageWrapper v-else image="undefined" ratio-size="615x718" />
          </div>
        </NLink>
      </div>

      <!-- Row 2: Product Info with empty border cells -->
      <div class="empty-border-cell" style="grid-area: 2 / 1 / 3 / 2"></div>

      <div
        v-if="productGroup[0]"
        class="product-info-cell"
        :class="`product-${groupIndex * 2 + 1}-info`"
        style="grid-area: 2 / 2 / 3 / 3"
      >
        <NLink :to="productGroup[0].url_path" class="product-info-link">
          <h2 class="product-name">{{ productGroup[0].title }}</h2>
          <div class="product-price-area">
            <p v-if="hasOldPrice(productGroup[0])" class="product-old-price">
              {{ productGroup[0].old_price }}
            </p>
            <p class="product-price">{{ productGroup[0].price }}</p>
          </div>
        </NLink>
      </div>

      <div class="empty-border-cell" style="grid-area: 2 / 3 / 3 / 4"></div>

      <div
        v-if="productGroup[1]"
        class="product-info-cell"
        :class="`product-${groupIndex * 2 + 2}-info`"
        style="grid-area: 2 / 4 / 3 / 5"
      >
        <NLink :to="productGroup[1].url_path" class="product-info-link">
          <h2 class="product-name">{{ productGroup[1].title }}</h2>
          <div class="product-price-area">
            <p v-if="hasOldPrice(productGroup[1])" class="product-old-price">
              {{ productGroup[1].old_price }}
            </p>
            <p class="product-price">{{ productGroup[1].price }}</p>
          </div>
        </NLink>
      </div>

      <div class="empty-border-cell" style="grid-area: 2 / 5 / 3 / 6"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollectionProductGrid',
  props: {
    products: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    productGroups() {
      // Group products into sets of 2 (like collections)
      const groups = []
      for (let i = 0; i < this.products.length; i += 2) {
        groups.push(this.products.slice(i, i + 2))
      }
      return groups
    },
  },
  methods: {
    hasOldPrice(product) {
      return product?.old_price && !product.old_price.startsWith('0.00')
    },
  },
}
</script>

<style lang="scss" scoped>
.product-grid-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.product-grid-container {
  display: grid;
  width: 100%;
  grid-template-columns: 10% 1fr 10% 1fr 10%;
  grid-template-rows: 1fr auto;
  gap: 0;
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.empty-border-cell {
  border: 1px solid var(--page-font-color);
  border-left: none;
  border-right: none;
}

.product-image-cell {
  border: 1px solid var(--page-font-color);
  border-bottom: none;
  border-top: none;
  padding: 2rem;
  position: relative;

  @include grid('laptop', $down: true) {
    padding: 1.5rem;
  }
}

.product-link {
  display: block;
  text-decoration: none;
  color: inherit;
  position: relative;
}

.product-info-cell {
  border: 1px solid var(--page-font-color);
  border-left: none;
  border-right: none;
  border-top: none;
  padding: 1.5rem 2rem;
  text-align: center;

  @include grid('laptop', $down: true) {
    padding: 1rem 1.5rem;
  }
}

.product-info-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

.product-images {
  position: relative;
}

.showOnHover {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.showOnHover__image {
  opacity: 0;
  transition: opacity var(--speed-fast) var(--easing);

  .product-link:hover & {
    opacity: 1;
  }
}

.product-img {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.product-name {
  font-family: var(--font-family-title);
  line-height: var(--title-line-height);
  text-transform: uppercase;
  color: var(--black);
  margin: 0 0 em(10) 0;

  @include grid('laptop', $down: true) {
    font-size: em(16);
    font-weight: var(--text-bold);
  }

  @include grid('laptop') {
    font-size: em(20);
    font-weight: var(--text-bold);
  }
}

.product-price-area {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: em(10);
}

.product-old-price {
  font-size: em(13);
  text-decoration: line-through;
  color: var(--gray-b3);
  margin: 0;
}

.product-price {
  font-size: em(14);
  font-weight: var(--text-bold);
  color: var(--black);
  margin: 0;
}

.draft-tag {
  background-color: var(--tag-bg-color);
  color: var(--tag-font-color);
  display: inline-block;
  padding: em(6, 14) em(12, 14);
  font-size: em(14);
  font-weight: var(--text-bold);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  word-break: break-all;
}

.draft-tag-label {
  display: block;
  margin-bottom: em(1, 14);
}

.product-link.isDraft {
  position: relative;

  &:before {
    content: '';
    background: var(--tag-font-color);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0.3;
    pointer-events: none;
  }
}

/* Mobile responsive layout */
@media (max-width: 768px) {
  .product-grid-wrapper {
    display: block;
  }

  .product-grid-container {
    display: flex;
    flex-direction: column;
    gap: 0;
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .product-image-cell {
    padding: 1.5rem;
    border: 1px solid var(--page-font-color);
    border-left: none;
    border-right: none;
    border-bottom: none;
  }

  .product-info-cell {
    padding: 1rem 1.5rem;
    border: 1px solid var(--page-font-color);
    border-left: none;
    border-right: none;
    border-top: none;
    text-align: center;
  }

  .empty-border-cell {
    display: none;
  }

  /* Order products properly for mobile within each group */
  [class*='product-'][class*='-image'] {
    order: 1;
  }

  [class*='product-'][class*='-info'] {
    order: 2;
  }

  /* Specific ordering for each product in a group */
  .product-image-cell:nth-child(1) {
    order: 1;
  } /* First product image */
  .product-info-cell:nth-child(3) {
    order: 2;
  } /* First product info */
  .product-image-cell:nth-child(6) {
    order: 3;
  } /* Second product image */
  .product-info-cell:nth-child(8) {
    order: 4;
  } /* Second product info */
}

/* Tablet responsive layout */
@media (min-width: 769px) and (max-width: 1024px) {
  .product-grid-container {
    grid-template-columns: 5% 1fr 5% 1fr 5%;
    padding: 0 1rem;
  }

  .product-image-cell {
    padding: 1.5rem;
  }

  .product-info-cell {
    padding: 1rem 1.5rem;
  }
}
</style>
