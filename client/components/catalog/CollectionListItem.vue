// components/catalog/CollectionListItem.vue

<template>
  <NLink :to="localePath(item.url_path)" class="collectionListItemLink">
    <div class="image-cell">
      <ImageWrapper
        v-if="item.featured_image"
        class="collectionListItem__img"
        :image="item.featured_image.large"
        :image-tablet="item.featured_image.small"
        :image-mobile="item.featured_image.medium"
        ratio-size="480x550"
      />
      <ImageWrapper v-else image="undefined" ratio-size="480x550" />
    </div>

    <div class="text-cell">
      <h1 class="collectionListItem__name">{{ item.title }}</h1>
      <p v-if="item.caption" class="collectionListItem__season">
        {{ item.caption }}
      </p>
    </div>
  </NLink>
</template>

<script>
// The script does not need changes
export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
/* The <NLink> is now a flex container holding the two cells */
.collectionListItemLink {
  display: flex;
  flex-direction: column;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

/* Cell 1: Contains the image */
.image-cell {
  flex-grow: 1; /* Allows the image cell to take up available space */
  padding: 2rem;
  /* The internal border that separates the image from the text cell */
  border-bottom: 1px solid var(--page-font-color);

  @include grid('laptop', $down: true) {
    padding: 1.5rem;
  }
}

/* Cell 2: Contains the text */
.text-cell {
  padding: 1.5rem 2rem;
  text-align: center;
}

.collectionListItem__name {
  font-family: var(--font-family-title);
  line-height: var(--title-line-height);
  text-transform: uppercase;
  color: var(--black);

  @include grid('laptop', $down: true) {
    font-size: em(18);
    font-weight: var(--title-medium);
  }

  @include grid('laptop') {
    font-size: em(22);
    font-weight: var(--title-medium);
  }
}

.collectionListItem__season {
  font-size: em(14);
  margin-top: em(8);
  color: var(--black);
}

/* The old overlay styles are no longer needed */
</style>
