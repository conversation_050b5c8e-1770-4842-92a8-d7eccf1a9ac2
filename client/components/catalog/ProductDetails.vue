<template>
  <div class="productDetails">
    <div class="productDetails__header">
      <h1 class="productDetails__title">
        {{ productTitle }}
      </h1>
      <div class="productDetails__priceArea">
        <p v-if="oldPrice" class="productDetails__priceArea-oldPrice">
          {{ oldPrice }}
        </p>
        <p v-if="productPrice" class="productDetails__priceArea-price">
          {{ productPrice }}
        </p>
      </div>
    </div>
    <ProductVariantSelector
      v-if="hasVariants"
      v-model="selectedVariant"
      :variants="product.variants"
      :variant-attributes="product.variant_attributes"
    />
    <ProductAddToCart v-if="hasVariants" :selected-variant="selectedVariant" />
    <!-- <ProductDeliveryHelp v-if="hasVariants" /> -->

    <HR v-if="product.details" :margins="44" />

    <div
      v-if="product.details"
      class="productDetails__detailsproductDetails__details"
    >
      <!-- eslint-disable-next-line vue/no-v-html -->
      <div v-html="product.details"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    product: { type: Object, default: null },
    value: { type: Object, default: null },
  },
  data: () => ({
    selectedVariant: null,
  }),
  computed: {
    hasVariants() {
      return this.product.variants && this.product.variants.length > 0
    },
    productTitle() {
      return this.product.title
    },
    productDetails() {
      return this.product.details
    },
    productPrice() {
      return this.selectedVariant
        ? this.selectedVariant.price
        : this.product.base_price
    },
    oldPrice() {
      if (this.selectedVariant?.old_price) {
        if (this.selectedVariant.old_price.startsWith('0.00')) {
          return null
        }
        return this.selectedVariant.old_price
      }
      return this.product.old_price &&
        !this.product.old_price.startsWith('0.00')
        ? this.product.old_price
        : null
    },
  },
  watch: {
    selectedVariant: {
      deep: true,
      handler(value) {
        this.$emit('input', value)
      },
    },
  },
  created() {
    // Set variant as selected if only one exists
    if (this.hasVariants && this.product.variants.length === 1) {
      this.selectedVariant = this.product.variants[0]
    }
  },
}
</script>

<style lang="scss">
.productDetails {
  @include grid('laptop') {
    padding-top: em(170); // To align with header offset
  }
}
.productDetails__header {
  margin-top: em(80);
  margin-bottom: em(30);
}

.productDetails__title {
  font-size: em(25);
  font-weight: var(--text-bold);
  line-height: var(--title-line-height);
}

.productDetails__priceArea {
  display: flex;
  align-items: center;

  &-oldPrice {
    font-size: em(13);
    margin-right: em(10);
    text-decoration: line-through;
  }
}

.productDetails__details {
  font-size: em(14);

  ol,
  ul {
    padding-left: em(40, 14);

    @include grid('laptop', $down: true) {
      margin-top: em(21, 14);
      margin-bottom: em(21, 14);
    }

    @include grid('laptop') {
      margin-top: em(21, 14);
      margin-bottom: em(21, 14);
    }
  }

  ul {
    padding-left: em(12, 14);
  }

  ul {
    & > li {
      position: relative;

      &:before {
        content: '';
        background-color: var(--page-font-color);
        position: absolute;
        top: em(10, 14);
        left: em(-10, 14);
        width: em(4);
        height: em(4);
        transform: rotate(45deg);
      }
    }
  }
}
</style>
