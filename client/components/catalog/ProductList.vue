<template>
  <div class="productList">
    <ul v-if="items" class="productList__list" :class="{ hasThreeCols: layout === '3-columns' }">
      <li v-for="(item, index) in items" :key="`${item + index}`" class="productList__list-item">
        <NLink :to="item.url_path" class="collectionItemLink" :class="{ isDraft: !item.live }">
          <span v-if="!item.live" class="draft-tag">
            <span class="draft-tag-label">
              {{ $t('collectionPage.itemTag.draft') }}
            </span>
          </span>
          <article class="collectionItem">
            <div class="collectionItem__images">
              <ImageWrapper class="collectionItem__img" :image="item.images[0]" ratio-size="615x718" />
              <div class="showOnHover">
                <ImageWrapper v-if="item.images.length > 1" class="collectionItem__img showOnHover__image"
                  :image="item.images[1]" ratio-size="615x718" />
              </div>
            </div>
            <div class="collectionItem__info">
              <h1 class="collectionItem__info-name">{{ item.title }}</h1>
              <div class="collectionItem__info-priceArea">
                <p v-if="hasOldPrice(item)" class="collectionItem__info-priceArea-oldPrice">{{ item.old_price }}
                </p>
                <p class="collectionItem__info-priceArea-price">{{ item.price }}</p>
              </div>
            </div>
          </article>
        </NLink>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true,
    },
    layout: {
      type: String,
      default: '',
    },
  },
  methods: {
    hasOldPrice(item) {
      return item?.old_price && !item.old_price.startsWith("0.00");
    }
  }
}
</script>

<style lang="scss" scoped>
.productList__list {
  display: grid;

  @include grid('laptop', $down: true) {
    grid-template-columns: repeat(2, 1fr);
    column-gap: em(14);
    row-gap: em(50);
    row-gap: em(25);
  }

  @include grid('laptop') {
    grid-template-columns: repeat(var(--collection-items-per-row), 1fr);
    column-gap: em(26);
    row-gap: em(44);
  }

  &.hasThreeCols {
    @include grid('laptop') {
      --collection-items-per-row: 3;
      grid-template-columns: repeat(var(--collection-items-per-row), 1fr);
    }
  }
}

.collectionItemLink.isDraft {
  position: relative;
  display: block;

  &:before {
    content: '';
    background: var(--tag-font-color);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0.3;
    pointer-events: none;
  }
}

.draft-tag {
  background-color: var(--tag-bg-color);
  color: var(--tag-font-color);
  display: inline-block;
  padding: em(6, 14) em(12, 14);
  font-size: em(14);
  font-weight: var(--text-bold);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  word-break: break-all;
}

.draft-tag-label {
  display: block;
  margin-bottom: em(1, 14);
}

.collectionItem__images {
  position: relative;
}

.showOnHover {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.showOnHover__image {
  opacity: 0;
  transition: opacity var(--speed-fast) var(--easing);

  .collectionItemLink:hover & {
    opacity: 1;
  }
}

.collectionItem__img {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.collectionItem__info {
  font-size: em(14);
  margin-top: em(16);
  text-align: center;
}

.collectionItem__info-name {
  line-height: var(--title-line-height);

  @include grid('laptop', $down: true) {
    font-size: em(16, 14);
    font-weight: var(--text-bold);
  }

  @include grid('laptop') {
    font-size: em(20, 14);
    font-weight: var(--text-bold);
    margin-bottom: em(6, 30);
  }
}

.collectionItem__info-priceArea {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: em(10);

  &-oldPrice {
    font-size: em(13);
    margin-right: em(10);
    text-decoration: line-through;
  }

}
</style>
