<script>
export default {
  components: {
    ImageBlock: () => import('./ImageBlock'),
    TableBlock: () => import('./TableBlock'),
    TextBlock: () => import('./TextBlock'),
  },
  props: {
    block: { type: Object, required: true }
  },
  render(createElement) {
    // Renders component with block type as name: `${Type}Block`.
    const { type } = this.block

    const componentName =
      type
        .split('_')
        .map((x) => x.charAt(0).toUpperCase() + x.slice(1))
        .join('') + 'Block'

    const options = { class: 'block', props: { value: this.block.value } }
    return createElement(componentName, options)
  }
}
</script>
