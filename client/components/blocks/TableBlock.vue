<template>
  <div class="tableBlock" :class="{ scrollbarPadding: !value.table_caption }">
    <!-- First row header -->
    <table v-if="value.first_row_is_table_header && !value.first_col_is_header">
      <tbody>
        <tr>
          <th
            v-for="(item, itemIndex) in value.data[0]"
            :key="itemIndex"
            :class="{
              'tableBlock__big-col': itemIndex === 0,
              'tableBlock__small-col': itemIndex !== 0,
            }"
          >
            {{ item }}
          </th>
        </tr>
        <tr v-for="(row, index) in value.data.slice(1)" :key="index">
          <td v-for="(item, itemIndex) in row" :key="itemIndex">{{ item }}</td>
        </tr>
      </tbody>
    </table>

    <!-- First column header -->
    <table
      v-else-if="value.first_col_is_header && !value.first_row_is_table_header"
    >
      <tbody>
        <tr v-for="(row, index) in value.data" :key="index">
          <th>{{ row[0] }}</th>
          <td v-for="(item, itemIndex) in row.slice(1)" :key="itemIndex">
            {{ item }}
          </td>
        </tr>
      </tbody>
    </table>

    <!-- First row and column header -->
    <table
      v-else-if="value.first_row_is_table_header && value.first_col_is_header"
    >
      <tbody>
        <tr>
          <th v-for="(item, itemIndex) in value.data[0]" :key="itemIndex">
            {{ item }}
          </th>
        </tr>
        <tr v-for="(row, index) in value.data.slice(1)" :key="index">
          <th>{{ row[0] }}</th>
          <td v-for="(item, itemIndex) in row.slice(1)" :key="itemIndex">
            {{ item }}
          </td>
        </tr>
      </tbody>
    </table>

    <!-- No header -->
    <table v-else>
      <tbody>
        <tr v-for="(row, index) in value.data" :key="index">
          <td v-for="(item, itemIndex) in row" :key="itemIndex">{{ item }}</td>
        </tr>
      </tbody>
    </table>

    <!-- Table caption -->
    <span v-if="value.table_caption" class="tableBlock__caption">
      {{ value.table_caption }}
    </span>
  </div>
</template>

<script>
export default {
  props: {
    value: { type: Object, required: true },
  },
}
</script>

<style lang="scss" scoped>
.tableBlock {
  overflow-x: auto;

  &::-webkit-scrollbar {
    width: em(4);
    height: em(8);
    background-color: var(--scrollbar-bg-color);
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-color);
  }
  // firefox
  scrollbar-width: thin;

  @include grid('laptop', $down: true) {
    padding-left: em(26);
    padding-right: em(26);
    margin-left: em(-26);
    margin-right: em(-26);
  }

  &.scrollbarPadding {
    @include grid('laptop', $down: true) {
      padding-bottom: em(28);
    }
  }

  table {
    min-width: 640px;
    // margin-top: em(24);
    // margin-bottom: em(24);

    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;

    tr {
      border-bottom: 1px solid var(--gray-b3);
    }
    th {
      font-weight: var(--text-bold);
      text-align: left;

      @include grid('tablet-large', $down: true) {
        font-size: em(12);
      }
    }
    td {
      font-size: em(14);
    }
    th,
    td {
      padding: em(23, 14) em(4, 14) em(24, 14);
    }
  }
}

.tableBlock__big-col {
  width: 40%;
}
.tableBlock__small-col {
  width: 20%;
}

.tableBlock__caption {
  display: block;
  font-style: italic;
  font-size: em(14);
  padding: em(23, 14) 0 em(24, 14);
}
</style>
