<template>
  <div class="imageBlock">
    <img
      :src="value.image.url"
      :alt="value.image.alt"
      :width="value.image.width"
      :height="value.image.height"
      class="imageBlock__img"
    />
    <p v-if="value.caption" class="imageBlock__caption">{{ value.caption }}</p>
  </div>
</template>

<script>
export default {
  props: {
    value: { type: Object, required: true },
  },
}
</script>

<style lang="scss" scoped>
.imageBlock__img {
  width: 100%;
}
.imageBlock__caption {
  font-style: italic;
  font-size: em(14);
  padding: em(23, 14) 0 em(24, 14);
}
</style>
