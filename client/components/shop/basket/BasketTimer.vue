<template>
  <div
    v-if="showTimer"
    class="basketTimer"
    :class="{ warning: isWarning, critical: isCritical }"
  >
    <div class="basketTimer__icon">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path
          d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6z"
        />
        <path d="M8 2v6l4 2-1 1.7-5-3V2z" />
      </svg>
    </div>
    <div class="basketTimer__content">
      <div class="basketTimer__text">
        {{ timerText }}
      </div>
      <div class="basketTimer__time">
        {{ formattedTime }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // Timestamp when the reservation started (in milliseconds)
    reservationStart: {
      type: Number,
      default: null,
    },
    // Duration of reservation in seconds (default 3 minutes = 180 seconds)
    reservationDuration: {
      type: Number,
      default: 180,
    },
    // Whether to show the timer
    enabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      currentTime: Date.now(),
      interval: null,
      warningShown: false,
      criticalShown: false,
    }
  },
  computed: {
    showTimer() {
      return this.enabled && this.reservationStart && this.timeRemaining > 0
    },
    timeRemaining() {
      if (!this.reservationStart) return 0
      const elapsed = (this.currentTime - this.reservationStart) / 1000
      return Math.max(0, this.reservationDuration - elapsed)
    },
    formattedTime() {
      const minutes = Math.floor(this.timeRemaining / 60)
      const seconds = Math.floor(this.timeRemaining % 60)
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    },
    isWarning() {
      return this.timeRemaining <= 60 && this.timeRemaining > 30
    },
    isCritical() {
      return this.timeRemaining <= 30
    },
    timerText() {
      if (this.isCritical) {
        return this.$t('basket.timer.critical')
      } else if (this.isWarning) {
        return this.$t('basket.timer.warning')
      } else {
        return this.$t('basket.timer.normal')
      }
    },
  },
  watch: {
    reservationStart: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.startTimer()
        } else {
          this.stopTimer()
        }
      },
    },
    timeRemaining(newVal) {
      if (newVal <= 0) {
        this.$emit('expired')
        this.stopTimer()
        // Reset flags for next timer
        this.warningShown = false
        this.criticalShown = false
      } else if (newVal <= 30 && !this.criticalShown) {
        this.$emit('critical')
        this.criticalShown = true
      } else if (newVal <= 60 && !this.warningShown && newVal > 30) {
        this.$emit('warning')
        this.warningShown = true
      }
    },
  },
  beforeDestroy() {
    this.stopTimer()
  },
  methods: {
    startTimer() {
      this.stopTimer()
      this.currentTime = Date.now()
      // Reset notification flags when starting a new timer
      this.warningShown = false
      this.criticalShown = false
      this.interval = setInterval(() => {
        this.currentTime = Date.now()
      }, 1000)
    },
    stopTimer() {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.basketTimer {
  display: flex;
  align-items: center;
  padding: em(12) em(16);
  background-color: var(--gray-ef);
  border-radius: em(4);
  margin-bottom: em(16);
  border-left: 4px solid var(--color-primary);
  transition: all 0.3s ease;

  &.warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
  }

  &.critical {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
    animation: pulse 1s infinite;
  }
}

.basketTimer__icon {
  margin-right: em(12);
  flex-shrink: 0;
}

.basketTimer__content {
  flex: 1;
}

.basketTimer__text {
  font-size: em(14);
  font-weight: 500;
  margin-bottom: em(4);
}

.basketTimer__time {
  font-size: em(16);
  font-weight: bold;
  font-family: monospace;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
</style>
