<template>
  <div v-if="hasAutomaticDiscounts" class="automaticDiscountInfo">
    <div class="automaticDiscountInfo__header">
      <div class="automaticDiscountInfo__icon">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6z"/>
          <path d="M6.5 5.5L9.5 8.5 6.5 11.5 5.5 10.5 7 9H3V7h4L5.5 5.5z"/>
        </svg>
      </div>
      <div class="automaticDiscountInfo__title">
        {{ $t('discount.automatic.applied') }}
      </div>
    </div>
    
    <div class="automaticDiscountInfo__discounts">
      <div 
        v-for="discount in automaticDiscounts" 
        :key="discount.id"
        class="automaticDiscountInfo__discount"
      >
        <div class="automaticDiscountInfo__discount-name">
          {{ discount.name }}
        </div>
        <div class="automaticDiscountInfo__discount-description">
          {{ discount.description }}
        </div>
        <div class="automaticDiscountInfo__discount-amount">
          {{ formatDiscountAmount(discount) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    basket: {
      type: Object,
      required: true
    }
  },
  computed: {
    automaticDiscounts() {
      // Extract automatic shipping discounts from basket extra_rows
      if (!this.basket?.extra_rows) return []
      
      return Object.values(this.basket.extra_rows).filter(row => {
        // Look for automatic shipping discount rows
        return row.modifier === 'automatic_shipping_discount' || 
               (row.extra && row.extra.automatic_discount)
      }).map(row => ({
        id: row.modifier,
        name: row.label || this.$t('discount.automatic.shipping'),
        description: row.extra?.description || this.$t('discount.automatic.shippingDescription'),
        amount: row.amount,
        percentage: row.extra?.percentage
      }))
    },
    hasAutomaticDiscounts() {
      return this.automaticDiscounts.length > 0
    }
  },
  methods: {
    formatDiscountAmount(discount) {
      if (discount.percentage) {
        return this.$t('discount.automatic.percentageOff', { percentage: discount.percentage })
      }
      return this.$t('discount.automatic.amountOff', { amount: discount.amount })
    }
  }
}
</script>

<style lang="scss" scoped>
.automaticDiscountInfo {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
  border: 1px solid #c3e6c3;
  border-radius: em(8);
  padding: em(16);
  margin-bottom: em(16);
}

.automaticDiscountInfo__header {
  display: flex;
  align-items: center;
  margin-bottom: em(12);
}

.automaticDiscountInfo__icon {
  margin-right: em(8);
  color: #28a745;
  flex-shrink: 0;
}

.automaticDiscountInfo__title {
  font-weight: 600;
  color: #155724;
  font-size: em(14);
}

.automaticDiscountInfo__discounts {
  display: grid;
  gap: em(8);
}

.automaticDiscountInfo__discount {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: em(8);
  align-items: start;
}

.automaticDiscountInfo__discount-name {
  font-weight: 500;
  color: #155724;
  font-size: em(13);
}

.automaticDiscountInfo__discount-description {
  grid-column: 1 / -1;
  font-size: em(12);
  color: #6c757d;
  margin-top: em(2);
}

.automaticDiscountInfo__discount-amount {
  font-weight: 600;
  color: #28a745;
  font-size: em(13);
  text-align: right;
}
</style>
