<template>
  <div class="checkoutFormAddress">
    <div class="checkoutFormAddress__layout" :class="{ columns: columns }">
      <FormField
        v-model="formData.firstName"
        :vid="getVID('firstName')"
        :label="$t('form.firstName')"
        :rules="defaultRules"
      />
      <FormField
        v-model="formData.lastName"
        :vid="getVID('lastName')"
        :label="$t('form.lastName')"
        :rules="defaultRules"
      />
      <FormField
        v-model="formData.address"
        :vid="getVID('address')"
        :label="$t('form.address')"
        :rules="addressRule"
      />
      <FormField
        v-model="formData.address2"
        :vid="getVID('address2')"
        :label="$t('form.address2')"
        :rules="address2Rule"
      />
      <FormField
        v-model="formData.postal"
        :vid="getVID('postal')"
        :label="$t('form.postal')"
        :rules="postalCodeRule"
      />
      <FormField
        v-model="formData.city"
        :vid="getVID('city')"
        :label="$t('form.city')"
        :rules="cityRule"
      />
      <FormField
        v-model="formData.country"
        :vid="getVID('country')"
        :label="$t('form.country')"
        placeholder=" "
        :rules="defaultRules"
        :choices="countryChoices"
      />
      <FormField
        v-show="hasState"
        v-model="state"
        :vid="getVID('state')"
        :label="$t('form.state')"
        placeholder=" "
        :rules="defaultRules"
        :choices="stateChoices"
      />
      <FormField
        v-model="formData.phone"
        :vid="getVID('phone')"
        :label="$t('form.phone')"
        :rules="defaultRules"
      />
      <FormField
        v-show="isMexicanAddress"
        v-model="formData.rfc"
        :vid="getVID('rfc')"
        :label="$t('form.rfc')"
        :help="$t('form.rfcHelp')"
        :rules="rfcRule"
        placeholder="PERJ850101ABC"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    prefix: { type: String, default: '' },
    initial: { type: String, default: '' },
    required: { type: Boolean, default: true },
    columns: { type: Boolean, default: false },
  },
  data() {
    return {
      formData: {},
      state: '',
      billingToDifferentAddress: false,
    }
  },
  computed: {
    config() {
      return this.$store.state.config.config
    },
    countryChoices() {
      return this.config.countries
    },
    stateChoices() {
      const country = this.formData.country
      if (country && this.config.states && this.config.states[country]) {
        return this.config.states[country]
      }
      return []
    },
    hasState() {
      return this.stateChoices.length > 0
    },
    isMexicanAddress() {
      return this.formData.country === 'MX'
    },
    defaultRules() {
      return this.required ? 'required' : ''
    },

    // max 30 characters rule
    cityRule() {
      return 'required|min:1|max:30|noHash'
    },

    addressRule() {
      return 'required|min:1|max:75|noHash'
    },

    address2Rule() {
      return 'max:75|noHash'
    },

    postalCodeRule() {
      return 'required|min:1|max:12'
    },

    rfcRule() {
      // Make RFC required for Mexican addresses
      if (this.isMexicanAddress) {
        return 'required|rfc'
      }
      return 'rfc'
    },
  },
  watch: {
    'formData.country'(value) {
      if (this.hasState) this.state = ''
      else this.state = value
      this.$emit('country-updated', value)
    },
    'formData.postal'(value) {
      this.$emit('postal-code-updated', value)
    },
    formData: {
      deep: true,
      handler: 'emitAddressText',
    },
    state: 'emitAddressText',
    initial: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          this.formData = this.getAddressData(value)
          setTimeout(() => {
            if (this.hasState) this.state = this.formData.state
            else this.state = this.formData.country
          }, 50)
        }
      },
    },
  },
  methods: {
    clear() {
      this.formData = {}
    },
    toggleBillingAddressForm() {
      this.billingToDifferentAddress = !this.billingToDifferentAddress
    },
    getVID(name) {
      if (this.prefix) return `${this.prefix}-${name}`
      else return name
    },
    emitAddressText() {
      this.$emit('input', this.getAddressText())
    },
    getAddressText() {
      const state = this.hasState ? this.state : ''

      const lines = [
        this.formData.firstName,
        this.formData.lastName,
        this.formData.address,
        this.formData.address2,
        this.formData.postal,
        this.formData.city,
        this.formData.country,
        state,
        this.formData.phone,
        this.formData.rfc || '', // Include RFC field
      ]
      return lines.join('\n')
    },
    getAddressData(addressText) {
      const lines = addressText.split('\n')
      return {
        firstName: lines[0] ? lines[0].trim() : '',
        lastName: lines[1] ? lines[1].trim() : '',
        address: lines[2] ? lines[2].trim() : '',
        address2: lines[3] ? lines[3].trim() : '',
        postal: lines[4] ? lines[4].trim() : '',
        city: lines[5] ? lines[5].trim() : '',
        country: lines[6] ? lines[6].trim() : '',
        state: lines[7] ? lines[7].trim() : '',
        phone: lines[8] ? lines[8].trim() : '',
        rfc: lines[9] ? lines[9].trim() : '', // Include RFC field
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.checkoutFormAddress__layout {
  display: grid;
  row-gap: em(32);

  @include grid('tablet') {
    column-gap: em(28);
  }

  &.columns {
    @include grid('tablet') {
      grid-template-columns: 1fr 1fr;
    }
  }
}
</style>
