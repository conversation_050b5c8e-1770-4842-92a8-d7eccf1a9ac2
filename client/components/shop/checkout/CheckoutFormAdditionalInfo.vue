<template>
  <div class="checkoutFormAdditionalInfo">
    <FormField
      :value="value"
      vid="additionalInfo"
      :label="$t('checkout.heading.additionalInfo')"
      @input="$emit('input', $event)"
    />
  </div>
</template>

<script>
export default {
  props: {
    value: { type: String, default: '' },
  },
}
</script>

<style lang="scss" scoped>
.checkoutFormAdditionalInfo {
  @include grid('tablet') {
    width: calc(50% - em(14));
  }
}
</style>
