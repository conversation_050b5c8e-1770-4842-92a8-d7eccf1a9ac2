<template>
  <div class="checkoutFormCustomer">
    <div v-if="!$auth.loggedIn">
      <TitleH3>
        {{ $t('checkout.heading.contactEmail') }}
      </TitleH3>
      <FormField
        vid="email"
        :value="value"
        :label="$t('form.email')"
        rules="required|email"
        class="email-field"
        @input="$emit('input', $event)"
      />
    </div>
    <div v-if="$auth.loggedIn">
      <TitleH3>
        {{ $t('checkout.welcome') }}
        {{ $auth.user.first_name || $auth.user.email }}
      </TitleH3>
      <div class="email-wrap">
        <div class="email-label">{{ $t('form.email') }}</div>
        <p class="email-text">
          {{ $auth.user.email }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: { type: String, default: '' },
  },
  mounted() {
    if (this.$auth.loggedIn) {
      this.$emit('input', this.$auth.user.email)
    }
  },
}
</script>

<style lang="scss" scoped>
.email-field {
  @include grid('tablet') {
    width: calc(50% - em(14));
  }
}

.email-wrap {
  user-select: none;
}

.email-label {
  color: var(--gray-b3);
  transform-origin: top left;
  transform: scale(calc(14 / 16));
}

.email-text {
  line-height: normal;
  display: block;
  width: 100%;
  font-size: em(21);
  padding-top: em(8, 21);
  padding-bottom: em(9, 21);
  border-bottom: em(1, 21) solid var(--page-font-color);

  @include grid('tablet') {
    width: calc(50% - em(14, 21));
  }
}
</style>
