<template>
  <div class="checkoutForm">
    <Form
      ref="form"
      action="/api/v1/checkout/"
      class="checkoutForm__form"
      :payload="payload"
      @success="onSuccess"
      @error="onError"
    >
      <div class="grid">
        <div class="column">
          <CheckoutFormCustomer v-model="formData.email" />
          <div class="margins checkoutFormAddresses">
            <TitleH3>
              {{ $t('checkout.heading.deliveryAddress') }}
            </TitleH3>
            <CheckoutFormAddress
              v-model="formData.shipping_address"
              prefix="shipping_address"
              :initial="addressInitial"
              :columns="true"
              @country-updated="setBasketCountry"
              @postal-code-updated="setBasketPostalCode"
            />

            <div class="checkoutFormAddresses__billing-checkbox">
              <CheckboxInput
                v-if="$auth.loggedIn"
                v-model="saveDefaultAddress"
                vid="checkbox-save-address"
                :placeholder="$t('checkout.checkbox.saveAddress')"
              />
              <CheckboxInput
                v-model="billingToDifferentAddress"
                vid="checkbox-billing-to-different-address"
                :placeholder="$t('checkout.checkbox.billingAddress')"
              />
            </div>

            <div
              v-if="billingToDifferentAddress"
              class="checkoutFormAddresses__billing-form"
            >
              <TitleH3>
                {{ $t('checkout.heading.billingAddress') }}
              </TitleH3>
              <CheckoutFormAddress
                v-model="formData.billing_address"
                prefix="shipping_address"
                :columns="true"
              />
            </div>
          </div>

          <div class="margins checkoutFormDeliveryAndInfo">
            <TitleH3>
              {{ $t('checkout.heading.additionalInfo') }}
            </TitleH3>
            <CheckoutFormAdditionalInfo v-model="formData.extra.notes" />
          </div>

          <CartItemList
            v-if="basket"
            :items="basket.items"
            class="margins cart-block"
            :editable="false"
          />
        </div>

        <div class="column margins-mobile">
          <CartTotal v-if="basket" :basket="basket" />
          <AutomaticDiscountInfo v-if="basket" :basket="basket" />
          <BasketTimer
            v-if="basket"
            :reservation-start="basketReservationStart"
            :enabled="hasDiscountOrReservation"
            @expired="onReservationExpired"
            @warning="onReservationWarning"
            @critical="onReservationCritical"
          />
          <!-- <FormField
            v-model="formData.extra.discount_code"
            vid="discount_code"
            :label="$t('form.discountCode')"
            :help="discountHelpText"
            class="margin-top"
            @input="setBasketDiscountCode"
          /> -->
          <div v-if="discountError" class="error">
            {{ discountError }}
          </div>

          <div class="margin-top">
            <div class="bizum-input">
              <CheckboxInput
                v-model="bizum"
                vid="checkbox-bizum"
                :placeholder="bizum_placeholder"
              />
            </div>
            <FormButton class="button--primary" fetching-on-success>
              {{ $t('checkout.submit') }}
            </FormButton>
            <p class="help">{{ $t('checkout.submit.help') }}</p>
            <p class="help">
              {{ $t('account.orderDetails.proceed.checkout') }}
            </p>
          </div>
        </div>
      </div>
    </Form>
  </div>
</template>

<script>
import { debounce } from 'debounce'

export default {
  components: {
    BasketTimer: () => import('~/components/shop/basket/BasketTimer.vue'),
    AutomaticDiscountInfo: () =>
      import('~/components/shop/discount/AutomaticDiscountInfo.vue'),
  },
  data: () => ({
    formData: {
      email: '',
      shipping_address: '',
      billing_address: '',
      extra: {},
      payment_method: 'redsys',
    },
    addressInitial: '',
    billingToDifferentAddress: false,
    saveDefaultAddress: false,
    bizum: false,
  }),
  computed: {
    basket() {
      return this.$store.state.basket.basket
    },
    discountError() {
      return this.basket?.extra?.discount_error
    },
    hasValidDiscount() {
      // Check for a valid discount (no error, and a code exists)
      return (
        this.basket &&
        this.basket.extra &&
        !this.basket.extra.discount_error &&
        this.basket.extra.discount_code
      )
    },
    discountHelpText() {
      // Only show the help text if there's a valid discount.
      return this.hasValidDiscount ? this.$t('form.discountCodeNotice') : ''
    },
    hasDiscountOrReservation() {
      // Show timer only if there's a valid discount code (no error and code exists)
      return !!(
        this.basket?.extra?.discount_code && !this.basket?.extra?.discount_error
      )
    },
    basketReservationStart() {
      // Use the reservation start time from the store
      return this.$store.state.basket.reservationStart
    },
    payload() {
      const data = { ...this.formData }
      if (!this.billingToDifferentAddress)
        data.billing_address = data.shipping_address
      if (this.bizum) data.payment_method = 'bizum'
      return data
    },
    bizum_placeholder() {
      return (
        '<span>' +
        this.$t('checkout.checkbox.bizum') +
        "<img src='/images/bizum_azul.png' id='bizum-logo'></span>"
      )
    },
  },
  watch: {
    payload: {
      deep: true,
      handler(value) {
        this.$emit('input', value)
      },
    },
  },
  async mounted() {
    if (this.$auth.loggedIn) {
      await this.$store.dispatch('user/fetchUser')
      this.addressInitial = this.$auth.user.address || ''
    }
  },
  methods: {
    async onSuccess(data) {
      if (this.saveDefaultAddress) {
        await this.$store.dispatch('user/updateUser', {
          address: this.shipping_address,
        })
      }

      // Store payment start time to handle timer expiration during external payment
      this.$store.commit('basket/setReservationStart', Date.now())

      // Store basket state before payment for potential recovery
      if (this.basket) {
        localStorage.setItem(
          'checkout_basket_backup',
          JSON.stringify({
            basketId: this.basket.id,
            discountCode: this.basket.extra?.discount_code,
            reservationStart: Date.now(),
            timestamp: Date.now(),
          })
        )
      }

      try {
        eval(JSON.parse(data.url)) // eslint-disable-line no-eval
      } catch (error) {
        this.$toast.error(this.$t('checkout.errorRedirecting'))
      }
    },
    onError(data) {
      if (data.payment_method) {
        this.$toast.error(data.payment_method[0])
        return
      }

      const newData = {}
      if (data.email) newData.email = data.email
      if (data.shipping_address) {
        for (const [key, value] of Object.entries(data.shipping_address)) {
          newData[`shipping_address-${key}`] = value
        }
      }
      if (data.billing_address) {
        for (const [key, value] of Object.entries(data.billing_address)) {
          newData[`billing_address-${key}`] = value
        }
      }
      if (data.extra) newData.additionalInfo = data.extra
      this.$refs.form.error(newData)
    },

    async setBasketCountry(country) {
      await this.$store.dispatch('basket/initBasket')
      if (this.basket.extra.country !== country) {
        try {
          let _ = {
            country,
          }
          if (this.formData?.extra?.discount_code) {
            _ = {
              ..._,
              discount_code: this.formData?.extra?.discount_code,
            }
          }
          await this.$store.dispatch('basket/updateBasketExtra', _)
        } catch (error) {}
      }
    },

    setBasketDiscountCode: debounce(async function (discountCode) {
      await this.$store.dispatch('basket/initBasket')
      if (this.basket.extra.discount_code !== discountCode) {
        try {
          await this.$store.dispatch('basket/updateBasketExtra', {
            discount_code: discountCode,
          })
        } catch (error) {}
      }
    }, 500),

    setBasketPostalCode: debounce(async function (postalCode) {
      await this.$store.dispatch('basket/initBasket')
      if (this.basket.extra.postal_code !== postalCode) {
        try {
          let _ = {
            postal_code: postalCode,
          }
          if (this.formData?.extra?.discount_code) {
            _ = {
              ..._,
              discount_code: this.formData?.extra?.discount_code,
            }
          }
          await this.$store.dispatch('basket/updateBasketExtra', _)
        } catch (error) {}
      }
    }, 500),

    async onReservationExpired() {
      this.$toast.error(this.$t('basket.timer.expired'))

      // Clear the discount code from the form
      if (this.formData?.extra?.discount_code) {
        this.formData.extra.discount_code = ''
      }

      // Handle reservation expiration through store
      await this.$store.dispatch('basket/handleReservationExpired')

      // Clear the discount from the basket by updating with empty discount code
      try {
        await this.$store.dispatch('basket/updateBasketExtra', {
          discount_code: '',
        })
      } catch (error) {
        console.error('Error clearing expired discount:', error)
      }
    },

    onReservationWarning() {
      this.$toast.info(this.$t('basket.timer.warningMessage'))
    },

    onReservationCritical() {
      this.$toast.error(this.$t('basket.timer.criticalMessage'))
    },
  },
}
</script>

<style lang="scss" scoped>
.error {
  margin-top: 4px;
  font-size: 14px;
  color: red;
}

.checkoutForm {
  @include grid('laptop', $down: true) {
    margin-top: em(48);
    margin-bottom: em(40);
  }
}

.grid {
  @include grid('laptop', $down: true) {
    margin-bottom: em(32);
  }

  @include grid('laptop') {
    display: grid;
    grid-template-columns: 2fr 1fr;
    column-gap: 10%;
    margin-bottom: em(80);
  }
}

.cart-block {
  @include grid('laptop', $down: true) {
    margin-top: em(80);
    margin-bottom: em(32);
  }

  @include grid('laptop') {
    margin-top: em(120);
  }
}

.margins {
  @include grid('laptop', $down: true) {
    margin-top: em(80);
  }

  @include grid('laptop') {
    margin-top: em(120);
  }
}

.margins-mobile {
  @include grid('laptop', $down: true) {
    margin-top: em(80);
  }
}

.margin-top {
  margin-top: em(48);
}

.checkoutFormAddresses__billing-checkbox {
  background-color: var(--gray-ef);
  display: grid;
  row-gap: em(56);
  padding: em(28);

  @include grid('tablet') {
    width: calc(50% - em(14));
  }

  @include grid('laptop', $down: true) {
    margin-top: em(48);
  }

  @include grid('laptop') {
    margin-top: em(56);
  }
}

.checkoutFormAddresses__billing-form {
  @include grid('laptop', $down: true) {
    margin-top: em(80);
  }

  @include grid('laptop') {
    margin-top: em(120);
  }
}

.help {
  font-size: em(12);
  margin-top: em(12, 12);
}

.bizum-input {
  background-color: var(--gray-ef);
  padding: 1.75em;
}
</style>

<style lang="scss">
img#bizum-logo {
  margin-top: -3px;
  margin-left: 3px;
}
</style>
