<template>
  <div v-if="basket" class="cart">
    <ContentContainer is-small>
      <div class="cartLayout">
        <div class="column">
          <CartItemList :items="basket.items" />

          <HR :margins="32" />
        </div>
        <div class="column">
          <CartTotal :basket="basket" />
          <div class="margin-top">
            <NLink :to="localePath('shop-checkout')" class="button--primary">
              <ButtonLabel> {{ $t('cart.checkout.buttonLabel') }} </ButtonLabel>
            </NLink>
          </div>
        </div>
      </div>
    </ContentContainer>
  </div>
</template>

<script>
export default {
  computed: {
    basket() {
      return this.$store.state.basket.basket
    },
  },
  async mounted() {
    await this.$store.dispatch('basket/initBasket')
  },
}
</script>

<style lang="scss">
.cartLayout {
  @include grid('laptop', $down: true) {
    margin-bottom: em(32);
  }

  @include grid('laptop') {
    display: grid;
    grid-template-columns: 2fr 1fr;
    column-gap: 10%;
    margin-bottom: em(80);
  }
}
.cartLayout__cart {
  @include grid('laptop', $down: true) {
    margin-top: em(80);
    margin-bottom: em(32);
  }

  @include grid('laptop') {
    margin-top: em(120);
  }
}

.margin-top {
  margin-top: em(48);
}
</style>
