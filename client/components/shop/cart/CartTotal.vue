<template>
  <div v-if="basketOrOrder" class="cartTotal">
    <div class="row">
      <!-- Show subtotal (VAT-included prices) -->
      <div class="items-total between">
        <span class="label">{{ $t('cart.subtotal') }}</span>
        <span class="value">{{ basketOrOrder.subtotal }}</span>
      </div>

      <!-- Show extra rows (shipping, discounts, etc.) but handle tax specially -->
      <div
        v-for="row in filteredExtraRows"
        :key="row.modifier"
        class="extra between"
      >
        <span v-if="row.modifier === 'shipping'" class="label">
          {{ row.extra.caption }}
        </span>
        <span v-else>{{ row.label }}</span>
        <span class="value">{{ row.amount }}</span>
      </div>
    </div>

    <div class="total between">
      <span class="label">{{ $t('cart.total') }}</span>
      <span class="value">{{ basketOrOrder.total }}</span>
    </div>

    <!-- VAT breakdown toggle -->
    <div v-if="vatInfo" class="vat-section">
      <button
        type="button"
        class="vat-toggle"
        :aria-expanded="showVatBreakdown"
        :aria-label="$t('cart.vat_breakdown_toggle')"
        @click="showVatBreakdown = !showVatBreakdown"
      >
        <span class="vat-text">
          {{
            $t('cart.includes_vat', {
              amount: vatInfo.amount,
              rate: vatInfo.rate,
            })
          }}
        </span>
        <span class="vat-icon" :class="{ expanded: showVatBreakdown }">
          <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
            <path
              d="M1 1L6 6L11 1"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </button>

      <!-- Expandable VAT breakdown -->
      <div v-if="showVatBreakdown" class="vat-breakdown">
        <div class="breakdown-row between">
          <span class="breakdown-label">{{
            $t('cart.subtotal_before_vat')
          }}</span>
          <span class="breakdown-value">{{ subtotalBeforeVat }}</span>
        </div>
        <div class="breakdown-row between">
          <span class="breakdown-label">{{
            $t('cart.vat_amount', { rate: vatInfo.rate })
          }}</span>
          <span class="breakdown-value">{{ vatInfo.amount }}</span>
        </div>
        <div class="breakdown-total between">
          <span class="breakdown-label">{{ $t('cart.total_with_vat') }}</span>
          <span class="breakdown-value">{{ basketOrOrder.total }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    basket: { type: Object, default: null },
    order: { type: Object, default: null },
  },
  data() {
    return {
      showVatBreakdown: false,
    }
  },
  computed: {
    basketOrOrder() {
      return this.basket || this.order
    },

    // Filter out tax from extra rows since we show it separately
    filteredExtraRows() {
      if (!this.basketOrOrder?.extra_rows) return []

      return this.basketOrOrder.extra_rows.filter(
        (row) => row.modifier !== 'tax'
      )
    },

    // Extract VAT information for display below total
    vatInfo() {
      if (!this.basketOrOrder?.extra_rows) return null

      const taxRow = this.basketOrOrder.extra_rows.find(
        (row) => row.modifier === 'tax'
      )

      if (!taxRow) return null

      return {
        amount: taxRow.amount,
        rate: taxRow.extra?.percent || '21',
      }
    },

    // Calculate subtotal before VAT for breakdown display
    subtotalBeforeVat() {
      // Use the subtotal_without_tax from the API if available
      if (this.basketOrOrder?.extra?.subtotal_without_tax) {
        return this.basketOrOrder.extra.subtotal_without_tax
      }

      // Fallback: calculate from total - VAT
      if (!this.vatInfo || !this.basketOrOrder?.total) return null

      // Parse the total and VAT amounts (remove currency symbols and convert to numbers)
      const totalStr = this.basketOrOrder.total
        .toString()
        .replace(/[^\d.,]/g, '')
        .replace(',', '.')
      const vatStr = this.vatInfo.amount
        .toString()
        .replace(/[^\d.,]/g, '')
        .replace(',', '.')

      const total = parseFloat(totalStr)
      const vat = parseFloat(vatStr)

      if (isNaN(total) || isNaN(vat)) return null

      const beforeVat = total - vat

      // Format back to currency (assuming Euro format)
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR',
      }).format(beforeVat)
    },
  },
}
</script>

<style lang="scss" scoped>
.cartTotal {
  display: grid;
  row-gap: em(18);
}

.row {
  display: grid;
  row-gap: em(18);
}

.between {
  display: grid;
  grid-auto-flow: column;
}

.items-total {
  font-weight: var(--text-bold);
}

.total {
  font-size: em(18);
  font-weight: var(--text-heavy);
}

.value {
  text-align: right;
}

// VAT Section Styling
.vat-section {
  margin-top: em(12);
  border-top: 1px solid var(--color-border-light, #e5e5e5);
  padding-top: em(12);
}

.vat-toggle {
  width: 100%;
  background: none;
  border: none;
  padding: em(8) 0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: left;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--color-background-hover, #f8f9fa);
    border-radius: em(4);
    padding-left: em(8);
    padding-right: em(8);
  }

  &:focus {
    outline: 2px solid var(--color-primary, #007bff);
    outline-offset: 2px;
    border-radius: em(4);
  }
}

.vat-text {
  font-size: em(14);
  color: var(--color-text-secondary, #666);
  font-style: italic;
  flex: 1;
}

.vat-icon {
  margin-left: em(8);
  transition: transform 0.2s ease;
  color: var(--color-text-secondary, #666);

  &.expanded {
    transform: rotate(180deg);
  }

  svg {
    display: block;
  }
}

// VAT Breakdown Styling
.vat-breakdown {
  margin-top: em(12);
  padding: em(16);
  background-color: var(--color-background-light, #f8f9fa);
  border-radius: em(6);
  border: 1px solid var(--color-border-light, #e5e5e5);

  @include grid('mobile') {
    padding: em(12);
    margin-top: em(8);
  }
}

.breakdown-row {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: em(16);
  padding: em(6) 0;

  &:not(:last-child) {
    border-bottom: 1px solid var(--color-border-lighter, #f0f0f0);
  }

  @include grid('mobile') {
    gap: em(8);
    padding: em(4) 0;
  }
}

.breakdown-total {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: em(16);
  padding: em(8) 0 em(4) 0;
  margin-top: em(8);
  border-top: 2px solid var(--color-border, #ddd);
  font-weight: var(--text-bold);

  @include grid('mobile') {
    gap: em(8);
    padding: em(6) 0 em(2) 0;
    margin-top: em(6);
  }
}

.breakdown-label {
  font-size: em(13);
  color: var(--color-text-secondary, #666);

  @include grid('mobile') {
    font-size: em(12);
  }
}

.breakdown-value {
  font-size: em(13);
  font-weight: var(--text-medium);
  text-align: right;
  color: var(--color-text-primary, #333);

  @include grid('mobile') {
    font-size: em(12);
  }
}

.breakdown-total .breakdown-label,
.breakdown-total .breakdown-value {
  font-size: em(14);
  font-weight: var(--text-bold);
  color: var(--color-text-primary, #333);

  @include grid('mobile') {
    font-size: em(13);
  }
}
</style>
