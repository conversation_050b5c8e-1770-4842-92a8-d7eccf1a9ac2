<template>
  <article class="cartItem">
    <div class="cartItem__cols">
      <div class="cartItem__thumbnail" :class="{ outOfStock: !isAvailable }">
        <nuxt-link v-if="productUrl" :to="productUrl">
          <ImageWrapper
            class="homepageCollectionItem__img"
            :image="item.product.image"
            ratio-size="480x636"
          />
        </nuxt-link>
        <ImageWrapper
          v-else-if="item.product"
          class="homepageCollectionItem__img"
          :image="item.product.image"
          ratio-size="480x636"
        />
      </div>
      <div class="cartItem__info-rows">
        <div class="cartItem__row">
          <div class="cartItem__row-item" :class="{ outOfStock: !isAvailable }">
            <nuxt-link v-if="productUrl" :to="productUrl">
              <h1 class="cartItem__name">{{ item.product.name }}</h1>
            </nuxt-link>
            <h1 v-else class="cartItem__name">{{ item.product.name }}</h1>
            <p class="cartItem__attributes">
              {{ productWeight }}
            </p>
          </div>
          <div v-if="editable" class="cartItem__row-item align-right">
            <Button class="cartItem__remove" @click="removeItem">
              <Icon name="IconCartRemove" />
            </Button>
          </div>
        </div>
        <div class="cartItem__row">
          <div class="cartItem__row-item" :class="{ outOfStock: !isAvailable }">
            <span class="cartItem__quantity">
              {{ $t('cart.quantity') }}: {{ item.quantity }}
            </span>
            <span
              v-if="!item.extra.shipping_availability"
              class="cartItem__available-shipping"
            >
              {{ $t('cart.shipping.localOnly') }}
            </span>
            <span
              v-if="item.extra.shipping_region_availability===false"
              class="cartItem__available-shipping"
            >
              {{ $t('cart.shipping.regionUnavailable') }}
            </span>

          </div>
          <div
            class="cartItem__row-item align-right"
            :class="{ outOfStock: !isAvailable }"
          >
            <span class="cartItem__price">{{ item.total }}</span>
          </div>
        </div>
      </div>
    </div>
    <Alert
      v-if="!isAvailable"
      class="cartItem__alert"
      :is-contained="true"
      :title="isAvailableLabel"
      :message="$t('cart.soldOut')"
    >
    </Alert>

    <HR v-if="!lastItem" :margins="36" />
  </article>
</template>

<script>
export default {
  props: {
    editable: { type: Boolean, default: true },
    item: {
      type: Object,
      required: true,
    },
    name: { type: String, default: '' },
    color: { type: String, default: '' },
    size: { type: String, default: '' },
    quantity: { type: String, default: '' },
    currency: { type: String, default: '' },
    price: { type: String, default: '' },
    outOfStock: { type: Boolean, default: false },
    lastItem: { type: Boolean, default: false },
  },
  computed: {
    productWeight() {
      if (this.item.product.weight) {
        return `${this.item.extra.weight} Kg`
      } else {
        return '0 Kg'
      }
    },
    productUrl() {
      if (this.item.product && this.item.product.base_product.url_path) {
        return this.item.product.base_product.url_path
      }
      return null
    },
    isAvailable() {
      return this.item.extra.availability
        ? this.item.extra.availability[0]
        : true
    },
    isAvailableLabel() {
      return this.item.extra.availability
        ? this.item.extra.availability[1]
        : 'Not available'
    },
  },
  methods: {
    removeItem() {
      this.$store.dispatch('basket/removeProductFromBasket', this.item)
    },
  },
}
</script>

<style lang="scss" scoped>
.cartItem__cols {
  display: grid;

  @include grid('mobile-large', $down: true) {
    row-gap: em(20);
  }

  @include grid('mobile-large') {
    grid-auto-flow: column;
    grid-template-columns: 90px 1fr;
    column-gap: em(56);
  }

  @include grid('tablet-large', $down: true) {
    column-gap: em(20);
  }
}

.cartItem__info-rows {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.cartItem__thumbnail {
  &.outOfStock {
    opacity: 0.3;
    pointer-events: none;
    user-select: none;
  }

  img {
    width: 100%;
  }
}

.cartItem__row {
  display: grid;
  grid-auto-flow: column;
  column-gap: em(22);
}

.cartItem__full-rows {
  display: grid;
}

.cartItem__row-item {
  &.outOfStock {
    opacity: 0.3;
    pointer-events: none;
    user-select: none;
  }
}

.cartItem__name {
  font-size: em(16);
  font-weight: var(--text-bold);
}

.cartItem__attributes {
  font-size: em(14);
}

.cartItem__quantity {
  font-size: em(14);
  white-space: nowrap;
}

.cartItem__available-shipping {
  color: var(--error-color);
  display: block;
  font-size: em(14);
}

.cartItem__price {
  font-weight: var(--text-heavy);
}

.cartItem__alert {
  margin-top: em(35);
}

.align-right {
  text-align: right;
}
</style>
