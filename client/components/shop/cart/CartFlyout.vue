<template>
  <div class="cartFlyout" :class="{ cartOpen: $store.state.ui.cartOpen }">
    <CartPanelBackdrop v-touch:swipe="onSwipe" @click="closeCart" />
    <CartPanel id="aria-cart-collapse" />
  </div>
</template>

<script>
export default {
  watch: {
    '$route.path'() {
      this.$store.commit('ui/setCartOpen', false)
    },
  },
  methods: {
    onSwipe(event) {
      if (event === 'right') {
        this.$store.commit('ui/setCartOpen', false)
      }
    },
    closeCart() {
      this.$store.commit('ui/setCartOpen', false)
    },
  },
}
</script>
