<template>
  <div class="cartItemList">
    <ul v-if="items.length" class="cartItemList__list">
      <li
        v-for="(item, index) in items"
        :key="item.id"
        class="cartItemList__list-item"
      >
        <CartItem
          :item="item"
          :last-item="index + 1 === items.length"
          :editable="editable"
        />
      </li>
    </ul>
    <div v-else>
      <p>{{ $t('cart.itemList.empty') }}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    items: { type: Array, default: () => [] },
    editable: { type: Boolean, default: true },
  },
}
</script>
