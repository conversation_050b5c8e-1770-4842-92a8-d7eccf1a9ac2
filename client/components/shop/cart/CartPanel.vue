<template>
  <article class="cartPanel">
    <div class="cartPanel__inner">
      <div class="cartPanel__header">
        <TitleH3>{{ $t('cart.panel.title') }}</TitleH3>
      </div>

      <div class="cartPanel__body">
        <CartItemList v-if="basket" :items="basket.items" />

        <HR :margins="40" class="black" />

        <CartTotal v-if="basket" :basket="basket" />
      </div>

      <div class="cartPanel__footer">
        <NLink :to="localePath('shop-cart')" class="button--primary">
          <ButtonLabel>{{ $t('cart.viewCart.buttonLabel') }}</ButtonLabel>
        </NLink>
      </div>
    </div>
  </article>
</template>

<script>
export default {
  computed: {
    basket() {
      return this.$store.state.basket.basket
    },
  },
}
</script>

<style lang="scss">
.cartPanel {
  min-height: 100vh;
  min-height: calc((var(--vh, 1vh) * 100));

  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: var(--page-bg-color);
  padding-top: em(70);
  padding-bottom: em(20);

  transform: translateX(100%);
  transition: transform calc(var(--speed) * 2) var(--easing);

  @include grid('laptop', $down: true) {
    margin-left: em(-26);
    margin-right: em(-26);
    padding-left: em(26);
    padding-right: em(26);
    // z-index: -1;
    max-width: 80vw;
    min-width: 40vw;
    width: 100%;
  }

  @include grid('laptop') {
    margin-left: em(-54);
    margin-right: em(-54);
    padding-left: em(26);
    padding-right: em(26);
    width: 33.3333%;
    z-index: 1;
  }

  @include grid('desktop-large') {
    margin-left: em(-96);
    margin-right: em(-96);
    padding-left: em(26);
    padding-right: em(26);
  }

  &:before {
    content: '';
    background-color: var(--black);
    opacity: 0.15;
    position: absolute;
    top: 0;
    left: 0;
    width: em(1);
    height: 100%;
  }

  .cartOpen & {
    transform: none;
  }
}

.cartPanel__header {
  margin-bottom: em(32, 25);
}

.cartPanel__inner {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.cartPanel__body {
  $scrollbarWidth: 4;
  flex: auto;

  overflow: hidden auto;
  padding-top: em(32);
  padding-bottom: em(32);
  margin-right: em(calc(-26 + $scrollbarWidth));
  padding-right: em(calc(26 - $scrollbarWidth));

  &::-webkit-scrollbar {
    width: em($scrollbarWidth);
    background-color: var(--scrollbar-bg-color);
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-color);
  }
  // firefox
  scrollbar-width: thin;
}

.cartPanel__footer {
  margin-top: em(32);
  margin-bottom: em(32);
}
</style>
