<template>
  <span
    class="orderStatusIndicator"
    :class="{
      isSuccess: orderStatus === 'isSuccess',
      isInProgress: orderStatus === 'isInProgress',
      isFailure: orderStatus === 'isFailure',
      isNotAvailable: orderStatus === 'isNotAvailable',
    }"
  ></span>
</template>

<script>
export default {
  props: {
    order: {
      type: Object,
      required: true,
    },
  },
  computed: {
    orderStatus() {
      if (this.order.status === 'CREATED') {
        return 'isInProgress'
      } else if (
        this.order.status === 'FAILED' ||
        this.order.status === 'CANCELLED'
      ) {
        return 'isFailure'
      } else if (
        this.order.status === 'PROCESSING' ||
        this.order.status === 'SHIPPED' ||
        this.order.status === 'COMPLETED'
      ) {
        return 'isSuccess'
      } else {
        return 'isNotAvailable'
      }
    },
  },
}
</script>

<style lang="scss">
.orderStatusIndicator {
  width: 100%;
  height: 100%;
  border-radius: em(9999);

  &.isSuccess {
    background-color: var(--order-success-color);
  }

  &.isInProgress {
    background-color: var(--order-in-progress-color);
  }

  &.isFailure {
    background-color: var(--order-failure-color);
  }

  &.isNotAvailable {
    background-color: var(--order-not-available-color);
  }
}
</style>
