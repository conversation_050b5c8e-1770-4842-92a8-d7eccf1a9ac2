<template>
  <div>
    <CartTotal :order="order" />

    <HR :margins="32" class="black" />

    <div v-if="!order.is_paid" class="orderTotal">
      <div class="amount-paid between">
        <span class="label">
          {{ $t('account.orderDetails.subtotal.label') }}
        </span>
        <span class="value">{{ order.amount_paid }}</span>
      </div>

      <div class="total between">
        <span class="label">
          {{ $t('account.orderDetails.total.label') }}
        </span>
        <span class="value total-amount">{{ order.amount_outstanding }}</span>
      </div>

      <Form :action="formAction" class="proceed" @success="onSuccess">
        <FormButton class="button--primary">
          {{ $t('account.orderDetails.proceed.buttonLabel') }}
        </FormButton>
        <p class="proceed-notice">
          {{ $t('account.orderDetails.proceed.notice') }}
        </p>
      </Form>
    </div>

    <div v-else class="orderTotal">
      <div class="total between">
        <span class="label">
          {{ $t('checkout.success.paidOn') }}
        </span>
        <span class="value total-amount">{{ formattedDate }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    order: { type: Object, required: true },
  },
  data: () => ({
    payload: {
      payment_method: 'redsys',
    },
  }),
  computed: {
    formattedDate() {
      const date = this.$dayjs(this.order.date_updated)
      const detailedDate =
        this.$dayjs(date).format('dddd') +
        ', ' +
        this.$dayjs(date).format('DD/MM/YYYY') +
        ', ' +
        this.$t('account.ordersList.date.at') +
        ' ' +
        this.$dayjs(date).format('HH:mm')
      return detailedDate
    },
  },
  methods: {
    async formAction() {
      return await this.$api.orders.payOrder(this.order.ref, this.payload)
    },
    onSuccess(data) {
      try {
        eval(JSON.parse(data.url)) // eslint-disable-line no-eval
      } catch (error) {
        this.$toast.error(this.$t('checkout.errorRedirecting'))
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.orderTotal {
  display: grid;
  row-gap: em(8);
  margin-bottom: em(48);
}

.between {
  display: grid;
  grid-auto-flow: column;
}

.amount-paid {
  font-weight: var(--text-bold);
  column-gap: em(16);
}

.total {
  font-size: em(18);
  font-weight: var(--text-heavy);
  column-gap: em(16, 18);
  align-items: baseline;
}
.total-amount {
  font-size: em(25, 18);
  font-weight: var(--text-heavy);
}

.value {
  text-align: right;
}

.proceed {
  display: grid;
  row-gap: em(8);
  margin-top: em(16);
}

.proceed-notice {
  font-size: em(12);
}
</style>
