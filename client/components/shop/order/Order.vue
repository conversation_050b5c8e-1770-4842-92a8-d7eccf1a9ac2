<template>
  <div class="order">
    <div class="column">
      <OrderInfo :order="order" />
      <OrderDelivery :order="order" />
    </div>
    <div class="column">
      <OrderItemList :items="order.items" />

      <HR :margins="32" class="black" />

      <OrderTotal :order="order" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    order: { type: Object, required: true },
  },
}
</script>

<style lang="scss" scoped>
.order {
  @include grid('tablet') {
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: 1fr 2fr;
    grid-gap: var(--layout-gap);
  }
}
</style>
