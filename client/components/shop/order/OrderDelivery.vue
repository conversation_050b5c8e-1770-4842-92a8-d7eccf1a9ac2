<template>
  <article class="deliveryDetails">
    <p class="deliveryDetails__title">
      {{ $t('account.orderDetails.title.orderInfo') }}
    </p>
    <div class="deliveryDetails__table">
      <!-- <div class="deliveryDetails__table-row">
        <div class="column">
          <span class="deliveryDetails__gray">
            {{ $t('account.orderDetails.title.suplier') }}
          </span>
        </div>
        <div class="column">
          <p v-html="companyAddress"></p>
          <p>
            <a :href="`mailto:${companyEmail}`">{{ companyEmail }}</a>
          </p>
        </div>
      </div>
      <HR :margins="24" /> -->

      <div class="deliveryDetails__table-row">
        <div class="column">
          <span class="deliveryDetails__gray">
            {{ $t('account.orderDetails.title.shippingAddress') }}
          </span>
        </div>
        <div class="column">
          <!-- eslint-disable-next-line vue/no-v-html -->
          <p v-html="formatAddress(order.shipping_address)"></p>
        </div>
      </div>
      <HR :margins="24" />

      <div class="deliveryDetails__table-row">
        <div class="column">
          <span class="deliveryDetails__gray">
            {{ $t('account.orderDetails.title.billingAddress') }}
          </span>
        </div>
        <div class="column">
          <!-- eslint-disable-next-line vue/no-v-html -->
          <p v-html="formatAddress(order.billing_address)"></p>
        </div>
      </div>
    </div>
  </article>
</template>

<script>
export default {
  props: {
    order: { type: Object, required: true },
  },
  computed: {
    config() {
      return this.$store.state.config.config
    },
    companyAddress() {
      const addr = this.config ? this.config.company_address : ''
      return addr.replaceAll('\n', '<br>')
    },
    companyEmail() {
      return this.config ? this.config.company_email : ''
    },
  },
  methods: {
    formatAddress(address) {
      const lines = address.split('\n')
      return lines.join('<br>')
    },
  },
}
</script>

<style lang="scss" scoped>
.deliveryDetails {
  margin-top: em(75);
  margin-bottom: em(75);
  font-size: em(14);
}
.deliveryDetails__title {
  font-size: em(21);
  font-weight: var(--text-bold);
  margin-bottom: em(24, 21);
}
.deliveryDetails__table {
  display: flex;
  flex-direction: column;
}
.deliveryDetails__table-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  column-gap: em(22);
}
.column {
  display: grid;
  row-gap: em(18);
}
.deliveryDetails__gray {
  color: var(--gray-b3);
}
.deliveryDetails__bold {
  font-weight: var(--text-bold);
}
</style>
