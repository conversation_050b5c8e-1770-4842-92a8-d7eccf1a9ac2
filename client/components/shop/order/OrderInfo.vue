<template>
  <article class="orderInfo">
    <div class="orderInfo__row">
      <div class="orderInfo__row-inner">
        <h2 class="orderInfo__title">{{ order.ref }}</h2>
        <p>
          <span class="orderInfo__small">
            {{ $t('account.orderDetails.date.label') }}
          </span>
          <span class="orderInfo__small orderInfo__bold">
            {{ formattedDate }}
          </span>
        </p>
      </div>
    </div>

    <div class="orderInfo__row">
      <div class="orderInfo__row-inner">
        <span class="orderInfo__status">
          <span class="orderInfo__bold">
            {{ order.status_display }}
          </span>
          <span class="orderInfo__disc">
            <Icon :size="11">
              <OrderStatusIndicator :order="order" />
            </Icon>
          </span>
        </span>

        <div v-if="order.status === 'PROCESSING'">
          <span class="orderInfo__small">
            {{ $t('account.orderDetails.status.processing') }}
          </span>
        </div>
        <div v-else-if="order.status === 'FAILED'">
          <span class="orderInfo__small">
            {{ $t('account.orderDetails.status.failed') }}
          </span>
        </div>
        <div v-else-if="order.status === 'CANCELLED'">
          <span class="orderInfo__small">
            {{ $t('account.orderDetails.status.cancelled') }}
          </span>
        </div>
        <div v-else-if="order.status === 'SHIPPED'">
          <span class="orderInfo__small">
            {{ $t('account.orderDetails.status.shipped') }}
          </span>
        </div>
        <div v-else-if="order.status === 'COMPLETED'">
          <span class="orderInfo__small">
            {{ $t('account.orderDetails.status.completed') }}
          </span>
        </div>
        <div v-else>
          <span class="orderInfo__small">
            {{ $t('account.orderDetails.status.created') }}
          </span>
        </div>
      </div>
    </div>
    <div v-if="trackingUrl" class="orderInfo__row">
      <a
        :href="trackingUrl"
        target="_blank"
        rel="noopener noreferrer"
        class="button--primary button--outline button--small"
      >
        {{ $t('account.orderDetails.tracking.buttonLabel') }}
      </a>
    </div>
  </article>
</template>

<script>
export default {
  props: {
    order: { type: Object, required: true },
  },
  computed: {
    trackingUrl() {
      if (this.order.extra.tracking_url && this.order.status !== 'COMPLETED') {
        return this.order.extra.tracking_url
      } else return null
    },
    formattedDate() {
      const date = this.$dayjs(this.order.date_updated) // needs to be date paid
      const detailedDate =
        this.$dayjs(date).format('dddd') +
        ', ' +
        this.$dayjs(date).format('DD/MM/YYYY') +
        ', ' +
        this.$t('account.ordersList.date.at') +
        ' ' +
        this.$dayjs(date).format('HH:mm')
      return detailedDate
    },
  },
}
</script>

<style lang="scss" scoped>
.orderInfo {
  display: grid;
  row-gap: em(24);
}
.orderInfo__row-inner {
  display: grid;
  row-gap: em(6);
}
.orderInfo__title {
  font-size: em(21);
  font-weight: var(--text-bold);
}
.orderInfo__small {
  font-size: em(14);
}
.orderInfo__bold {
  font-weight: var(--text-bold);
}
.orderInfo__status {
  display: flex;
  align-items: baseline;
}
.orderInfo__disc {
  margin-left: em(9);
}
</style>
