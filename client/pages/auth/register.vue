<template>
  <div id="page-register">
    <LayoutContainer>
      <FormContainer>
        <Header :title="title" :is-viewport-height="false"> </Header>

        <RegisterForm />

        <HR :margins="40" />

        <ExtraLinks
          :title="$t('auth.register.extraLinks.haveAccount')"
          :button-label="$t('auth.register.extraLinks.buttonLabel')"
          :button-url="localePath('auth-login')"
        />
      </FormContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  nuxtI18n: {
    paths: {
      es: '/autentacion/registrate',
    },
  },

  asyncData({ redirect, app }) {
    if (app.$auth.loggedIn) redirect(app.localePath('account'))
  },

  data() {
    return {
      title: this.$t('auth.register.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
}
</script>
