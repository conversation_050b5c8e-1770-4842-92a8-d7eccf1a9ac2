<template>
  <div></div>
</template>

<script>
export default {
  nuxtI18n: {
    paths: {
      es: '/autentacion/activar',
    },
  },
  data: () => ({
    activated: false,
  }),
  async fetch() {
    const postData = {
      uid: this.$route.query.u || '',
      token: this.$route.query.t || '',
    }
    try {
      const { data } = await this.$api.user.activate(postData)
      this.$toast.success(data.detail)
      this.activated = true
    } catch (e) {
      const errorData = e.response.data
      if (errorData.non_field_errors) {
        this.$toast.error(errorData.non_field_errors[0])
      } else {
        this.$toast.error(this.$t('auth.activate.error'))
      }
    }
  },
  fetchOnServer: false,
}
</script>
