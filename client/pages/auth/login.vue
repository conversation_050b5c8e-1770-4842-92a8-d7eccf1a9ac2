<template>
  <div id="page-login">
    <LayoutContainer>
      <FormContainer>
        <Header :title="title" :is-viewport-height="false"> </Header>

        <LoginForm />

        <HR :margins="40" />

        <ExtraLinks
          :title="$t('auth.login.extraLinks.noAccount')"
          :button-label="$t('auth.login.extraLinks.buttonLabel')"
          :button-url="localePath('auth-register')"
        />

        <Footer />
      </FormContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  nuxtI18n: {
    paths: {
      es: '/autentacion/acceso',
    },
  },

  asyncData({ redirect, app }) {
    if (app.$auth.loggedIn) redirect(app.localePath('account'))
  },

  data() {
    return {
      title: this.$t('auth.login.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
}
</script>
