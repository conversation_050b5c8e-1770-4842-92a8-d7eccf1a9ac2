<template>
  <div id="page-order-details">
    <LayoutContainer>
      <ContentContainer is-small>
        <Header :title="title" :is-viewport-height="false">
          <template #above>
            <BackToParentPage />
          </template>
        </Header>

        <Order :order="order" />
      </ContentContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  middleware: ['auth'],
  nuxtI18n: {
    paths: {
      es: '/cuenta/pedidos/:id',
    },
  },

  async asyncData({ params, $api, error }) {
    try {
      const { data } = await $api.orders.getOrder(params.id)
      return { order: data }
    } catch (e) {
      error({ statusCode: 404 })
    }
  },

  data() {
    return {
      order: null,
      title: this.$t('account.orderDetails.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
}
</script>

<style lang="scss">
.order__layout {
  @include grid('laptop', $down: true) {
    margin-bottom: em(32);
  }

  @include grid('laptop') {
    display: grid;
    grid-template-columns: 1fr 2fr;
    column-gap: 10%;
    margin-bottom: em(80);
  }
}
</style>
