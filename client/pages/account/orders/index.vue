<template>
  <div id="page-orders">
    <LayoutContainer>
      <ContentContainer is-small>
        <Header :title="title" :is-viewport-height="false"> </Header>
      </ContentContainer>

      <ContentContainer is-small>
        <AccountOrders v-if="items" :items="items" />

        <div v-else>
          <p>{{ $t('generic.noResults') }}</p>
        </div>
      </ContentContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  middleware: ['auth'],
  nuxtI18n: {
    paths: {
      es: '/cuenta/pedidos',
    },
  },
  async asyncData({ $api }) {
    try {
      const { data } = await $api.orders.getOrders()
      return { orders: data }
    } catch (error) {
      return { orders: null }
    }
  },

  data() {
    return {
      title: this.$t('account.ordersList.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },

  computed: {
    items() {
      return this.orders || []
    },
    hasOrders() {
      return this.orders.count ? this.orders : 0
    },
  },
}
</script>
