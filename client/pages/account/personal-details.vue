<template>
  <div id="page-personal-details">
    <LayoutContainer>
      <FormContainer v-if="changesSaved">
        <Response
          icon="IconBag"
          :title="$t('account.personalDetails.confirmation.title')"
          :message="$t('account.personalDetails.confirmation.subtitle')"
        >
          <Footer>
            <NLink :to="localePath('index')" class="button--primary">
              <ButtonLabel>
                {{ $t('account.personalDetails.confirmation.button') }}
              </ButtonLabel>
            </NLink>
          </Footer>
        </Response>
      </FormContainer>
      <FormContainer v-else>
        <Header :title="title" :is-viewport-height="false"> </Header>

        <PersonalDetailsForm />
      </FormContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  middleware: ['auth'],
  nuxtI18n: {
    paths: {
      es: '/cuenta/detalles-personales',
    },
  },

  data() {
    return {
      title: this.$t('account.personalDetails.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },

  computed: {
    changesSaved() {
      // return true
      return false
    },
  },
}
</script>
