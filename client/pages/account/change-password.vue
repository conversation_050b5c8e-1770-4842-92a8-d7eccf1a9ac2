<template>
  <div id="page-change-password">
    <LayoutContainer>
      <FormContainer>
        <Header :title="title" :is-viewport-height="false"> </Header>

        <ChangePasswordForm />

        <Footer />
      </FormContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  middleware: ['auth'],
  nuxtI18n: {
    paths: {
      es: '/autentacion/cambiar-la-contrasena',
    },
  },
  data() {
    return {
      title: this.$t('account.changePassword.head.title'),
    }
  },
  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
}
</script>
