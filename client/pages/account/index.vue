<template>
  <div id="page-account">
    <LayoutContainer>
      <ContentContainer is-small>
        <div>
          <Header :is-viewport-height="false">
            <HeaderTitle>
              {{ $t('account.welcome') }} {{ displayName }}
            </HeaderTitle>
          </Header>
        </div>
        <AccountItemList />
      </ContentContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  middleware: ['auth'],
  nuxtI18n: {
    paths: {
      es: '/cuenta',
    },
  },

  data() {
    return {
      title: this.$t('account.head.title'),
      displayName: '',
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },

  created() {
    if (this.$auth.loggedIn) {
      this.displayName = this.$auth.user.first_name || this.$auth.user.email
    }
  },
}
</script>

<style lang="scss" scoped>
.headerTitle {
  font-family: var(--font-family-title);
  font-size: em(40);
  font-weight: var(--title-black);
  line-height: var(--title-line-height);
  letter-spacing: -0.02em;
  text-transform: uppercase;

  @include grid('laptop', $down: true) {
    font-size: em(32);
  }
}
</style>
