<template>
  <div class="demo-page">
    <div class="demo-container">
      <div class="logo-container">
        <LogoBrand />
      </div>

      <h1 class="main-heading">MADKAT 5 YEARS</h1>

      <!-- Release Date Announcement -->
      <div class="release-announcement">
        <div class="release-date-container">
          <div class="release-label">NEW COLLECTION DROP</div>
          <div class="release-date">23.07.2025</div>
          <!-- <div class="release-tagline">Fresh designs, bold statements</div> -->
        </div>
      </div>

      <!-- Countdown Timer -->
      <!-- <div class="countdown-container">
        <div class="countdown-title">COUNTDOWN TO DROP</div>
        <div class="countdown-timer">
          <div class="countdown-unit">
            <div class="countdown-number">{{ countdown.days }}</div>
            <div class="countdown-label">DAYS</div>
          </div>
          <div class="countdown-separator">:</div>
          <div class="countdown-unit">
            <div class="countdown-number">{{ countdown.hours }}</div>
            <div class="countdown-label">HOURS</div>
          </div>
          <div class="countdown-separator">:</div>
          <div class="countdown-unit">
            <div class="countdown-number">{{ countdown.minutes }}</div>
            <div class="countdown-label">MINUTES</div>
          </div>
          <div class="countdown-separator">:</div>
          <div class="countdown-unit">
            <div class="countdown-number">{{ countdown.seconds }}</div>
            <div class="countdown-label">SECONDS</div>
          </div>
        </div>
      </div> -->

      <p class="subheading">
        NEW DESIGNS ARE <span class="highlight">COMING</span>
      </p>

      <iframe
        src="https://cdn.forms-content.sg-form.com/2a0ec614-42b4-11f0-8c2c-7a5def5acb71"
      />
      <!-- <p class="form-intro">Register to be the first to know.</p>
        <form class="subscribe-form" @submit.prevent="handleSubscription">
          <input
            v-model="email"
            type="email"
            placeholder="Your email address"
            required
            class="email-input"
            aria-label="Email Address"
          />
          <button type="submit" class="submit-button" aria-label="Subscribe">
            SUBMIT
          </button>
        </form> -->

      <div class="login-link-container">
        <NLink :to="localePath('account-orders')" class="login-link">
          Manage your account or view orders
        </NLink>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'blank',
  nuxtI18n: false,
  data() {
    return {
      email: '',
      countdown: {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      },
      countdownInterval: null,
      launchDate: new Date('2025-07-23T00:00:00').getTime(),
    }
  },
  mounted() {
    this.updateCountdown()
    this.countdownInterval = setInterval(this.updateCountdown, 1000)
  },
  beforeDestroy() {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval)
    }
  },
  methods: {
    updateCountdown() {
      const now = new Date().getTime()
      const distance = this.launchDate - now

      if (distance > 0) {
        this.countdown.days = Math.floor(distance / (1000 * 60 * 60 * 24))
        this.countdown.hours = Math.floor(
          (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        )
        this.countdown.minutes = Math.floor(
          (distance % (1000 * 60 * 60)) / (1000 * 60)
        )
        this.countdown.seconds = Math.floor((distance % (1000 * 60)) / 1000)
      } else {
        // Launch date has passed
        this.countdown = { days: 0, hours: 0, minutes: 0, seconds: 0 }
        if (this.countdownInterval) {
          clearInterval(this.countdownInterval)
        }
      }
    },
    handleSubscription() {
      alert(`Thank you! We will notify ${this.email} when we are back.`)
      this.email = ''
    },
  },
}
</script>

<style scoped>
/* Base Styles */
.demo-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f8f8f8; /* Clean, light off-white */

  text-align: center;
  padding: 1.5rem;
  color: #2c3e50; /* A softer, professional dark color */
}

.demo-container {
  max-width: 550px;
  width: 100%;
}

/* 1. Logo */
.logo-container {
  margin-bottom: 4rem;
  /* Assuming your LogoBrand component has its own styling */
}

/* 2. Main Heading */
.main-heading {
  font-size: 1.25rem; /* 20px */
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.2em; /* Wide spacing for a premium feel */
  color: #34495e;
  margin: 0 0 1rem;
}

/* 3. Subheading */
.subheading {
  font-size: 1rem; /* 16px */
  margin: 0 0 4rem;
  color: #7f8c8d;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.subheading span {
  color: #2c3e50;
  font-weight: 700;
}

.highlight {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
  font-weight: 800;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Release Date Announcement */
.release-announcement {
  margin: 3rem 0;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.release-announcement::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.release-date-container {
  position: relative;
  z-index: 1;
}

.release-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.2em;
  margin-bottom: 0.5rem;
}

.release-date {
  font-size: 3rem;
  font-weight: 900;
  color: white;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-bottom: 0.5rem;
  letter-spacing: 0.1em;
}

.release-tagline {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
  font-weight: 300;
}

/* Countdown Timer */
.countdown-container {
  margin: 3rem 0;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.countdown-title {
  font-size: 1rem;
  font-weight: 600;
  color: #34495e;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  margin-bottom: 1.5rem;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.countdown-unit {
  text-align: center;
  min-width: 60px;
}

.countdown-number {
  font-size: 2.5rem;
  font-weight: 900;
  color: #2c3e50;
  line-height: 1;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse 2s ease-in-out infinite;
}

.countdown-label {
  font-size: 0.7rem;
  font-weight: 600;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-top: 0.25rem;
}

.countdown-separator {
  font-size: 2rem;
  font-weight: 300;
  color: #bdc3c7;
  animation: blink 1s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.3;
  }
}

/* 4. Underline-Style Form */
.form-container {
  margin-bottom: 3rem;
}

.form-intro {
  font-size: 1rem;
  margin: 0 0 1.5rem;
  color: #34495e;
  font-weight: 500;
}

.subscribe-form {
  display: flex;
  align-items: center;
  /* The underline effect is achieved with a border-bottom */
  border-bottom: 2px solid #bdc3c7;
  transition: border-color 0.3s ease;
}

.subscribe-form:focus-within {
  border-color: #2c3e50;
}

.email-input {
  flex-grow: 1; /* Input takes up all available space */
  border: none;
  background: transparent;
  padding: 0.75rem 0.25rem;
  font-size: 1rem;
  color: #2c3e50;
  width: 100%;
}

.email-input::placeholder {
  color: #95a5a6;
  font-weight: 400;
}

.email-input:focus {
  outline: none;
}

.submit-button {
  flex-shrink: 0;
  background: transparent;
  border: none;
  color: #34495e;
  font-weight: 700;
  font-size: 0.8rem;
  letter-spacing: 0.1em;
  cursor: pointer;
  padding: 0.75rem 0 0.75rem 1rem;
  transition: color 0.3s ease;
}

.submit-button:hover {
  color: #000;
}

/* 5. Login Link */
.login-link-container {
  margin-top: 1rem;
}

.login-link {
  color: #7f8c8d;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  border-bottom: 1px dotted #7f8c8d;
  transition: color 0.3s ease, border-color 0.3s ease;
}

.login-link:hover {
  color: #2c3e50;
  border-color: #2c3e50;
}

iframe {
  width: 100%;
  height: 35vh;
  overflow: hidden;
  border: none;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .main-heading {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .release-announcement {
    margin: 2rem 0;
    padding: 1.5rem;
  }

  .release-date {
    font-size: 2.2rem;
  }

  .release-label {
    font-size: 0.8rem;
  }

  .release-tagline {
    font-size: 0.9rem;
  }

  .countdown-container {
    margin: 2rem 0;
    padding: 1.5rem;
  }

  .countdown-timer {
    gap: 0.5rem;
  }

  .countdown-number {
    font-size: 1.8rem;
  }

  .countdown-unit {
    min-width: 45px;
  }

  .countdown-separator {
    font-size: 1.5rem;
  }

  .subheading {
    font-size: 0.9rem;
    margin-bottom: 2rem;
  }

  iframe {
    height: 30vh;
  }
}

@media (max-width: 480px) {
  .countdown-timer {
    flex-direction: column;
    gap: 1rem;
  }

  .countdown-separator {
    display: none;
  }

  .release-date {
    font-size: 1.8rem;
  }

  .countdown-number {
    font-size: 1.5rem;
  }
}
</style>
