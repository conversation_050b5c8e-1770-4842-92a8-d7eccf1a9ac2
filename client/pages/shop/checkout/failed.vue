<template>
  <div id="page-checkout-failed">
    <LayoutContainer>
      <FormContainer>
        <Response
          icon="IconBagX"
          :title="$t('checkout.failed.title')"
          :message="$t('checkout.failed.message')"
        >
          <Footer>
            <NLink :to="localePath('shop-cart')" class="button--primary">
              <ButtonLabel> {{ $t('cart.goToCart.buttonLabel') }} </ButtonLabel>
            </NLink>
          </Footer>
        </Response>
      </FormContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  nuxtI18n: {
    paths: {
      es: '/tienda/comprar/fallida',
    },
  },

  data() {
    return {
      title: this.$t('checkout.failed.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
}
</script>
