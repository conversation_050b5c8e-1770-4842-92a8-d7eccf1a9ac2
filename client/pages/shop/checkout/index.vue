<template>
  <div id="page-checkout">
    <LayoutContainer v-if="basket">
      <ContentContainer v-if="basketHasItems" is-small>
        <Header :title="title" :is-viewport-height="false"> </Header>
        <CheckoutForm />
      </ContentContainer>

      <FormContainer v-else>
        <Response
          :icon="'IconBagPlus'"
          :title="$t('cart.empty.title')"
          :message="$t('cart.empty.message')"
        >
          <Footer>
            <NLink :to="localePath('index')" class="button--primary">
              <ButtonLabel> {{ $t('cart.empty.buttonLabel') }} </ButtonLabel>
            </NLink>
          </Footer>
        </Response>
      </FormContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  nuxtI18n: {
    paths: {
      es: '/tienda/comprar',
    },
  },

  data() {
    return {
      title: this.$t('checkout.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
  computed: {
    basket() {
      return this.$store.state.basket.basket
    },
    basketHasItems() {
      return this.basket && this.basket.items.length > 0
    },
  },
  async mounted() {
    await this.$store.dispatch('basket/initBasket')
  },
}
</script>
