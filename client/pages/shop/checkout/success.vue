<template>
  <div id="page-checkout-success">
    <LayoutContainer>
      <ContentContainer v-if="order" is-small>
        <Header :title="title"> </Header>
        <Order :order="order" />
      </ContentContainer>
      <ContentContainer v-else is-small>
        <Header :title="$t('account.head.notFound')"> </Header>
      </ContentContainer>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  nuxtI18n: {
    paths: {
      es: '/tienda/comprar/exito',
    },
  },
  async asyncData({ query, $api }) {
    try {
      const { data } = await $api.orders.getLastOrder(query.token)
      return { order: data }
    } catch (error) {
      return { order: null }
    }
  },

  data() {
    return {
      title: this.$t('checkout.success.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
  mounted() {
    // Clear any payment backup data on successful checkout
    this.clearPaymentBackup()
  },
  methods: {
    clearPaymentBackup() {
      if (process.client) {
        localStorage.removeItem('checkout_basket_backup')
      }
    },
  },
}
</script>
