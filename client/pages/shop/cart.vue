<template>
  <div id="page-cart">
    <LayoutContainer v-if="basket">
      <FormContainer v-if="basket.items.length === 0">
        <Response
          :icon="'IconBagPlus'"
          :title="$t('cart.empty.title')"
          :message="$t('cart.empty.message')"
        >
          <Footer>
            <a :href="localePath('index')" class="button--primary">
              <ButtonLabel> {{ $t('cart.empty.buttonLabel') }} </ButtonLabel>
            </a>
          </Footer>
        </Response>
      </FormContainer>

      <div v-else>
        <ContentContainer is-small>
          <Header :title="title" :is-viewport-height="false"> </Header>
        </ContentContainer>

        <Cart />
      </div>
    </LayoutContainer>
  </div>
</template>

<script>
export default {
  nuxtI18n: {
    paths: {
      es: '/tienda/carro',
    },
  },

  data() {
    return {
      title: this.$t('cart.head.title'),
    }
  },

  head() {
    return {
      title: this.title + ' - ' + this.$store.state.config.siteName,
    }
  },
  computed: {
    basket() {
      return this.$store.state.basket.basket
    },
  },
  async mounted() {
    await this.$store.dispatch('basket/initBasket')
  },
}
</script>

<style lang="scss">
.cartBody {
  @include grid('laptop') {
    padding-bottom: em(80);
  }
}
.cartBody__inner {
  @include grid('laptop') {
    display: flex;
  }
}
.cartBody__start {
  @include grid('laptop') {
    width: 60%;
  }
}
.cartBody__middle {
  @include grid('laptop') {
    width: 10%;
  }
}
.cartBody__end {
  @include grid('laptop') {
    width: 30%;
  }
}
</style>
