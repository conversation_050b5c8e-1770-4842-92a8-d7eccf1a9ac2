<template>
  <main v-if="!isLoading">
    <CollectionPage v-if="pageType === 'catalog.ProductCollection'" />
    <!-- <ProductPage v-else-if="isProductPage" /> -->
    <ProductDetailGufram v-else-if="isProductPage" :page="page" />

    <HomePage v-else-if="pageType === 'home.HomePage'" :products="products" />
    <BasicPage v-else-if="pageType === 'basic.BasicPage'" />
  </main>
</template>

<script>
import { mapState } from 'vuex'

export default {
  async asyncData({ store, route, error, app }) {
    // Turn the loader ON immediately
    store.commit('page/setIsLoading', true)

    try {
      await store.dispatch('page/fetchPageByPath', route.fullPath)
      const pageData = store.state.page.page

      if (pageData.translated_urls) {
        const urls = {}
        for (const [lang, value] of Object.entries(pageData.translated_urls)) {
          if (lang !== app.i18n.locale) {
            urls[lang] = { pathMatch: value.replace(`/${lang}/`, '') }
          }
        }
        await store.dispatch('i18n/setRouteParams', urls)
      }
    } catch (e) {
      error({ statusCode: 404 })
    } finally {
      // Turn the loader OFF only when everything is done, success or fail.
      store.commit('page/setIsLoading', false)
    }
  },

  data() {
    return {
      products: [], // Initialize products as reactive property
    }
  },

  async fetch() {
    // Fetch products for homepage - this runs on every navigation including back/forward
    if (this.pageType === 'home.HomePage' && this.page.collections) {
      const products = []

      // Filter collections to only include homepage collections for 3D view
      const homepageCollections = this.page.collections.filter(
        (collection) => collection.is_homepage_collection
      )

      // Use homepage collections if any exist, otherwise fall back to all collections
      const collectionsToFetch =
        homepageCollections.length > 0
          ? homepageCollections
          : this.page.collections

      const collectionPromises = collectionsToFetch.map((collection) =>
        this.$api.pages.getPage(collection.id)
      )

      try {
        const collectionResponses = await Promise.all(collectionPromises)
        collectionResponses.forEach((response) => {
          if (
            response.data &&
            response.data.products &&
            response.data.products.results
          ) {
            products.push(...response.data.products.results)
          }
        })

        this.products = products
      } catch (error) {
        this.products = []
      }
    }
  },

  head() {
    const linkCanonical = [
      {
        rel: 'canonical',
        href: `${this.metaBaseUrl}`,
      },
    ]
    const meta = [
      {
        hid: 'description',
        name: 'description',
        content: `${this.metaDescription}`,
      },
      {
        hid: 'og:title',
        name: 'og:title',
        content: `${this.page.search_title}`,
      },
      {
        hid: 'og:description',
        name: 'og:description',
        content: `${this.metaDescription}`,
      },
      { hid: 'og:url', name: 'og:url', content: `${this.page.url_full}` },
    ]
    if (this.page.search_image) {
      meta.push({
        hid: 'og:image',
        name: 'og:image',
        content: this.page.search_image,
      })
    } else {
      meta.push({
        hid: 'og:image',
        property: 'og:image',
        content: `${this.metaBaseUrl}/images/banner3.png`,
      })
    }

    let title = this.page.search_title
    if (this.pageType !== 'home.HomePage') {
      title += ' - ' + this.$store.state.config.siteName
    }
    return {
      title,
      link: linkCanonical,
      meta,
    }
  },
  computed: {
    isLoading() {
      return this.$store.state.page.isLoading
    },
    ...mapState('page', ['page', 'pageType']),

    metaBaseUrl() {
      return this.$store.state.config.rootUrl
        ? this.$store.state.config.rootUrl
        : ''
    },
    metaDescription() {
      return this.page.search_description
        ? this.page.search_description
        : this.$store.state.config.siteName
    },
    isProductPage() {
      return (
        this.pageType === 'catalog.ColorProduct' ||
        this.pageType === 'catalog.NumericSizeColorProduct' ||
        this.pageType === 'catalog.NumericSizeProduct' ||
        this.pageType === 'catalog.SingleProduct' ||
        this.pageType === 'catalog.SizeColorProduct' ||
        this.pageType === 'catalog.SizeProduct'
      )
    },
  },
}
</script>
