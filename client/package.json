{"name": "le<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext \".js,.vue\" --ignore-path .gitignore .", "lint": "yarn lint:js"}, "dependencies": {"@nuxtjs/auth-next": "5.0.0-1624817847.21691f1", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/dayjs": "^1.4.1", "@nuxtjs/i18n": "^7.0.3", "@nuxtjs/toast": "^3.3.1", "bourbon": "^7.0.0", "cookie-universal-nuxt": "^2.1.5", "core-js": "^3.15.1", "cross-env": "^7.0.3", "debounce": "^1.2.1", "gsap": "^3.13.0", "hooper": "^0.3.4", "jwt-decode": "^3.1.2", "normalize.css": "^8.0.1", "nuxt": "^2.15.7", "nuxt-webfontloader": "^1.1.0", "qs": "^6.10.1", "three": "^0.178.0", "vee-validate": "^3.4.13", "vue-lazyload": "^1.3.3", "vue-zoom-on-hover": "^1.0.6", "vue2-touch-events": "^3.2.2"}, "devDependencies": {"@babel/eslint-parser": "^7.14.7", "@nuxtjs/device": "^2.1.0", "@nuxtjs/eslint-config": "^6.0.1", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/google-analytics": "^2.4.0", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/style-resources": "^1.2.1", "eslint": "^7.29.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-vue": "^7.12.1", "postcss-loader": "^3.0.0", "prettier": "^2.3.2", "sass": "^1.43.4", "sass-loader": "10"}}