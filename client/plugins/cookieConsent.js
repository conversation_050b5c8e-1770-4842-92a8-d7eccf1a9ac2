/**
 * <PERSON>ie Consent Management Plugin
 * Provides utilities for managing GDPR-compliant cookie preferences
 */

export default ({ app }, inject) => {
  const cookieConsent = {
    // <PERSON>ie keys
    CONSENT_KEY: 'cookielaw',
    PREFERENCES_KEY: 'cookie-preferences',
    
    // Default preferences
    defaultPreferences: {
      necessary: true,
      analytics: false,
      marketing: false,
    },

    /**
     * Get current cookie preferences
     * @returns {Object} Current preferences object
     */
    getPreferences() {
      const preferences = app.$cookies.get(this.PREFERENCES_KEY)
      return preferences ? { ...this.defaultPreferences, ...preferences } : this.defaultPreferences
    },

    /**
     * Check if user has given consent
     * @returns {Boolean} True if consent has been given
     */
    hasConsent() {
      return !!app.$cookies.get(this.CONSENT_KEY)
    },

    /**
     * Check if specific cookie category is allowed
     * @param {String} category - Cookie category (necessary, analytics, marketing)
     * @returns {Boolean} True if category is allowed
     */
    isAllowed(category) {
      if (category === 'necessary') return true // Always allowed
      const preferences = this.getPreferences()
      return preferences[category] || false
    },

    /**
     * Save cookie preferences
     * @param {Object} preferences - Preferences object
     */
    savePreferences(preferences) {
      const finalPreferences = { ...this.defaultPreferences, ...preferences }
      finalPreferences.necessary = true // Always true
      
      app.$cookies.set(this.CONSENT_KEY, true, {
        path: '/',
        maxAge: 60 * 60 * 24 * 365, // 1 year
      })
      
      app.$cookies.set(this.PREFERENCES_KEY, finalPreferences, {
        path: '/',
        maxAge: 60 * 60 * 24 * 365, // 1 year
      })

      // Emit event for components to react
      if (app.$nuxt) {
        app.$nuxt.$emit('cookie-preferences-updated', finalPreferences)
      }
    },

    /**
     * Accept all cookies
     */
    acceptAll() {
      this.savePreferences({
        necessary: true,
        analytics: true,
        marketing: true,
      })
    },

    /**
     * Reject all non-necessary cookies
     */
    rejectAll() {
      this.savePreferences({
        necessary: true,
        analytics: false,
        marketing: false,
      })
    },

    /**
     * Reset cookie consent (for testing purposes)
     */
    reset() {
      app.$cookies.remove(this.CONSENT_KEY, { path: '/' })
      app.$cookies.remove(this.PREFERENCES_KEY, { path: '/' })
    },

    /**
     * Initialize analytics based on preferences
     */
    initializeAnalytics() {
      if (this.isAllowed('analytics')) {
        // Initialize Google Analytics, etc.
        console.log('Analytics cookies allowed - initializing tracking')
      } else {
        console.log('Analytics cookies not allowed - skipping tracking')
      }
    },

    /**
     * Initialize marketing tools based on preferences
     */
    initializeMarketing() {
      if (this.isAllowed('marketing')) {
        // Initialize marketing pixels, etc.
        console.log('Marketing cookies allowed - initializing marketing tools')
      } else {
        console.log('Marketing cookies not allowed - skipping marketing tools')
      }
    },
  }

  // Inject the cookie consent utility
  inject('cookieConsent', cookieConsent)
}
