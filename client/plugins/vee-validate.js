import { extend } from 'vee-validate'
import { required, email, confirmed } from 'vee-validate/dist/rules'

export default ({ app }) => {
  extend('required', {
    ...required,
    message: () => app.i18n.t('validate.required'),
  })
  extend('email', {
    ...email,
    message: () => app.i18n.t('validate.email'),
  })
  extend('password', {
    validate(value) {
      const len = value.length
      return len >= 8 && len < 100
    },
    message: () => app.i18n.t('validate.password'),
  })
  extend('confirmed', {
    ...confirmed,
    message: () => app.i18n.t('validate.confirmPassword'),
  })
  extend('isTrue', {
    ...required,
    params: [{ name: 'allowFalse', default: false }],
    message: () => app.i18n.t('validate.required.checkbox'),
  })
  extend('noHash', {
    validate(value) {
      return !value.includes('#')
    },
    message: () => app.i18n.t('validate.noHashtag'),
  })
  extend('min', {
    validate(value, { length }) {
      return value.length >= length
    },
    params: ['length'],
    message: (field, { length }) =>
      app.i18n.t('validate.minLength', { length }),
  })
  extend('max', {
    validate(value, { length }) {
      return value.length <= length
    },
    params: ['length'],
    message: (field, { length }) =>
      app.i18n.t('validate.maxLength', { length }),
  })
  extend('rfc', {
    validate(value) {
      if (!value) return true // RFC is optional

      // Remove spaces and convert to uppercase
      const cleanRFC = value.replace(/\s/g, '').toUpperCase()

      // RFC should be 12-13 characters long
      if (cleanRFC.length < 12 || cleanRFC.length > 13) return false

      // RFC should be alphanumeric
      if (!/^[A-Z0-9]+$/.test(cleanRFC)) return false

      return true
    },
    message: () => app.i18n.t('validate.rfc'),
  })
  // extend('complexHouseNumber', {
  //   validate(value) {
  //     // Check if there's a number in the string
  //     const hasNumber = /\d/.test(value)
  //     if (!hasNumber) return false

  //     // Split at first number
  //     const parts = value.split(/(\d+)/)

  //     // Check if the part after the number is <= 20 characters
  //     if (parts.length > 1 && parts.slice(1).join('').length > 20) return false

  //     return true
  //   },
  //   message: () => app.i18n.t('validate.invalidHouseNumber'),
  // })
}
