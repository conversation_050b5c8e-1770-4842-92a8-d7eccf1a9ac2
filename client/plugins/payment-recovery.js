export default ({ app, store }) => {
  // Check for payment recovery on app initialization
  if (process.client) {
    app.router.afterEach((to, from) => {
      // Check if user is returning from payment or on checkout success/failed pages
      if (to.path.includes('/checkout/success') || to.path.includes('/checkout/failed') || to.path.includes('/payment/')) {
        checkPaymentRecovery(store, app)
      }
    })
  }
}

function checkPaymentRecovery(store, app) {
  try {
    const backupData = localStorage.getItem('checkout_basket_backup')
    if (!backupData) return

    const backup = JSON.parse(backupData)
    const now = Date.now()
    const timeSincePayment = (now - backup.timestamp) / 1000 // seconds

    // If more than 3 minutes have passed since payment started
    if (timeSincePayment > 180) {
      // Clear the backup data
      localStorage.removeItem('checkout_basket_backup')
      
      // Show warning about potential discount expiration
      if (backup.discountCode) {
        app.$toast.warning(app.i18n.t('payment.recovery.discountMayHaveExpired'))
      }
      
      // Log the potential issue for debugging
      console.warn('Payment took longer than 3 minutes, discount reservation may have expired', {
        basketId: backup.basketId,
        discountCode: backup.discountCode,
        timeSincePayment
      })
    } else {
      // Payment completed within time limit, clear backup
      localStorage.removeItem('checkout_basket_backup')
    }
  } catch (error) {
    console.error('Error during payment recovery check:', error)
    // Clear potentially corrupted data
    localStorage.removeItem('checkout_basket_backup')
  }
}
