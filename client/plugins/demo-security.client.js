export default ({ app, store, route }) => {
  // Only run if demo token is required
  if (!process.env.DEMO_TOKEN) {
    return
  }

  // Prevent back button access to protected pages
  if (typeof window !== 'undefined') {
    // Set cache control headers to prevent page caching
    const preventCache = () => {
      // Add meta tags to prevent caching
      const metaTags = [
        {
          name: 'Cache-Control',
          content: 'no-cache, no-store, must-revalidate, private',
        },
        { name: 'Pragma', content: 'no-cache' },
        { name: 'Expires', content: '0' },
      ]

      metaTags.forEach((tag) => {
        let meta = document.querySelector(`meta[http-equiv="${tag.name}"]`)
        if (!meta) {
          meta = document.createElement('meta')
          meta.setAttribute('http-equiv', tag.name)
          document.head.appendChild(meta)
        }
        meta.setAttribute('content', tag.content)
      })

      // Also try to set headers on the document
      try {
        if (document.head) {
          const style = document.createElement('style')
          style.textContent = `
            /* Force browser to not cache this page */
            html {
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;
            }
          `
          document.head.appendChild(style)
        }
      } catch (e) {
        // Ignore errors
      }
    }

    // Handle page visibility change (when user comes back to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page is visible again, check token validity
        const token = app.$cookies.get('demotoken')
        const isValidToken = String(token) === String(process.env.DEMO_TOKEN)

        if (!isValidToken) {
          // Invalid token, redirect to demo page
          const whitelistedPaths = [
            '/es/auth',
            '/en/auth',
            '/demo',
            '/es/account',
            '/en/account',
          ]

          const currentPath = window.location.pathname
          const isWhitelisted = whitelistedPaths.some((path) =>
            currentPath.startsWith(path)
          )

          if (!isWhitelisted) {
            window.location.href = '/demo/'
          }
        }
      }
    }

    // Handle browser back/forward navigation
    const handlePopState = (event) => {
      const token = app.$cookies.get('demotoken')
      const isValidToken = String(token) === String(process.env.DEMO_TOKEN)

      if (!isValidToken) {
        const whitelistedPaths = [
          '/es/auth',
          '/en/auth',
          '/demo',
          '/es/account',
          '/en/account',
        ]

        const currentPath = window.location.pathname
        const isWhitelisted = whitelistedPaths.some((path) =>
          currentPath.startsWith(path)
        )

        if (!isWhitelisted) {
          // Prevent the navigation and redirect immediately
          event.preventDefault()
          event.stopPropagation()

          // Clear history and redirect
          if (window.history) {
            window.history.pushState(null, '', '/demo/')
          }
          window.location.replace('/demo/')
          return false
        }
      }
    }

    // Handle page unload to clear history
    const handleBeforeUnload = () => {
      const token = app.$cookies.get('demotoken')
      const isValidToken = String(token) === String(process.env.DEMO_TOKEN)

      if (!isValidToken) {
        // Clear the page from browser cache
        if (window.history && window.history.replaceState) {
          window.history.replaceState(null, '', window.location.href)
        }
      }
    }

    // Periodic token validation
    const validateTokenPeriodically = () => {
      const token = app.$cookies.get('demotoken')
      const isValidToken = String(token) === String(process.env.DEMO_TOKEN)

      if (!isValidToken) {
        const whitelistedPaths = [
          '/es/auth',
          '/en/auth',
          '/demo',
          '/es/account',
          '/en/account',
        ]

        const currentPath = window.location.pathname
        const isWhitelisted = whitelistedPaths.some((path) =>
          currentPath.startsWith(path)
        )

        if (!isWhitelisted) {
          window.location.replace('/demo/')
        }
      }
    }

    // Apply cache prevention
    preventCache()

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('popstate', handlePopState)
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Add additional security listeners
    window.addEventListener('pageshow', handleVisibilityChange)
    window.addEventListener('focus', handleVisibilityChange)

    // Periodic validation every 5 seconds
    const tokenCheckInterval = setInterval(validateTokenPeriodically, 5000)

    // Cleanup function (though this won't run in most cases)
    const cleanup = () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('popstate', handlePopState)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('pageshow', handleVisibilityChange)
      window.removeEventListener('focus', handleVisibilityChange)
      if (tokenCheckInterval) {
        clearInterval(tokenCheckInterval)
      }
    }

    // Store cleanup function for potential future use
    if (app.$demoSecurity) {
      app.$demoSecurity.cleanup = cleanup
    } else {
      app.$demoSecurity = { cleanup }
    }
  }
}
