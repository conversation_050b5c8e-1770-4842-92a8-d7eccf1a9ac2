{"mainNav.logout": "Logout", "mainNav.currency": "Currency: ", "cookieMessage.title": "WE CARE ABOUT YOUR PRIVACY", "cookieMessage.text": "We use cookies and similar technologies to provide the best experience on our website.", "cookieMessage.externalLink": "What are cookies?", "cookieMessage.button.ok": "OK", "cookieMessage.button.no": "No, thanks", "account.welcome": "Hi, ", "account.logout": "Logout", "account.myOrders.title": "My Orders", "account.myOrders.description": "Track your orders here.", "account.myDetails.title": "Personal Details", "account.myDetails.description": "Edit your personal details such as email address and password here.", "account.changePassword.title": "Change Password", "account.changePassword.description": "Change your password here.", "account.changePassword.header.title": "Change Password", "account.changePassword.form.buttonLabel": "Submit request", "account.ordersList.title": "My Orders", "account.ordersList.code.title": "Order", "account.ordersList.date.at": "at", "account.ordersList.status.title": "Order status:", "account.ordersList.buttonLabel": "View Order", "account.personalDetails.address.title": "Default shipping address", "account.personalDetails.clearAddress.buttonLabel": "Clear address", "account.personalDetails.saveChanges.buttonLabel": "Save Changes", "account.personalDetails.confirmation.title": "Changes saved!", "account.personalDetails.confirmation.subtitle": "Your changes have been saved.", "account.personalDetails.confirmation.button": "Continue Shopping", "account.orderDetails.date.label": "Issue date:", "account.orderDetails.status.created": "Your order will be shipped soon.", "account.orderDetails.status.failed": "Your order was not successful.", "account.orderDetails.status.cancelled": "This order was cancelled.", "account.orderDetails.status.processing": "This order is being processed.", "account.orderDetails.status.shipped": "This order was shipped.", "account.orderDetails.status.completed": "This order was completed.", "account.orderDetails.status.refunded": "This order was refunded.", "account.orderDetails.tracking.buttonLabel": "Track your order", "account.orderDetails.title.orderInfo": "Order Info", "account.orderDetails.title.suplier": "Supplier", "account.orderDetails.title.shippingAddress": "Shipping address", "account.orderDetails.title.billingAddress": "Billing address", "account.orderDetails.subtotal.label": "Amount Paid:", "account.orderDetails.total.label": "Outstanding Amount:", "account.orderDetails.proceed.buttonLabel": "Proceed to Payment", "account.orderDetails.proceed.notice": "You will be redirected to a page where you can complete your purchase safely.", "account.orderDetails.proceed.checkout": "Reservation guaranteed for 3 minutes once checkout begins", "account.head.notFound": "Order not found", "account.head.title": "Account", "account.ordersList.head.title": "My Orders", "account.orderDetails.head.title": "Order Details", "account.personalDetails.head.title": "Personal Details", "account.changePassword.head.title": "Change Password", "auth.login.header.title": "<PERSON><PERSON>", "auth.login.form.forgotPassword": "Forgot your password?", "auth.login.form.buttonLabel": "<PERSON><PERSON>", "auth.login.extraLinks.noAccount": "Don't have an account?", "auth.login.extraLinks.buttonLabel": "Create Account", "auth.register.header.title": "Register", "auth.register.form.buttonLabel": "Register", "auth.register.extraLinks.haveAccount": "Already have an account?", "auth.register.extraLinks.buttonLabel": "<PERSON><PERSON>", "auth.resetPassword.header.title": "Request Password Reset", "auth.resetPassword.form.buttonLabel": "Submit request", "auth.resetPasswordRedirect.header.title": "Check Email", "auth.resetPasswordRedirect.details": "The Password reset link sent to your Email address", "auth.resetPasswordConfirm.header.title": "Choose and confirm password", "auth.resetPasswordConfirm.form.buttonLabel": "Submit request", "auth.resetPasswordConfirm.form.invalidUrl": "Invalid URL provided, please try to reset password again", "auth.login.head.title": "<PERSON><PERSON>", "auth.register.head.title": "Register", "auth.resetPassword.head.title": "Request Password Reset", "auth.resetPasswordConfirm.head.title": "Reset Password", "cart.head.title": "Your Cart", "cart.panel.title": "Your Cart", "cart.added": "Item added to cart", "cart.addError.generic": "Failed to add item to cart", "cart.addError.notAvailable": "This product is not available", "cart.addError.outOfStock": "This product is out of stock", "cart.addError.noVariant": "Please select a variant first", "cart.quantity": "Quantity", "cart.soldOut": "Sold out", "cart.subtotal": "Subtotal", "cart.total": "Total", "cart.includes_vat": "Includes {amount} tax ({rate}% VAT)", "cart.vat_breakdown_toggle": "View tax breakdown", "cart.subtotal_before_vat": "Subtotal (before VAT)", "cart.vat_amount": "VAT ({rate}%)", "cart.total_with_vat": "Total (with VAT)", "cart.checkout.buttonLabel": "Checkout", "cart.viewCart.buttonLabel": "View Cart", "cart.itemList.empty": "Cart is empty", "cart.goToCart.buttonLabel": "Go to cart", "cart.heading.orderDetails": "Order Details", "cart.empty.buttonLabel": "Continue shopping", "cart.empty.title": "Your cart seems empty", "cart.empty.message": "Add items to your cart.", "cart.shipping.localOnly": "Available for shipping to Spain only.", "cart.shipping.regionUnavailable": "Not available for shipping to your region", "basket.timer.normal": "Items reserved", "basket.timer.warning": "Reservation expires soon", "basket.timer.critical": "Reservation expires very soon!", "basket.timer.expired": "Your reservation has expired. Please refresh and try again.", "basket.timer.warningMessage": "Your reservation expires in less than 1 minute.", "basket.timer.criticalMessage": "Your reservation expires in less than 30 seconds!", "discount.automatic.applied": "Automatic Discount Applied", "discount.automatic.shipping": "Free Shipping", "discount.automatic.shippingDescription": "Automatic shipping discount applied to your order", "discount.automatic.percentageOff": "{percentage}% off", "discount.automatic.amountOff": "{amount} off", "payment.recovery.discountMayHaveExpired": "Your payment took longer than expected. Your discount may have expired, but we'll do our best to honor it.", "collectionPage.relatedProducts.title": "Shop Collections", "collectionPage.itemTag.draft": "Draft item", "productPage.addToCart.buttonLabel": "Add to cart", "productPage.addToCart.loading": "Adding...", "productPage.selectVariant.buttonLabel": "Select variant", "productPage.banner.draft": "Page Preview", "productPage.banner.draft.publicIn": "Public ", "productPage.collection.label": "Collection", "productPage.color.label": "Color", "productPage.size.label": "Size", "productPage.productDetails.productDetailsLabel": "Details", "productPage.productDetails.productGuidesLabel": "Guides", "homePage.relatedProducts.title": "Shop Collections", "orderDetailsPage.back": "Back", "errorPage.head.title": "Page Not Found", "errorPage.message.404": "The page you requested does not exist.", "errorPage.title": "Error", "errorPage.message": "An error has occurred", "errorPage.returnLink": "Click <PERSON>ER<PERSON> to go back to the shop.", "form.email": "Email", "form.password": "Password", "form.passwordConfirm": "Confirm password", "form.passwordOld": "Old password", "form.passwordNew": "New password", "form.passwordNewConfirm": "Confirm new password", "form.firstName": "First Name", "form.lastName": "Last Name", "form.address": "Address", "form.address2": "Address 2", "form.postal": "Postal", "form.city": "City", "form.country": "Country", "form.state": "State", "form.phone": "Phone", "form.rfc": "RFC (Tax ID)", "form.rfcHelp": "Mexican tax identification number", "form.submit": "Submit", "form.correctErrors": "Correct errors", "form.selectPlaceholder": "Select option", "form.terms.label": "I have read and accept the {0} and {1}", "form.terms.label.terms": "Terms and Conditions", "form.terms.label.privacy": "Privacy Policy", "form.discountCode": "Discount Code", "form.discountCodeNotice": "Discount applied successfully", "checkout.head.title": "Checkout", "checkout.heading.contactEmail": "Contact Email", "checkout.heading.deliveryAddress": "Delivery address", "checkout.heading.billingAddress": "Billing address", "checkout.heading.deliveryOptions": "Delivery options", "checkout.heading.additionalInfo": "Additional Info", "checkout.heading.cartDetails": "Cart details", "checkout.checkbox.saveAddress": "Save address as default", "checkout.checkbox.billingAddress": "Bill to different address", "checkout.success.head.title": "Thank you!", "checkout.success.paidOn": "Paid on:", "checkout.failed.head.title": "Checkout Failed", "checkout.failed.title": "Payment unsuccessful!", "checkout.failed.message": "Payment not complete!<br>Please try again.", "checkout.failed.buttonLabel": "Go to Cart", "image.missingAlt": "Image missing", "validate.required": "This field is required", "validate.required.checkbox": "Marking the checkbox is required", "validate.password": "Password must be at least 8 characters", "validate.email": "Enter a valid Email address", "validate.min": "Password too short", "validate.max": "Password too long", "validate.shippingMethods": "Select a shipping method", "validate.confirmPassword": "Passwords are not matching", "validate.noHashtag": "Hashtags are not allowed", "validate.maxLength": "Maximum of {length} characters", "validate.minLength": "Minimum of {length} characters", "validate.rfc": "Enter a valid RFC (12-13 alphanumeric characters)", "generic.noResults": "No results", "generic.notAvailable": "Not available", "generic.loadMore": "Load more", "generic.searchResults": "Found {count} results", "notice.delivery": "Delivery estimated in 1-5 business days.", "checkout.welcome": "Hi, ", "checkout.submit": "Proceed to Payment", "checkout.submit.help": "You will be redirected to a page where you can complete your purchase safely.", "checkout.errorRedirecting": "Error while trying to redirect to payment", "checkout.checkbox.bizum": "Pay faster with", "notifications.copied": "Copied {code} to clipboard!"}