<template>
  <div id="__error">
    <div class="errorPage__container">
      <div class="errorPage__header">
        <div v-if="error.statusCode === 404">
          <h1 class="errorPage__header-title">404</h1>
          <p>
            {{ $t('errorPage.message.404') }}
            <NLink :to="localePath('index')">
              {{ $t('errorPage.returnLink') }}
            </NLink>
          </p>
        </div>
        <div v-else-if="error.statusCode == 429">
          <h1 class="errorPage__header-title">
            {{ error.statusCode }} - {{ error.message }}
          </h1>
          <p>
            <span v-if="error.data && error.data.detail">
              {{ error.data.detail }}
            </span>
            <span v-else>You are going too fast. Please slow down.</span>
          </p>
        </div>
        <div v-else>
          <h1 class="errorPage__header-title">
            {{ error.statusCode }} - {{ error.message }}
          </h1>
          <p>
            {{ $t('errorPage.message') }}
            <NLink :to="localePath('index')">
              {{ $t('errorPage.returnLink') }}
            </NLink>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'blank',
  props: {
    error: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      title:
        'Error ' +
        this.error.statusCode +
        ' - ' +
        this.$t('errorPage.head.title'),
    }
  },

  head() {
    return {
      title: this.title,
    }
  },
}
</script>

<style lang="scss" scoped>
.errorPage__container {
  margin-left: auto;
  margin-right: auto;

  @include grid('laptop', $down: true) {
    padding-left: em(26);
    padding-right: em(26);
  }

  @include grid('laptop') {
    padding-left: em(54);
    padding-right: em(54);
    max-width: 83.3333%;
  }

  @include grid('desktop-large') {
    padding-left: em(96);
    padding-right: em(96);
  }
}
.errorPage__header {
  @include grid('laptop', $down: true) {
    margin-top: em(130);
    margin-bottom: em(42);
  }

  @include grid('laptop') {
    margin-top: em(170);
    margin-bottom: em(48);
  }
}

.errorPage__header-title {
  font-size: em(40);
  font-weight: var(--text-bold);
  letter-spacing: -0.02em;
}
</style>
