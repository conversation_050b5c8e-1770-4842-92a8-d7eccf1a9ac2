<template>
  <div id="__default">
    <GlobalLoader />
    <LayoutHeader />
    <NotificationTicker />
    <LayoutBody>
      <Nuxt :nuxt-child-key="key" />
    </LayoutBody>
    <LayoutFooter v-if="!isHomepage3dView" />
    <LayoutCookie />
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  head() {
    return this.$nuxtI18nHead()
  },
  computed: {
    ...mapState('page', ['demoTokenIsEnabled', 'pageType']),
    ...mapState('ui', ['homepageView']),

    key() {
      let key = 'default'
      if (this.$store.state.page.page) {
        key = this.$store.state.page.page.url_path
      }
      return key
    },
    isHomepage3dView() {
      return this.pageType === 'home.HomePage' && this.homepageView === '3d'
    },
  },
  watch: {
    // test comment
    // Refresh page and basket when currency changes
    '$store.state.currency.currency'(value) {
      this.$store.dispatch('basket/refreshBasket')
      this.$store.dispatch('page/refreshPage')
    },

    '$route.path'() {
      if (this.demoTokenIsEnabled) {
        if (
          this.$route.path.startsWith('/es/catalogo') ||
          this.$route.path.startsWith('/es/colecciones') ||
          this.$route.path.startsWith('/es/collections') ||
          this.$route.path.startsWith('/es/tienda') ||
          this.$route.path.startsWith('/en/catalogo') ||
          this.$route.path.startsWith('/en/collections') ||
          this.$route.path.startsWith('/en/tienda') ||
          this.$route.path.startsWith('/en/collections') ||
          this.$route.path.startsWith('/en/shop') ||
          this.$route.path.startsWith('/es/shop') ||
          this.$route.path === '/es/' ||
          this.$route.path === '/en/'
        ) {
          this.$router.push('/demo/')
        }
      }
    },
  },
  mounted() {
    this.$store.dispatch('ui/initializeHomepageView')
    this.defineCoefficientCssVariableForVhOnMobileDevices()
  },
  methods: {
    defineCoefficientCssVariableForVhOnMobileDevices() {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)

      window.addEventListener('resize', () => {
        const vh = window.innerHeight * 0.01
        document.documentElement.style.setProperty('--vh', `${vh}px`)
      })
    },
  },
}
</script>

<style lang="scss">
$translateUnit: em(8);

#__default {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-enter-active,
.page-leave-active {
  transition: opacity calc(var(--speed) * 0.5) var(--easing),
    transform calc(var(--speed)) var(--easing);
  transform-origin: top;
}
.page-enter,
.page-leave-to {
  transform: translateY(-$translateUnit);
  opacity: 0;
}
</style>
