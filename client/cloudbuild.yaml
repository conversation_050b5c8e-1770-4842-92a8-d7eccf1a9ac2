steps:
  - id: Build
    name: gcr.io/cloud-builders/docker
    args:
      - 'build'
      - '-t'
      - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'
      - '.'
    dir: client

  - id: Push
    name: gcr.io/cloud-builders/docker
    args:
      - 'push'
      - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'

  - id: Deploy
    name: gcr.io/google.com/cloudsdktool/cloud-sdk
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '$_SERVICE_NAME'
      - '--platform=managed'
      - '--region=$_REGION'
      - '--image=$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'
      - '--min-instances=$_MIN_INSTANCES'
      - '--max-instances=$_MAX_INSTANCES'
      - '--timeout=$_TIMEOUT'
      - '--concurrency=$_CONCURRENCY'
      - '--allow-unauthenticated'
      - '--set-env-vars'
      - 'API_URL=$_API_URL'
      - '--set-env-vars'
      - 'DEMO_TOKEN=$_DEMO_TOKEN'

  - id: Update Traffic
    name: gcr.io/google.com/cloudsdktool/cloud-sdk
    entrypoint: gcloud
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '$_SERVICE_NAME'
      - '--region=$_REGION'
      - '--to-latest'

images:
  - '$_GCR_HOSTNAME/$PROJECT_ID/$_AR_REPOSITORY/$_IMAGE_NAME:$COMMIT_SHA'

options:
  substitutionOption: ALLOW_LOOSE
  pool:
    name: projects/$PROJECT_ID/locations/europe-west4/workerPools/leeosmerch
  logging: CLOUD_LOGGING_ONLY

substitutions:
  _SERVICE_NAME: leeos-merch-client
  _IMAGE_NAME: leeos-merch-client
  _REGION: 'europe-west4'
  _GCR_HOSTNAME: europe-docker.pkg.dev
  _AR_REPOSITORY: eu.gcr.io
  _MIN_INSTANCES: '3'
  _MAX_INSTANCES: '1000'
  _TIMEOUT: '30'
  _CONCURRENCY: '80'
  # Container environment vars
  _API_URL: 'https://cms.madkat.store'
  _DEMO_TOKEN: ''

tags:
  - leeos-merch

timeout: 900s
