# RecursionError: maximum recursion depth exceeded while calling a Python object

**Issue ID:** 54209040
**Project:** madkat-api
**Date:** 20/08/2025, 15:45:36

## Tags

- **browser:** Chrome 138
- **browser.name:** Chrome
- **client_os:** macOS
- **client_os.name:** macOS
- **device:** Mac
- **device.family:** Mac
- **environment:** production
- **handled:** no
- **level:** error
- **mechanism:** django
- **runtime:** CPython 3.9.23
- **runtime.name:** CPython
- **server_name:** localhost
- **transaction:** /admin/users/{user_id}/delete/
- **url:** http://cms.madkat.store/admin/users/13487/delete/
- **user:** id:7

## Exception

### Exception 1
**Type:** RecursionError
**Value:** maximum recursion depth exceeded while calling a Python object

#### Stacktrace

```
 __hash__ in django/db/models/fields/__init__.py [Line 546] (Not in app)
        return NotImplemented

    def __hash__(self):
        return hash((
            self.creation_counter,
            self.model._meta.app_label if hasattr(self, 'model') else None,  <-- SUSPECT LINE
            self.model._meta.model_name if hasattr(self, 'model') else None,
        ))

    def __deepcopy__(self, memodict):
        # We don't have to deepcopy very much here, since most things are not
---
Variable values:
{
  "self": "<salesman.core.models.JSONField: _extra>"
}

=======
 add_to_dict in django/db/models/sql/query.py [Line 2423] (Not in app)
    exists.
    """
    if key in data:
        data[key].add(value)
    else:
        data[key] = {value}  <-- SUSPECT LINE


def is_reverse_o2o(field):
    """
    Check if the given field is reverse-o2o. The field is expected to be some
---
Variable values:
{
  "data": {},
  "key": "<class 'salesman.orders.models.Order'>",
  "value": "<salesman.core.models.JSONField: _extra>"
}

=======
 deferred_to_data in django/db/models/sql/query.py [Line 729] (Not in app)
            model = field.related_model if is_reverse_object else field.model
            model = model._meta.concrete_model
            if model == opts.model:
                model = cur_model
            if not is_reverse_o2o(field):
                add_to_dict(seen, model, field)  <-- SUSPECT LINE

        if defer:
            # We need to load all fields for each model, except those that
            # appear in "seen" (for all models that appear in "seen"). The only
            # slight complexity here is handling fields that exist on parent
---
Variable values:
{
  "callback": "<bound method Query.get_loaded_field_names_cb of <django.db.models.sql.query.Query object at 0x7f0ccf295910>>",
  "defer": "False",
  "field_name": "'_extra'",
  "field_names": [
    "'_extra'"
  ],
  "must_include": {
    "<class 'salesman.orders.models.Order'>": [
      "<django.db.models.fields.AutoField: id>"
    ]
  },
  "orig_opts": "<Options for Order>",
  "parts": [
    "'_extra'"
  ],
  "seen": {},
  "self": "<django.db.models.sql.query.Query object at 0x7f0ccf295910>",
  "target": {}
}

=======
 deferred_to_columns in django/db/models/sql/compiler.py [Line 1102] (Not in app)
        Convert the self.deferred_loading data structure to mapping of table
        names to sets of column names which are to be loaded. Return the
        dictionary.
        """
        columns = {}
        self.query.deferred_to_data(columns, self.query.get_loaded_field_names_cb)  <-- SUSPECT LINE
        return columns

    def get_converters(self, expressions):
        converters = {}
        for i, expression in enumerate(expressions):
---
Variable values:
{
  "columns": {},
  "self": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>"
}

=======
 get_default_columns in django/db/models/sql/compiler.py [Line 677] (Not in app)
        of strings as the first component and None as the second component).
        """
        result = []
        if opts is None:
            opts = self.query.get_meta()
        only_load = self.deferred_to_columns()  <-- SUSPECT LINE
        start_alias = start_alias or self.query.get_initial_alias()
        # The 'seen_models' is used to optimize checking the needed parent
        # alias for a given field. This also includes None -> start_alias to
        # be used by local fields.
        seen_models = {None: start_alias}
---
Variable values:
{
  "from_parent": "None",
  "opts": "<Options for Order>",
  "result": [],
  "self": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>",
  "start_alias": "None"
}

=======
 get_select in django/db/models/sql/compiler.py [Line 227] (Not in app)
            annotations[alias] = select_idx
            select.append((RawSQL(sql, params), alias))
            select_idx += 1
        assert not (self.query.select and self.query.default_cols)
        if self.query.default_cols:
            cols = self.get_default_columns()  <-- SUSPECT LINE
        else:
            # self.query.select is a special case. These columns never go to
            # any model.
            cols = self.query.select
        if cols:
---
Variable values:
{
  "annotations": {},
  "klass_info": "None",
  "select": [],
  "select_idx": "0",
  "self": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>"
}

=======
 setup_query in django/db/models/sql/compiler.py [Line 46] (Not in app)
        self._meta_ordering = None

    def setup_query(self):
        if all(self.query.alias_refcount[a] == 0 for a in self.query.alias_map):
            self.query.get_initial_alias()
        self.select, self.klass_info, self.annotation_col_map = self.get_select()  <-- SUSPECT LINE
        self.col_count = len(self.select)

    def pre_sql_setup(self):
        """
        Do any necessary class setup immediately prior to producing SQL. This
---
Variable values:
{
  "self": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>"
}

=======
 pre_sql_setup in django/db/models/sql/compiler.py [Line 55] (Not in app)
        """
        Do any necessary class setup immediately prior to producing SQL. This
        is for things that can't necessarily be done in __init__ because we
        might not have all the pieces in place at that time.
        """
        self.setup_query()  <-- SUSPECT LINE
        order_by = self.get_order_by()
        self.where, self.having = self.query.where.split_having()
        extra_select = self.get_extra_select(order_by, self.select)
        self.has_extra_select = bool(extra_select)
        group_by = self.get_group_by(self.select + extra_select, order_by)
---
Variable values:
{
  "self": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>"
}

=======
 as_sql in django/db/models/sql/compiler.py [Line 513] (Not in app)
        If 'with_limits' is False, any limit/offset information is not included
        in the query.
        """
        refcounts_before = self.query.alias_refcount.copy()
        try:
            extra_select, order_by, group_by = self.pre_sql_setup()  <-- SUSPECT LINE
            for_update_part = None
            # Is a LIMIT/OFFSET clause needed?
            with_limit_offset = with_limits and (self.query.high_mark is not None or self.query.low_mark)
            combinator = self.query.combinator
            features = self.connection.features
---
Variable values:
{
  "refcounts_before": {
    "salesmanorders_order": "1"
  },
  "self": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>",
  "with_col_aliases": "False",
  "with_limits": "True"
}

=======
 execute_sql in django/db/models/sql/compiler.py [Line 1162] (Not in app)
        is needed, as the filters describe an empty set. In that case, None is
        returned, to avoid any unnecessary database interaction.
        """
        result_type = result_type or NO_RESULTS
        try:
            sql, params = self.as_sql()  <-- SUSPECT LINE
            if not sql:
                raise EmptyResultSet
        except EmptyResultSet:
            if result_type == MULTI:
                return iter([])
---
Variable values:
{
  "chunk_size": "100",
  "chunked_fetch": "False",
  "result_type": "'multi'",
  "self": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>"
}

=======
 __iter__ in django/db/models/query.py [Line 51] (Not in app)
        queryset = self.queryset
        db = queryset.db
        compiler = queryset.query.get_compiler(using=db)
        # Execute the query. This will also fill compiler.select, klass_info,
        # and annotations.
        results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)  <-- SUSPECT LINE
        select, klass_info, annotation_col_map = (compiler.select, compiler.klass_info,
                                                  compiler.annotation_col_map)
        model_cls = klass_info['model']
        select_fields = klass_info['select_fields']
        model_fields_start, model_fields_end = select_fields[0], select_fields[-1] + 1
---
Variable values:
{
  "compiler": "<django.db.models.sql.compiler.SQLCompiler object at 0x7f0ccf295700>",
  "db": "'default'",
  "queryset": "<QuerySet from django.db.models.query at 0x7f0ccf2959d0>",
  "self": "<django.db.models.query.ModelIterable object at 0x7f0ccf295880>"
}

=======
 _fetch_all in django/db/models/query.py [Line 1324] (Not in app)
        c._fields = self._fields
        return c

    def _fetch_all(self):
        if self._result_cache is None:
            self._result_cache = list(self._iterable_class(self))  <-- SUSPECT LINE
        if self._prefetch_related_lookups and not self._prefetch_done:
            self._prefetch_related_objects()

    def _next_is_sticky(self):
        """
---
Variable values:
{
  "self": "<QuerySet from django.db.models.query at 0x7f0ccf2959d0>"
}

=======
 __len__ in django/db/models/query.py [Line 262] (Not in app)
        if len(data) > REPR_OUTPUT_SIZE:
            data[-1] = "...(remaining elements truncated)..."
        return '<%s %r>' % (self.__class__.__name__, data)

    def __len__(self):
        self._fetch_all()  <-- SUSPECT LINE
        return len(self._result_cache)

    def __iter__(self):
        """
        The queryset iterator protocol uses three nested iterators in the
---
Variable values:
{
  "self": "<QuerySet from django.db.models.query at 0x7f0ccf2959d0>"
}

=======
 get in django/db/models/query.py [Line 431] (Not in app)
            clone = clone.order_by()
        limit = None
        if not clone.query.select_for_update or connections[clone.db].features.supports_select_for_update_with_limit:
            limit = MAX_GET_RESULTS
            clone.query.set_limits(high=limit)
        num = len(clone)  <-- SUSPECT LINE
        if num == 1:
            return clone._result_cache[0]
        if not num:
            raise self.model.DoesNotExist(
                "%s matching query does not exist." %
---
Variable values:
{
  "args": [],
  "clone": "<QuerySet from django.db.models.query at 0x7f0ccf2959d0>",
  "kwargs": {},
  "limit": "21",
  "self": "<QuerySet from django.db.models.query at 0x7f0ccf295820>"
}

=======
 refresh_from_db in django/db/models/base.py [Line 650] (Not in app)
        elif deferred_fields:
            fields = [f.attname for f in self._meta.concrete_fields
                      if f.attname not in deferred_fields]
            db_instance_qs = db_instance_qs.only(*fields)

        db_instance = db_instance_qs.get()  <-- SUSPECT LINE
        non_loaded_fields = db_instance.get_deferred_fields()
        for field in self._meta.concrete_fields:
            if field.attname in non_loaded_fields:
                # This field wasn't refreshed - skip ahead.
                continue
---
Variable values:
{
  "db_instance_qs": "<QuerySet from django.db.models.query at 0x7f0ccf295820>",
  "deferred_fields": [
    "'email'",
    "'total'",
    "'subtotal'",
    "'date_created'",
    "[Filtered]",
    "'_extra'",
    "'billing_address'",
    "'ref'",
    "'date_updated'",
    "'shipping_address'"
  ],
  "field": "'_extra'",
  "fields": [
    "'_extra'"
  ],
  "hints": {
    "instance": "<broken repr>"
  },
  "prefetched_objects_cache": [],
  "self": "<broken repr>",
  "using": "None"
}

=======
 __get__ in django/db/models/query_utils.py [Line 144] (Not in app)
        if field_name not in data:
            # Let's see if the field is part of the parent chain. If so we
            # might be able to reuse the already loaded value. Refs #18343.
            val = self._check_parent_chain(instance)
            if val is None:
                instance.refresh_from_db(fields=[field_name])  <-- SUSPECT LINE
            else:
                data[field_name] = val
        return data[field_name]

    def _check_parent_chain(self, instance):
---
Variable values:
{
  "cls": "<class 'salesman.orders.models.Order'>",
  "data": {
    "_state": "<django.db.models.base.ModelState object at 0x7f0ccf295790>",
    "id": "921641",
    "status": "'COMPLETED'"
  },
  "field_name": "'_extra'",
  "instance": "<broken repr>",
  "self": "<django.db.models.query_utils.DeferredAttribute object at 0x7f0cfc409610>",
  "val": "None"
}

=======
```
